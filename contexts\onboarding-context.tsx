"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import { useRouter } from "next/navigation"

export type OnboardingStep =
  | "welcome"
  | "institution-details"
  | "admin-profile"
  | "academic-setup"
  | "invite-users"
  | "complete"

interface OnboardingData {
  // Institution details
  institutionName: string
  institutionType: string
  address: string
  city: string
  country: string
  logo?: File

  // Admin profile
  firstName: string
  lastName: string
  email: string
  phone: string

  // Academic setup
  academicYear: string
  terms: number
  gradeSystem: string

  // Invite users
  invitedUsers: {
    email: string
    role: string
    name: string
  }[]
}

type OnboardingContextType = {
  currentStep: OnboardingStep
  onboardingData: OnboardingData
  progress: number
  setCurrentStep: (step: OnboardingStep) => void
  updateOnboardingData: (data: Partial<OnboardingData>) => void
  nextStep: () => void
  prevStep: () => void
  completeOnboarding: () => void
  isOnboardingComplete: boolean
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined)

const STEPS: OnboardingStep[] = [
  "welcome",
  "institution-details",
  "admin-profile",
  "academic-setup",
  "invite-users",
  "complete",
]

const initialOnboardingData: OnboardingData = {
  institutionName: "",
  institutionType: "",
  address: "",
  city: "",
  country: "Zambia",
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  academicYear: new Date().getFullYear().toString(),
  terms: 3,
  gradeSystem: "percentage",
  invitedUsers: [],
}

export const OnboardingProvider = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState<OnboardingStep>("welcome")
  const [onboardingData, setOnboardingData] = useState<OnboardingData>(initialOnboardingData)
  const [isOnboardingComplete, setIsOnboardingComplete] = useState(false)

  // Calculate progress percentage
  const currentStepIndex = STEPS.indexOf(currentStep)
  const progress = Math.round((currentStepIndex / (STEPS.length - 1)) * 100)

  // Load onboarding state from localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedStep = localStorage.getItem("onboardingStep")
      const savedData = localStorage.getItem("onboardingData")
      const completed = localStorage.getItem("onboardingComplete")

      if (savedStep) {
        setCurrentStep(savedStep as OnboardingStep)
      }

      if (savedData) {
        try {
          setOnboardingData(JSON.parse(savedData))
        } catch (error) {
          console.error("Failed to parse onboarding data:", error)
        }
      }

      if (completed === "true") {
        setIsOnboardingComplete(true)
      }
    }
  }, [])

  // Save onboarding state to localStorage when it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("onboardingStep", currentStep)
      localStorage.setItem("onboardingData", JSON.stringify(onboardingData))
      localStorage.setItem("onboardingComplete", isOnboardingComplete.toString())
    }
  }, [currentStep, onboardingData, isOnboardingComplete])

  const updateOnboardingData = (data: Partial<OnboardingData>) => {
    setOnboardingData((prev) => ({ ...prev, ...data }))
  }

  const nextStep = () => {
    const currentIndex = STEPS.indexOf(currentStep)
    if (currentIndex < STEPS.length - 1) {
      setCurrentStep(STEPS[currentIndex + 1])
    }
  }

  const prevStep = () => {
    const currentIndex = STEPS.indexOf(currentStep)
    if (currentIndex > 0) {
      setCurrentStep(STEPS[currentIndex - 1])
    }
  }

  const completeOnboarding = () => {
    setIsOnboardingComplete(true)
    // In a real app, we would send the data to the server here
    router.push("/dashboard")
  }

  const value = {
    currentStep,
    onboardingData,
    progress,
    setCurrentStep,
    updateOnboardingData,
    nextStep,
    prevStep,
    completeOnboarding,
    isOnboardingComplete,
  }

  return <OnboardingContext.Provider value={value}>{children}</OnboardingContext.Provider>
}

export const useOnboarding = () => {
  const context = useContext(OnboardingContext)
  if (context === undefined) {
    throw new Error("useOnboarding must be used within an OnboardingProvider")
  }
  return context
}
