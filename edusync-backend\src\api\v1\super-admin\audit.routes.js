const express = require('express');
const router = express.Router();
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const {
  getAuditLogs,
  getAuditStats,
  createAuditLog,
  exportAuditLogs,
  getAuditLogById
} = require('./audit.controller');

// Protect all routes and restrict to SUPER_ADMIN
router.use(authenticate);
router.use(restrictTo('SUPER_ADMIN'));

// Audit log routes
router.get('/', getAuditLogs);
router.get('/stats', getAuditStats);
router.get('/export', exportAuditLogs);
router.get('/:id', getAuditLogById);
router.post('/', createAuditLog);

module.exports = router;
