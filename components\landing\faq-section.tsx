"use client"

import { useState } from "react"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Button } from "@/components/ui/button"
import Link from "next/link"

export function FaqSection() {
  const [activeCategory, setActiveCategory] = useState("general")

  const categories = [
    { id: "general", name: "General" },
    { id: "pricing", name: "Pricing" },
    { id: "features", name: "Features" },
    { id: "technical", name: "Technical" },
  ]

  const faqs = {
    general: [
      {
        question: "What is Edusync?",
        answer:
          "Edusync is a comprehensive school management system designed specifically for Zambian schools. It helps educational institutions streamline their administrative processes, enhance learning experiences, and improve communication between teachers, students, and parents.",
      },
      {
        question: "How can Edusync benefit my school?",
        answer:
          "Edusync can help reduce administrative workload, improve data accuracy, enhance communication, provide valuable insights through analytics, and create a better learning environment for students. It centralizes all school management functions in one easy-to-use platform.",
      },
      {
        question: "Is Edusync suitable for all types of schools?",
        answer:
          "Yes, Edusync is designed to be flexible and can be used by primary schools, secondary schools, colleges, and other educational institutions of various sizes across Zambia.",
      },
      {
        question: "How long does it take to implement Edusync?",
        answer:
          "The implementation time varies depending on the size of your institution and the modules you choose to implement. Typically, basic setup can be completed within 1-2 weeks, with full implementation taking 4-6 weeks.",
      },
    ],
    pricing: [
      {
        question: "How much does Edusync cost?",
        answer:
          "Edusync offers flexible pricing plans starting from K500 per month. The cost depends on the number of students, staff, and the features you need. Please visit our pricing page for detailed information.",
      },
      {
        question: "Is there a free trial available?",
        answer:
          "Yes, we offer a 14-day free trial that gives you access to all features of our Standard plan. No credit card is required to start your trial.",
      },
      {
        question: "Can I upgrade or downgrade my plan?",
        answer:
          "Yes, you can upgrade or downgrade your plan at any time. Changes to your subscription will be reflected in your next billing cycle.",
      },
      {
        question: "Are there any hidden fees?",
        answer:
          "No, there are no hidden fees. All costs are transparently displayed on our pricing page. You only pay for what you choose to use.",
      },
    ],
    features: [
      {
        question: "What features are included in Edusync?",
        answer:
          "Edusync includes student management, attendance tracking, grade management, fee collection, timetable scheduling, communication tools, performance analytics, and much more. The specific features available depend on your chosen plan.",
      },
      {
        question: "Can parents access the system?",
        answer:
          "Yes, parents can access a dedicated portal where they can view their children's attendance, grades, fee statements, and communicate with teachers.",
      },
      {
        question: "Does Edusync support online learning?",
        answer:
          "Yes, our platform includes features to support online and hybrid learning environments, including assignment submission, resource sharing, and virtual classroom integration.",
      },
      {
        question: "Can I generate reports with Edusync?",
        answer:
          "Yes, Edusync offers comprehensive reporting capabilities, allowing you to generate various reports on student performance, attendance, finances, and more.",
      },
    ],
    technical: [
      {
        question: "Is my data secure with Edusync?",
        answer:
          "Yes, we take data security very seriously. We use industry-standard encryption, regular backups, and strict access controls to ensure your data is safe and secure.",
      },
      {
        question: "Can Edusync be accessed on mobile devices?",
        answer:
          "Yes, Edusync is fully responsive and can be accessed on smartphones and tablets, making it convenient for teachers, administrators, and parents to use on the go.",
      },
      {
        question: "What kind of support do you offer?",
        answer:
          "We offer email support for all plans, with priority support and dedicated account managers available for higher-tier plans. We also provide comprehensive documentation and video tutorials.",
      },
      {
        question: "Can Edusync be integrated with other systems?",
        answer:
          "Yes, Edusync offers API access for integration with other systems such as accounting software, learning management systems, and more. Custom integrations are available for Premium plan subscribers.",
      },
    ],
  }

  return (
    <section id="faq" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900">Frequently Asked Questions</h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Find answers to common questions about our school management system
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={activeCategory === category.id ? "default" : "outline"}
                onClick={() => setActiveCategory(category.id)}
                className={activeCategory === category.id ? "bg-emerald-600 hover:bg-emerald-700" : ""}
              >
                {category.name}
              </Button>
            ))}
          </div>

          <Accordion type="single" collapsible className="bg-white rounded-xl shadow-md">
            {faqs[activeCategory as keyof typeof faqs].map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="px-6 text-left hover:text-emerald-600">{faq.question}</AccordionTrigger>
                <AccordionContent className="px-6 pb-4">
                  <p className="text-gray-600">{faq.answer}</p>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>

          <div className="mt-8 text-center">
            <p className="text-gray-600 mb-4">Still have questions?</p>
            <Link href="/contact">
              <Button variant="outline">Contact Our Support Team</Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
