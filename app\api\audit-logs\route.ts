import { NextRequest, NextResponse } from 'next/server'

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  try {
    const originalEndpoint = request.headers.get('X-Original-Endpoint') || ''
    const queryParams = request.headers.get('X-Query-Params') || ''
    
    let backendUrl = `${BACKEND_URL}${originalEndpoint}`
    
    const params = new URLSearchParams(queryParams)
    if (params.toString()) {
      backendUrl += `?${params.toString()}`
    }

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: request.headers.get('Authorization') || '',
        Cookie: request.headers.get('Cookie') || '',
      },
    })

    const data = await response.json()
    
    return NextResponse.json(data, { status: response.status })
  } catch (error) {
    console.error('Audit Logs API proxy error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
