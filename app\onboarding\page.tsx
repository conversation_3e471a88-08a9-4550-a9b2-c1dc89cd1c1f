"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Progress } from "@/components/ui/progress"
import { useOnboarding } from "@/contexts/onboarding-context"
import { WelcomeStep } from "@/components/onboarding/welcome-step"
import { InstitutionDetailsStep } from "@/components/onboarding/institution-details-step"
import { AdminProfileStep } from "@/components/onboarding/admin-profile-step"
import { AcademicSetupStep } from "@/components/onboarding/academic-setup-step"
import { InviteUsersStep } from "@/components/onboarding/invite-users-step"
import { CompleteStep } from "@/components/onboarding/complete-step"

export default function OnboardingPage() {
  const router = useRouter()
  const { currentStep, progress, isOnboardingComplete } = useOnboarding()

  // Redirect to dashboard if onboarding is complete
  useEffect(() => {
    if (isOnboardingComplete) {
      router.push("/dashboard")
    }
  }, [isOnboardingComplete, router])

  return (
    <div className="container max-w-4xl py-8">
      <div className="mb-8 flex items-center justify-between">
        <Link href="/" className="text-2xl font-bold text-emerald-600">
          EduManage
        </Link>
        <Link href="/auth/login" className="text-sm text-muted-foreground hover:text-foreground">
          Already have an account? Log in
        </Link>
      </div>

      <div className="mb-8">
        <Progress value={progress} className="h-2" />
        <div className="mt-2 flex justify-between text-xs text-muted-foreground">
          <span>Getting Started</span>
          <span>{progress}% Complete</span>
        </div>
      </div>

      <div className="rounded-lg border bg-background shadow-sm">
        {currentStep === "welcome" && <WelcomeStep />}
        {currentStep === "institution-details" && <InstitutionDetailsStep />}
        {currentStep === "admin-profile" && <AdminProfileStep />}
        {currentStep === "academic-setup" && <AcademicSetupStep />}
        {currentStep === "invite-users" && <InviteUsersStep />}
        {currentStep === "complete" && <CompleteStep />}
      </div>
    </div>
  )
}
