const { PrismaClient } = require('@prisma/client');
const asyncHandler = require('../../../utils/asyncHandler');
const AppError = require('../../../utils/appError');
const { createAuditLog } = require('../../../services/auditService');

const prisma = new PrismaClient();

/**
 * Get institution settings
 */
const getInstitutionSettings = asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // Get institution with settings
  const institution = await prisma.institution.findUnique({
    where: { id },
    include: {
      settings: true
    }
  });

  if (!institution) {
    return res.status(404).json({
      success: false,
      message: 'Institution not found'
    });
  }

  // Check if user has access to this institution
  const userAccess = await prisma.institutionUser.findFirst({
    where: {
      userId: req.user.id,
      institutionId: id,
      isActive: true
    }
  });

  if (!userAccess && req.user.role !== 'SUPER_ADMIN') {
    return res.status(403).json({
      success: false,
      message: 'Access denied to this institution'
    });
  }

  // Structure the settings response
  const settings = {
    general: {
      name: institution.name,
      email: institution.email,
      phone: institution.phone,
      address: institution.address,
      website: institution.website,
      logo: institution.logo,
      description: institution.description
    },
    academic: {
      academicYear: institution.settings?.academicYear || '2023-2024',
      gradeSystem: institution.settings?.gradeSystem || 'percentage',
      attendanceTracking: institution.settings?.attendanceTracking || true,
      examSettings: institution.settings?.examSettings || {
        passingGrade: 50,
        maxRetakes: 2,
        enableOnlineExams: false
      }
    },
    notifications: {
      smsNotifications: institution.settings?.smsNotifications || false,
      emailNotifications: institution.settings?.emailNotifications || true,
      pushNotifications: institution.settings?.pushNotifications || true,
      notificationEvents: institution.settings?.notificationEvents || {
        attendance: { email: true, sms: false },
        grades: { email: true, sms: false },
        fees: { email: true, sms: true },
        announcements: { email: true, sms: false },
        parentMeetings: { email: true, sms: true }
      }
    },
    localization: {
      language: institution.settings?.language || 'english',
      timezone: institution.settings?.timezone || 'Africa/Lusaka',
      dateFormat: institution.settings?.dateFormat || 'dd/mm/yyyy',
      currency: institution.settings?.currency || 'ZMW',
      currencySymbol: institution.settings?.currencySymbol || 'K'
    },
    security: {
      twoFactorAuth: institution.settings?.twoFactorAuth || false,
      passwordPolicy: institution.settings?.passwordPolicy || {
        minLength: 8,
        requireSpecialChars: true,
        requireNumbers: true,
        requireUppercase: true
      },
      sessionTimeout: institution.settings?.sessionTimeout || 30,
      ipRestriction: institution.settings?.ipRestriction || false
    },
    features: {
      enabledModules: institution.settings?.enabledModules || [
        'students', 'teachers', 'classes', 'attendance', 'grades', 'fees'
      ],
      maxStudents: institution.maxStudents || 500,
      maxTeachers: institution.maxTeachers || 50,
      storageLimit: institution.storageLimit || 1000
    }
  };

  res.status(200).json({
    success: true,
    data: settings
  });
});

/**
 * Update institution settings
 */
const updateInstitutionSettings = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { category, settings: settingsData } = req.body;

  // Check if institution exists
  const institution = await prisma.institution.findUnique({
    where: { id },
    include: { settings: true }
  });

  if (!institution) {
    return res.status(404).json({
      success: false,
      message: 'Institution not found'
    });
  }

  // Check if user has access to this institution
  const userAccess = await prisma.institutionUser.findFirst({
    where: {
      userId: req.user.id,
      institutionId: id,
      isActive: true,
      role: { in: ['ADMIN', 'SUPER_ADMIN'] }
    }
  });

  if (!userAccess && req.user.role !== 'SUPER_ADMIN') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Admin privileges required.'
    });
  }

  let updatedInstitution;

  try {
    // Handle different categories of settings
    if (category === 'general') {
      updatedInstitution = await prisma.institution.update({
        where: { id },
        data: {
          name: settingsData.name || institution.name,
          email: settingsData.email || institution.email,
          phone: settingsData.phone || institution.phone,
          address: settingsData.address || institution.address,
          website: settingsData.website || institution.website,
          description: settingsData.description || institution.description,
          updatedAt: new Date()
        }
      });
    } else {
      // For other categories, update the settings JSON field
      const currentSettings = institution.settings || {};
      const newSettings = {
        ...currentSettings,
        [category]: {
          ...currentSettings[category],
          ...settingsData
        }
      };

      updatedInstitution = await prisma.institution.update({
        where: { id },
        data: {
          settings: newSettings,
          updatedAt: new Date()
        }
      });
    }

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'INSTITUTION_SETTINGS_UPDATED',
      details: `Updated ${category} settings for institution ${institution.name}`,
      resourceType: 'Institution',
      resourceId: id,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    res.status(200).json({
      success: true,
      message: `${category} settings updated successfully`,
      data: updatedInstitution
    });

  } catch (error) {
    console.error('Settings update error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update settings',
      error: error.message
    });
  }
});

/**
 * Upload institution logo
 */
const uploadInstitutionLogo = asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: 'No file uploaded'
    });
  }

  // Check if institution exists
  const institution = await prisma.institution.findUnique({
    where: { id }
  });

  if (!institution) {
    return res.status(404).json({
      success: false,
      message: 'Institution not found'
    });
  }

  // Check user access
  const userAccess = await prisma.institutionUser.findFirst({
    where: {
      userId: req.user.id,
      institutionId: id,
      isActive: true,
      role: { in: ['ADMIN', 'SUPER_ADMIN'] }
    }
  });

  if (!userAccess && req.user.role !== 'SUPER_ADMIN') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Admin privileges required.'
    });
  }

  // In production, you would upload to cloud storage (AWS S3, Google Cloud, etc.)
  const logoUrl = `/uploads/institutions/${req.file.filename}`;

  const updatedInstitution = await prisma.institution.update({
    where: { id },
    data: {
      logo: logoUrl,
      updatedAt: new Date()
    }
  });

  // Create audit log
  await createAuditLog({
    userId: req.user.id,
    action: 'INSTITUTION_LOGO_UPDATED',
    details: `Updated logo for institution ${institution.name}`,
    resourceType: 'Institution',
    resourceId: id,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  });

  res.status(200).json({
    success: true,
    message: 'Logo uploaded successfully',
    data: {
      logoUrl: logoUrl
    }
  });
});

/**
 * Get institution statistics for settings page
 */
const getInstitutionStats = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if institution exists and user has access
  const institution = await prisma.institution.findUnique({
    where: { id }
  });

  if (!institution) {
    return res.status(404).json({
      success: false,
      message: 'Institution not found'
    });
  }

  // Check user access
  const userAccess = await prisma.institutionUser.findFirst({
    where: {
      userId: req.user.id,
      institutionId: id,
      isActive: true
    }
  });

  if (!userAccess && req.user.role !== 'SUPER_ADMIN') {
    return res.status(403).json({
      success: false,
      message: 'Access denied to this institution'
    });
  }

  // Get statistics
  const [
    totalStudents,
    totalTeachers,
    totalStaff,
    totalClasses,
    storageUsed
  ] = await Promise.all([
    prisma.student.count({
      where: { 
        school: { institutionId: id }
      }
    }),
    prisma.teacher.count({
      where: { 
        school: { institutionId: id }
      }
    }),
    prisma.staff.count({
      where: { 
        school: { institutionId: id }
      }
    }),
    prisma.class.count({
      where: { 
        school: { institutionId: id }
      }
    }),
    // Mock storage calculation - in production, calculate actual usage
    Promise.resolve(Math.floor(Math.random() * 500) + 100)
  ]);

  const stats = {
    totalStudents,
    totalTeachers,
    totalStaff,
    totalClasses,
    storageUsed: `${storageUsed} MB`,
    storageLimit: `${institution.storageLimit || 1000} MB`,
    storagePercentage: Math.round((storageUsed / (institution.storageLimit || 1000)) * 100),
    subscriptionPlan: institution.subscriptionPlan || 'Basic',
    subscriptionStatus: institution.status || 'Active'
  };

  res.status(200).json({
    success: true,
    data: stats
  });
});

module.exports = {
  getInstitutionSettings,
  updateInstitutionSettings,
  uploadInstitutionLogo,
  getInstitutionStats
};
