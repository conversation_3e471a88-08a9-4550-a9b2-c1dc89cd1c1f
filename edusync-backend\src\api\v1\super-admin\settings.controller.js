const { PrismaClient } = require('@prisma/client');
const asyncHandler = require('../../../utils/asyncHandler');

const prisma = new PrismaClient();

/**
 * Get system settings
 */
const getSystemSettings = asyncHandler(async (req, res) => {
  const { category } = req.query;

  // Build filters
  const where = {};
  if (category) where.category = category;

  const settings = await prisma.systemSettings.findMany({
    where,
    orderBy: {
      category: 'asc'
    }
  });

  // Group settings by category
  const groupedSettings = settings.reduce((acc, setting) => {
    if (!acc[setting.category]) {
      acc[setting.category] = {};
    }
    acc[setting.category] = {
      ...acc[setting.category],
      ...setting.settings
    };
    return acc;
  }, {});

  res.status(200).json({
    success: true,
    data: category ? groupedSettings[category] : groupedSettings
  });
});

/**
 * Update system settings
 */
const updateSystemSettings = asyncHandler(async (req, res) => {
  const { category, settings: settingsData } = req.body;

  if (!category || !settingsData) {
    return res.status(400).json({
      success: false,
      message: 'Category and settings data are required'
    });
  }

  const settings = await prisma.systemSettings.upsert({
    where: { category },
    update: {
      settings: settingsData,
      updatedAt: new Date()
    },
    create: {
      category,
      settings: settingsData
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'System Settings Updated',
      details: `System settings updated for category: ${category}`,
      category: 'System',
      severity: 'Important',
      entityType: 'SystemSettings',
      entityId: settings.id,
      entityName: category,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'System settings updated successfully',
    data: settings.settings
  });
});

/**
 * Get application configuration
 */
const getAppConfig = asyncHandler(async (req, res) => {
  // Get application configuration from database
  const appConfig = await prisma.systemSettings.findFirst({
    where: { category: 'application' }
  });

  const defaultConfig = {
    appName: 'EduSync',
    appVersion: '1.0.0',
    maintenanceMode: false,
    allowRegistration: false,
    maxFileUploadSize: 10485760, // 10MB
    supportedFileTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'jpg', 'jpeg', 'png', 'gif'],
    sessionTimeout: 3600, // 1 hour
    defaultLanguage: 'en',
    timezone: 'UTC',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h'
  };

  res.status(200).json({
    success: true,
    data: appConfig ? { ...defaultConfig, ...appConfig.settings } : defaultConfig
  });
});

/**
 * Update application configuration
 */
const updateAppConfig = asyncHandler(async (req, res) => {
  const configData = req.body;

  const config = await prisma.systemSettings.upsert({
    where: { category: 'application' },
    update: {
      settings: configData,
      updatedAt: new Date()
    },
    create: {
      category: 'application',
      settings: configData
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Application Configuration Updated',
      details: 'Application configuration settings have been modified',
      category: 'System',
      severity: 'Important',
      entityType: 'SystemSettings',
      entityId: config.id,
      entityName: 'application',
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Application configuration updated successfully',
    data: config.settings
  });
});

/**
 * Get email settings
 */
const getEmailSettings = asyncHandler(async (req, res) => {
  const emailSettings = await prisma.systemSettings.findFirst({
    where: { category: 'email' }
  });

  const defaultSettings = {
    smtpHost: '',
    smtpPort: 587,
    smtpSecure: false,
    smtpUser: '',
    smtpPassword: '',
    fromEmail: '',
    fromName: 'EduSync',
    enableEmailNotifications: true,
    welcomeEmailTemplate: 'default',
    passwordResetTemplate: 'default',
    notificationTemplate: 'default'
  };

  // Mask sensitive information
  const settings = emailSettings ? { ...defaultSettings, ...emailSettings.settings } : defaultSettings;
  if (settings.smtpPassword) {
    settings.smtpPassword = '••••••••';
  }

  res.status(200).json({
    success: true,
    data: settings
  });
});

/**
 * Update email settings
 */
const updateEmailSettings = asyncHandler(async (req, res) => {
  const emailData = req.body;

  // Don't update password if it's masked
  if (emailData.smtpPassword === '••••••••') {
    delete emailData.smtpPassword;
  }

  const settings = await prisma.systemSettings.upsert({
    where: { category: 'email' },
    update: {
      settings: emailData,
      updatedAt: new Date()
    },
    create: {
      category: 'email',
      settings: emailData
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Email Settings Updated',
      details: 'Email configuration settings have been modified',
      category: 'System',
      severity: 'Important',
      entityType: 'SystemSettings',
      entityId: settings.id,
      entityName: 'email',
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Email settings updated successfully'
  });
});

/**
 * Test email configuration
 */
const testEmailConfig = asyncHandler(async (req, res) => {
  const { testEmail } = req.body;

  if (!testEmail) {
    return res.status(400).json({
      success: false,
      message: 'Test email address is required'
    });
  }

  try {
    // Mock email test - in real implementation, this would send a test email
    // using the configured SMTP settings
    
    // Simulate some processing time
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // For demo purposes, we'll simulate success
    const success = Math.random() > 0.2; // 80% success rate
    
    if (success) {
      // Create audit log
      await prisma.auditLog.create({
        data: {
          action: 'Email Configuration Test',
          details: `Email configuration test sent to ${testEmail}`,
          category: 'System',
          severity: 'Info',
          userId: req.user.id,
          userEmail: req.user.email,
          userAgent: req.get('User-Agent'),
          ip: req.ip || req.connection.remoteAddress,
          timestamp: new Date()
        }
      });

      res.status(200).json({
        success: true,
        message: 'Test email sent successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Failed to send test email. Please check your email configuration.'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error testing email configuration',
      error: error.message
    });
  }
});

/**
 * Get backup settings
 */
const getBackupSettings = asyncHandler(async (req, res) => {
  const backupSettings = await prisma.systemSettings.findFirst({
    where: { category: 'backup' }
  });

  const defaultSettings = {
    enableAutomaticBackups: true,
    backupFrequency: 'daily',
    backupTime: '02:00',
    retentionDays: 30,
    backupLocation: 'local',
    cloudProvider: '',
    cloudBucket: '',
    cloudRegion: '',
    cloudAccessKey: '',
    cloudSecretKey: '',
    includeFiles: true,
    includeDatabase: true,
    compressionEnabled: true,
    encryptionEnabled: false
  };

  // Mask sensitive information
  const settings = backupSettings ? { ...defaultSettings, ...backupSettings.settings } : defaultSettings;
  if (settings.cloudSecretKey) {
    settings.cloudSecretKey = '••••••••';
  }

  res.status(200).json({
    success: true,
    data: settings
  });
});

/**
 * Update backup settings
 */
const updateBackupSettings = asyncHandler(async (req, res) => {
  const backupData = req.body;

  // Don't update secret key if it's masked
  if (backupData.cloudSecretKey === '••••••••') {
    delete backupData.cloudSecretKey;
  }

  const settings = await prisma.systemSettings.upsert({
    where: { category: 'backup' },
    update: {
      settings: backupData,
      updatedAt: new Date()
    },
    create: {
      category: 'backup',
      settings: backupData
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Backup Settings Updated',
      details: 'Backup configuration settings have been modified',
      category: 'System',
      severity: 'Important',
      entityType: 'SystemSettings',
      entityId: settings.id,
      entityName: 'backup',
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Backup settings updated successfully'
  });
});

/**
 * Trigger manual backup
 */
const triggerManualBackup = asyncHandler(async (req, res) => {
  try {
    // Mock backup process - in real implementation, this would trigger actual backup
    const backupId = `backup_${Date.now()}`;
    
    // Simulate backup process
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'Manual Backup Triggered',
        details: `Manual backup initiated with ID: ${backupId}`,
        category: 'System',
        severity: 'Important',
        userId: req.user.id,
        userEmail: req.user.email,
        userAgent: req.get('User-Agent'),
        ip: req.ip || req.connection.remoteAddress,
        timestamp: new Date()
      }
    });

    res.status(200).json({
      success: true,
      message: 'Manual backup completed successfully',
      data: {
        backupId,
        timestamp: new Date(),
        size: '45.7 MB', // Mock size
        location: 'local'
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Backup process failed',
      error: error.message
    });
  }
});

/**
 * Get system information
 */
const getSystemInfo = asyncHandler(async (req, res) => {
  // Mock system information - in real implementation, get actual system stats
  const systemInfo = {
    serverInfo: {
      nodeVersion: process.version,
      platform: process.platform,
      architecture: process.arch,
      uptime: Math.floor(process.uptime()),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
      }
    },
    databaseInfo: {
      type: 'PostgreSQL',
      version: '14.5',
      size: '234 MB',
      connections: 15,
      maxConnections: 100
    },
    storageInfo: {
      totalSpace: '500 GB',
      usedSpace: '127 GB',
      freeSpace: '373 GB',
      usagePercentage: 25.4
    },
    cacheInfo: {
      type: 'Redis',
      status: 'connected',
      memory: '64 MB',
      keys: 1247
    }
  };

  res.status(200).json({
    success: true,
    data: systemInfo
  });
});

module.exports = {
  getSystemSettings,
  updateSystemSettings,
  getAppConfig,
  updateAppConfig,
  getEmailSettings,
  updateEmailSettings,
  testEmailConfig,
  getBackupSettings,
  updateBackupSettings,
  triggerManualBackup,
  getSystemInfo
};
