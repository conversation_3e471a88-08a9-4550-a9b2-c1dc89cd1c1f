const { PrismaClient } = require('@prisma/client');
const { logger } = require('../../../utils/logger');

const prisma = new PrismaClient();

/**
 * Get all students with filtering and pagination
 */
const getStudents = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      classId,
      status = 'ACTIVE',
      academicYearId,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Build where clause
    let where = {
      status: status
    };

    // Add search functionality
    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { admissionNumber: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ];
    }

    // Filter by class if provided
    if (classId) {
      where.studentEnrollments = {
        some: {
          classId: classId,
          isActive: true
        }
      };
    }

    // Filter by academic year if provided
    if (academicYearId) {
      where.studentEnrollments = {
        some: {
          academicYearId: academicYearId,
          isActive: true
        }
      };
    }

    // Get students with enrollments and related data
    const students = await prisma.student.findMany({
      where,
      skip,
      take,
      orderBy: {
        [sortBy]: sortOrder
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phoneNumber: true,
            profileImageUrl: true,
            isActive: true
          }
        },
        studentEnrollments: {
          where: { isActive: true },
          include: {
            class: {
              select: {
                id: true,
                name: true,
                gradeLevel: true,
                section: true
              }
            },
            academicYear: {
              select: {
                id: true,
                name: true,
                isActive: true
              }
            }
          }
        },
        parents: {
          include: {
            parent: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    email: true,
                    phoneNumber: true
                  }
                }
              }
            }
          }
        }
      }
    });

    // Get total count for pagination
    const totalStudents = await prisma.student.count({ where });

    // Format response data
    const formattedStudents = students.map(student => ({
      id: student.id,
      admissionNumber: student.admissionNumber,
      firstName: student.user.firstName,
      lastName: student.user.lastName,
      email: student.user.email,
      phoneNumber: student.user.phoneNumber,
      profileImageUrl: student.user.profileImageUrl,
      dateOfBirth: student.dateOfBirth,
      gender: student.gender,
      status: student.status,
      bloodGroup: student.bloodGroup,
      address: student.address,
      emergencyContact: student.emergencyContact,
      medicalInfo: student.medicalInfo,
      currentClass: student.studentEnrollments[0]?.class || null,
      academicYear: student.studentEnrollments[0]?.academicYear || null,
      parents: student.parents.map(sp => ({
        relationship: sp.relationship,
        isPrimary: sp.isPrimary,
        parent: {
          name: `${sp.parent.user.firstName} ${sp.parent.user.lastName}`,
          email: sp.parent.user.email,
          phoneNumber: sp.parent.user.phoneNumber
        }
      })),
      createdAt: student.createdAt,
      updatedAt: student.updatedAt
    }));

    res.status(200).json({
      success: true,
      data: {
        students: formattedStudents,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalStudents / take),
          totalStudents,
          limit: take
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching students:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch students',
      error: error.message
    });
  }
};

/**
 * Get student by ID
 */
const getStudentById = async (req, res) => {
  try {
    const { id } = req.params;

    const student = await prisma.student.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phoneNumber: true,
            profileImageUrl: true,
            isActive: true
          }
        },
        studentEnrollments: {
          include: {
            class: {
              include: {
                subjects: {
                  include: {
                    subject: true
                  }
                }
              }
            },
            academicYear: true
          }
        },
        parents: {
          include: {
            parent: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    email: true,
                    phoneNumber: true
                  }
                }
              }
            }
          }
        },
        attendanceRecords: {
          take: 10,
          orderBy: { date: 'desc' }
        },
        gradeRecords: {
          take: 10,
          orderBy: { recordedAt: 'desc' },
          include: {
            subject: true
          }
        },
        examResults: {
          take: 10,
          orderBy: { submittedAt: 'desc' },
          include: {
            exam: true,
            subject: true
          }
        }
      }
    });

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Format response
    const formattedStudent = {
      id: student.id,
      admissionNumber: student.admissionNumber,
      firstName: student.user.firstName,
      lastName: student.user.lastName,
      email: student.user.email,
      phoneNumber: student.user.phoneNumber,
      profileImageUrl: student.user.profileImageUrl,
      dateOfBirth: student.dateOfBirth,
      gender: student.gender,
      status: student.status,
      bloodGroup: student.bloodGroup,
      address: student.address,
      emergencyContact: student.emergencyContact,
      medicalInfo: student.medicalInfo,
      enrollments: student.studentEnrollments,
      parents: student.parents,
      recentAttendance: student.attendanceRecords,
      recentGrades: student.gradeRecords,
      recentExamResults: student.examResults,
      createdAt: student.createdAt,
      updatedAt: student.updatedAt
    };

    res.status(200).json({
      success: true,
      data: { student: formattedStudent }
    });

  } catch (error) {
    logger.error('Error fetching student:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student',
      error: error.message
    });
  }
};

/**
 * Create new student
 */
const createStudent = async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      phoneNumber,
      dateOfBirth,
      gender,
      admissionNumber,
      classId,
      academicYearId,
      bloodGroup,
      address,
      emergencyContact,
      medicalInfo,
      parents = []
    } = req.body;

    // Validate required fields
    if (!firstName || !lastName || !admissionNumber || !classId || !academicYearId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Check if admission number already exists
    const existingStudent = await prisma.student.findUnique({
      where: { admissionNumber }
    });

    if (existingStudent) {
      return res.status(400).json({
        success: false,
        message: 'Admission number already exists'
      });
    }

    // Create student in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create user first
      const user = await tx.user.create({
        data: {
          firstName,
          lastName,
          email: email || `${admissionNumber}@student.school.edu`,
          phoneNumber,
          role: 'STUDENT',
          isEmailVerified: false
        }
      });

      // Create student
      const student = await tx.student.create({
        data: {
          userId: user.id,
          admissionNumber,
          dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
          gender,
          bloodGroup,
          address,
          emergencyContact,
          medicalInfo,
          status: 'ACTIVE'
        }
      });

      // Create enrollment
      const enrollment = await tx.studentEnrollment.create({
        data: {
          studentId: student.id,
          classId,
          academicYearId,
          enrollmentNumber: `ENR-${admissionNumber}-${new Date().getFullYear()}`,
          enrollmentDate: new Date(),
          isActive: true
        }
      });

      return { user, student, enrollment };
    });

    logger.info(`Student created: ${result.student.id}`);

    res.status(201).json({
      success: true,
      message: 'Student created successfully',
      data: { student: result.student }
    });

  } catch (error) {
    logger.error('Error creating student:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create student',
      error: error.message
    });
  }
};

/**
 * Update student
 */
const updateStudent = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id },
      include: { user: true }
    });

    if (!existingStudent) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Update student and user in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update user data if provided
      if (updateData.firstName || updateData.lastName || updateData.email || updateData.phoneNumber) {
        await tx.user.update({
          where: { id: existingStudent.userId },
          data: {
            ...(updateData.firstName && { firstName: updateData.firstName }),
            ...(updateData.lastName && { lastName: updateData.lastName }),
            ...(updateData.email && { email: updateData.email }),
            ...(updateData.phoneNumber && { phoneNumber: updateData.phoneNumber })
          }
        });
      }

      // Update student data
      const student = await tx.student.update({
        where: { id },
        data: {
          ...(updateData.dateOfBirth && { dateOfBirth: new Date(updateData.dateOfBirth) }),
          ...(updateData.gender && { gender: updateData.gender }),
          ...(updateData.bloodGroup && { bloodGroup: updateData.bloodGroup }),
          ...(updateData.address && { address: updateData.address }),
          ...(updateData.emergencyContact && { emergencyContact: updateData.emergencyContact }),
          ...(updateData.medicalInfo && { medicalInfo: updateData.medicalInfo }),
          ...(updateData.status && { status: updateData.status })
        }
      });

      return student;
    });

    logger.info(`Student updated: ${id}`);

    res.status(200).json({
      success: true,
      message: 'Student updated successfully',
      data: { student: result }
    });

  } catch (error) {
    logger.error('Error updating student:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update student',
      error: error.message
    });
  }
};

/**
 * Delete student
 */
const deleteStudent = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id }
    });

    if (!existingStudent) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Soft delete by updating status
    await prisma.student.update({
      where: { id },
      data: { status: 'INACTIVE' }
    });

    logger.info(`Student deleted: ${id}`);

    res.status(200).json({
      success: true,
      message: 'Student deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting student:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete student',
      error: error.message
    });
  }
};

/**
 * Get student statistics
 */
const getStudentStats = async (req, res) => {
  try {
    const { institutionId } = req.query;

    const [
      totalStudents,
      activeStudents,
      inactiveStudents,
      suspendedStudents,
      graduatedStudents
    ] = await Promise.all([
      prisma.student.count({
        where: { institutionId }
      }),
      prisma.student.count({
        where: { 
          institutionId,
          status: 'ACTIVE' 
        }
      }),
      prisma.student.count({
        where: { 
          institutionId,
          status: 'INACTIVE' 
        }
      }),
      prisma.student.count({
        where: { 
          institutionId,
          status: 'SUSPENDED' 
        }
      }),
      prisma.student.count({
        where: { 
          institutionId,
          status: 'GRADUATED' 
        }
      })
    ]);

    res.json({
      success: true,
      data: {
        total: totalStudents,
        active: activeStudents,
        inactive: inactiveStudents,
        suspended: suspendedStudents,
        graduated: graduatedStudents
      }
    });
  } catch (error) {
    logger.error('Error fetching student statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student statistics',
      error: error.message
    });
  }
};

/**
 * Bulk create students
 */
const bulkCreateStudents = async (req, res) => {
  try {
    const { students } = req.body;
    
    if (!Array.isArray(students) || students.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Students array is required and must not be empty'
      });
    }

    const createdStudents = await prisma.student.createMany({
      data: students,
      skipDuplicates: true
    });

    res.status(201).json({
      success: true,
      message: `Successfully created ${createdStudents.count} students`,
      data: createdStudents
    });
  } catch (error) {
    logger.error('Error bulk creating students:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to bulk create students',
      error: error.message
    });
  }
};

/**
 * Get student attendance
 */
const getStudentAttendance = async (req, res) => {
  try {
    const { id } = req.params;
    const { startDate, endDate } = req.query;

    const where = {
      studentId: id
    };

    if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      };
    }

    const attendance = await prisma.attendance.findMany({
      where,
      include: {
        class: {
          select: {
            name: true,
            subject: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    res.json({
      success: true,
      data: attendance
    });
  } catch (error) {
    logger.error('Error fetching student attendance:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student attendance',
      error: error.message
    });
  }
};

/**
 * Get student grades
 */
const getStudentGrades = async (req, res) => {
  try {
    const { id } = req.params;
    
    const grades = await prisma.grade.findMany({
      where: {
        studentId: id
      },
      include: {
        subject: {
          select: {
            name: true,
            code: true
          }
        },
        exam: {
          select: {
            name: true,
            type: true,
            date: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.json({
      success: true,
      data: grades
    });
  } catch (error) {
    logger.error('Error fetching student grades:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student grades',
      error: error.message
    });
  }
};

/**
 * Get student fees
 */
const getStudentFees = async (req, res) => {
  try {
    const { id } = req.params;
    
    const fees = await prisma.feeRecord.findMany({
      where: {
        studentId: id
      },
      include: {
        feeStructure: {
          select: {
            name: true,
            amount: true,
            dueDate: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.json({
      success: true,
      data: fees
    });
  } catch (error) {
    logger.error('Error fetching student fees:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student fees',
      error: error.message
    });
  }
};

/**
 * Get student enrollments
 */
const getStudentEnrollments = async (req, res) => {
  try {
    const { id } = req.params;
    
    const enrollments = await prisma.enrollment.findMany({
      where: {
        studentId: id
      },
      include: {
        class: {
          select: {
            name: true,
            grade: true,
            section: true
          }
        },
        academicYear: {
          select: {
            name: true,
            startDate: true,
            endDate: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.json({
      success: true,
      data: enrollments
    });
  } catch (error) {
    logger.error('Error fetching student enrollments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student enrollments',
      error: error.message
    });
  }
};

module.exports = {
  getStudents,
  getStudentById,
  createStudent,
  updateStudent,
  deleteStudent,
  getStudentStats,
  bulkCreateStudents,
  getStudentAttendance,
  getStudentGrades,
  getStudentFees,
  getStudentEnrollments
};