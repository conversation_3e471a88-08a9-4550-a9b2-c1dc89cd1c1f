"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { ArrowRight, CheckCircle2 } from "lucide-react"


export function HeroSection() {
  const [isVisible, setIsVisible] = useState(false)



  useEffect(() => {
    setIsVisible(true)
  }, [])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 },
    },
  }

  const benefits = [
    "Streamlined administration",
    "Enhanced learning experience",
    "Real-time communication",
    "Data-driven insights",
  ]

  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-white to-emerald-50 py-20 md:py-32">
      {/* Background pattern */}
      <div className="absolute inset-0 z-0 opacity-10">
        <div className="absolute -top-24 -right-24 h-96 w-96 rounded-full bg-emerald-300 blur-3xl" />
        <div className="absolute top-1/2 left-1/4 h-64 w-64 rounded-full bg-emerald-400 blur-3xl" />
      </div>

      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="flex flex-col lg:flex-row items-center gap-12 lg:gap-20"
          initial="hidden"
          animate={isVisible ? "visible" : "hidden"}
          variants={containerVariants}
        >
          <div className="lg:w-1/2">
            <motion.h1
              className="text-4xl sm:text-5xl md:text-6xl font-bold text-gray-900 leading-tight"
              variants={itemVariants}
            >
              Transform Your <span className="text-emerald-600">School Management</span> Experience
            </motion.h1>

            <motion.p className="mt-6 text-xl text-gray-600 max-w-3xl" variants={itemVariants}>
              A comprehensive school management system designed for Zambian schools. Streamline administration, enhance
              learning, and connect your entire school community.
            </motion.p>

            <motion.div className="mt-6 space-y-4" variants={itemVariants}>
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-2">
                  <CheckCircle2 className="h-5 w-5 text-emerald-500 flex-shrink-0" />
                  <span className="text-gray-700">{benefit}</span>
                </div>
              ))}
            </motion.div>

            <motion.div className="mt-10 flex flex-col sm:flex-row gap-4" variants={itemVariants}>
              <Link href="/auth/register">
                <Button size="lg" className="text-lg px-8 group relative overflow-hidden">
                  <span className="relative z-10">Get Started</span>
                  <span className="absolute inset-0 bg-emerald-600 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></span>
                </Button>
              </Link>
              <Link href="#demo">
                <Button size="lg" variant="outline" className="text-lg px-8 group">
                  <span>Request Demo</span>
                  <ArrowRight className="ml-2 h-5 w-5 transform group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </motion.div>
          </div>

          <motion.div className="lg:w-1/2 relative" variants={itemVariants}>
            <div className="relative w-full h-[400px] lg:h-[500px] rounded-xl overflow-hidden shadow-2xl transform rotate-1 hover:rotate-0 transition-transform duration-500">
              <div className="absolute inset-0 bg-gradient-to-tr from-emerald-500/20 to-transparent z-10 rounded-xl" />
              <Image
                src="/placeholder.svg?height=500&width=600"
                alt="School Management Dashboard"
                fill
                className="object-cover rounded-xl"
                priority
              />
            </div>

            {/* Floating elements */}
            <div className="absolute -top-6 -left-6 bg-white p-4 rounded-lg shadow-lg transform -rotate-3 hidden md:block">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-emerald-500"></div>
                <span className="text-sm font-medium">98% Attendance</span>
              </div>
            </div>

            <div className="absolute -bottom-4 -right-4 bg-white p-4 rounded-lg shadow-lg transform rotate-3 hidden md:block">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-emerald-500"></div>
                <span className="text-sm font-medium">Grade Improved by 25%</span>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
