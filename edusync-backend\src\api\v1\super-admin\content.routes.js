const express = require('express');
const router = express.Router();
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const {
  getFeatures,
  createFeature,
  updateFeature,
  deleteFeature,
  getPricingPlans,
  createPricingPlan,
  updatePricingPlan,
  deletePricingPlan,
  getTestimonials,
  createTestimonial,
  updateTestimonial,
  deleteTestimonial
} = require('./content.controller');

// Protect all routes and restrict to SUPER_ADMIN
router.use(authenticate);
router.use(restrictTo('SUPER_ADMIN'));

// Features routes
router.get('/features', getFeatures);
router.post('/features', createFeature);
router.put('/features/:id', updateFeature);
router.delete('/features/:id', deleteFeature);

// Pricing plans routes
router.get('/pricing', getPricingPlans);
router.post('/pricing', createPricingPlan);
router.put('/pricing/:id', updatePricingPlan);
router.delete('/pricing/:id', deletePricingPlan);

// Testimonials routes
router.get('/testimonials', getTestimonials);
router.post('/testimonials', createTestimonial);
router.put('/testimonials/:id', updateTestimonial);
router.delete('/testimonials/:id', deleteTestimonial);

module.exports = router;
