import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Calendar, Clock, Download, Printer, Users, BookOpen, MapPin } from "lucide-react"
import Link from "next/link"

export default function TeacherSchedulePage({ params }: { params: { id: string } }) {
  const teacherId = params.id

  // This would normally come from a database
  const teacher = {
    id: teacherId,
    name: "Dr. <PERSON>",
    department: "Mathematics",
    position: "Senior Mathematics Teacher",
    employeeId: "EMP-2015-042",
    classes: ["10A", "10B", "11A", "12A"],
    subjects: ["Algebra II", "Calculus", "Statistics"],
  }

  // Sample schedule data
  const weeklySchedule = [
    {
      day: "Monday",
      periods: [
        { period: 1, time: "8:00 AM - 8:50 AM", class: "10A", subject: "Algebra II", room: "M-101" },
        { period: 2, time: "9:00 AM - 9:50 AM", class: "11A", subject: "Calculus", room: "M-102" },
        { period: 3, time: "10:00 AM - 10:50 AM", class: null, subject: null, room: null },
        { period: 4, time: "11:00 AM - 11:50 AM", class: "12A", subject: "Statistics", room: "M-103" },
        { period: 5, time: "12:00 PM - 12:50 PM", class: null, subject: null, room: null, note: "Lunch Break" },
        { period: 6, time: "1:00 PM - 1:50 PM", class: "10B", subject: "Algebra II", room: "M-101" },
        { period: 7, time: "2:00 PM - 2:50 PM", class: null, subject: null, room: null, note: "Department Meeting" },
        { period: 8, time: "3:00 PM - 3:50 PM", class: null, subject: null, room: null, note: "Office Hours" },
      ],
    },
    {
      day: "Tuesday",
      periods: [
        { period: 1, time: "8:00 AM - 8:50 AM", class: "10B", subject: "Algebra II", room: "M-101" },
        { period: 2, time: "9:00 AM - 9:50 AM", class: null, subject: null, room: null, note: "Prep Time" },
        { period: 3, time: "10:00 AM - 10:50 AM", class: "11A", subject: "Calculus", room: "M-102" },
        { period: 4, time: "11:00 AM - 11:50 AM", class: "12A", subject: "Statistics", room: "M-103" },
        { period: 5, time: "12:00 PM - 12:50 PM", class: null, subject: null, room: null, note: "Lunch Break" },
        { period: 6, time: "1:00 PM - 1:50 PM", class: "10A", subject: "Algebra II", room: "M-101" },
        { period: 7, time: "2:00 PM - 2:50 PM", class: "11A", subject: "Calculus", room: "M-102" },
        { period: 8, time: "3:00 PM - 3:50 PM", class: null, subject: null, room: null, note: "Office Hours" },
      ],
    },
    {
      day: "Wednesday",
      periods: [
        { period: 1, time: "8:00 AM - 8:50 AM", class: "10A", subject: "Algebra II", room: "M-101" },
        { period: 2, time: "9:00 AM - 9:50 AM", class: "11A", subject: "Calculus", room: "M-102" },
        { period: 3, time: "10:00 AM - 10:50 AM", class: null, subject: null, room: null, note: "Prep Time" },
        { period: 4, time: "11:00 AM - 11:50 AM", class: "12A", subject: "Statistics", room: "M-103" },
        { period: 5, time: "12:00 PM - 12:50 PM", class: null, subject: null, room: null, note: "Lunch Break" },
        { period: 6, time: "1:00 PM - 1:50 PM", class: "10B", subject: "Algebra II", room: "M-101" },
        { period: 7, time: "2:00 PM - 2:50 PM", class: null, subject: null, room: null, note: "Faculty Meeting" },
        { period: 8, time: "3:00 PM - 3:50 PM", class: null, subject: null, room: null, note: "Office Hours" },
      ],
    },
    {
      day: "Thursday",
      periods: [
        { period: 1, time: "8:00 AM - 8:50 AM", class: "10B", subject: "Algebra II", room: "M-101" },
        { period: 2, time: "9:00 AM - 9:50 AM", class: null, subject: null, room: null, note: "Prep Time" },
        { period: 3, time: "10:00 AM - 10:50 AM", class: "11A", subject: "Calculus", room: "M-102" },
        { period: 4, time: "11:00 AM - 11:50 AM", class: "12A", subject: "Statistics", room: "M-103" },
        { period: 5, time: "12:00 PM - 12:50 PM", class: null, subject: null, room: null, note: "Lunch Break" },
        { period: 6, time: "1:00 PM - 1:50 PM", class: "10A", subject: "Algebra II", room: "M-101" },
        { period: 7, time: "2:00 PM - 2:50 PM", class: "11A", subject: "Calculus", room: "M-102" },
        { period: 8, time: "3:00 PM - 3:50 PM", class: null, subject: null, room: null, note: "Office Hours" },
      ],
    },
    {
      day: "Friday",
      periods: [
        { period: 1, time: "8:00 AM - 8:50 AM", class: "10A", subject: "Algebra II", room: "M-101" },
        { period: 2, time: "9:00 AM - 9:50 AM", class: "11A", subject: "Calculus", room: "M-102" },
        { period: 3, time: "10:00 AM - 10:50 AM", class: null, subject: null, room: null, note: "Prep Time" },
        { period: 4, time: "11:00 AM - 11:50 AM", class: "12A", subject: "Statistics", room: "M-103" },
        { period: 5, time: "12:00 PM - 12:50 PM", class: null, subject: null, room: null, note: "Lunch Break" },
        { period: 6, time: "1:00 PM - 1:50 PM", class: "10B", subject: "Algebra II", room: "M-101" },
        {
          period: 7,
          time: "2:00 PM - 2:50 PM",
          class: null,
          subject: null,
          room: null,
          note: "Professional Development",
        },
        { period: 8, time: "3:00 PM - 3:50 PM", class: null, subject: null, room: null },
      ],
    },
  ]

  // Sample upcoming events
  const upcomingEvents = [
    {
      title: "Mathematics Department Meeting",
      date: "October 18, 2023",
      time: "2:00 PM - 3:30 PM",
      location: "Conference Room A",
    },
    {
      title: "Parent-Teacher Conference",
      date: "October 20, 2023",
      time: "4:00 PM - 7:00 PM",
      location: "School Auditorium",
    },
    {
      title: "Mathematics Competition Preparation",
      date: "October 25, 2023",
      time: "3:00 PM - 4:30 PM",
      location: "Room M-101",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <Link href={`/dashboard/teachers/${teacherId}`}>
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Back to teacher profile</span>
              </Link>
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">Teaching Schedule</h1>
          </div>
          <p className="text-muted-foreground ml-10">
            Weekly schedule for {teacher.name} ({teacher.position})
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Printer className="mr-2 h-4 w-4" />
            Print Schedule
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export to Calendar
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Teaching Hours</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">Hours per week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Classes Taught</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">Unique classes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Subjects</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">Different subjects</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Students</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">128</div>
            <p className="text-xs text-muted-foreground">Total students taught</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="weekly" className="space-y-4">
        <TabsList>
          <TabsTrigger value="weekly">Weekly Schedule</TabsTrigger>
          <TabsTrigger value="daily">Daily View</TabsTrigger>
          <TabsTrigger value="events">Upcoming Events</TabsTrigger>
        </TabsList>

        <TabsContent value="weekly" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Weekly Teaching Schedule</CardTitle>
              <CardDescription>Complete weekly schedule for the current term</CardDescription>
            </CardHeader>
            <CardContent className="overflow-auto">
              <div className="min-w-[800px]">
                <div className="grid grid-cols-9 bg-muted/50 p-2 text-sm font-medium">
                  <div>Period</div>
                  <div>Time</div>
                  <div>Monday</div>
                  <div>Tuesday</div>
                  <div>Wednesday</div>
                  <div>Thursday</div>
                  <div>Friday</div>
                </div>
                <div className="divide-y">
                  {Array.from({ length: 8 }, (_, i) => i + 1).map((period) => (
                    <div key={period} className="grid grid-cols-9 p-2 text-sm">
                      <div className="font-medium">{period}</div>
                      <div className="text-muted-foreground">{weeklySchedule[0].periods[period - 1].time}</div>
                      {weeklySchedule.map((day, index) => {
                        const slot = day.periods[period - 1]
                        return (
                          <div key={index} className={slot.class ? "font-medium" : "text-muted-foreground"}>
                            {slot.class ? (
                              <>
                                <div className="font-medium">
                                  {slot.class} - {slot.subject}
                                </div>
                                <div className="text-xs text-muted-foreground">Room {slot.room}</div>
                              </>
                            ) : (
                              <div>{slot.note || "Free Period"}</div>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Class Distribution</CardTitle>
                <CardDescription>Number of periods per class</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {teacher.classes.map((className, index) => (
                    <div key={index}>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">Class {className}</span>
                        <span className="text-sm font-medium">
                          {
                            weeklySchedule.flatMap((day) => day.periods).filter((period) => period.class === className)
                              .length
                          }{" "}
                          periods
                        </span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full"
                          style={{
                            width: `${(weeklySchedule.flatMap((day) => day.periods).filter((period) => period.class === className).length / 40) * 100}%`,
                          }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Subject Distribution</CardTitle>
                <CardDescription>Number of periods per subject</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {teacher.subjects.map((subject, index) => (
                    <div key={index}>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">{subject}</span>
                        <span className="text-sm font-medium">
                          {
                            weeklySchedule.flatMap((day) => day.periods).filter((period) => period.subject === subject)
                              .length
                          }{" "}
                          periods
                        </span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full"
                          style={{
                            width: `${(weeklySchedule.flatMap((day) => day.periods).filter((period) => period.subject === subject).length / 40) * 100}%`,
                          }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="daily" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Daily Schedule</CardTitle>
                <CardDescription>Today's teaching schedule</CardDescription>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  Previous Day
                </Button>
                <Button variant="outline" size="sm">
                  Today
                </Button>
                <Button variant="outline" size="sm">
                  Next Day
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">Monday, October 16, 2023</h3>
                </div>

                <div className="space-y-4">
                  {weeklySchedule[0].periods.map((period, index) => (
                    <Card key={index}>
                      <CardContent className="p-4">
                        <div className="flex items-start gap-4">
                          <div className="bg-primary/10 text-primary font-medium rounded-lg p-2 text-center min-w-[60px]">
                            <div className="text-xs text-muted-foreground">Period</div>
                            <div className="text-lg">{period.period}</div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h3 className="font-medium">
                                {period.class ? `${period.class} - ${period.subject}` : period.note || "Free Period"}
                              </h3>
                              <span className="text-sm text-muted-foreground">{period.time}</span>
                            </div>
                            {period.class && (
                              <div className="mt-2 space-y-1">
                                <div className="flex items-center text-sm">
                                  <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                                  <span>32 Students</span>
                                </div>
                                <div className="flex items-center text-sm">
                                  <BookOpen className="mr-2 h-4 w-4 text-muted-foreground" />
                                  <span>Topic: Introduction to {period.subject}</span>
                                </div>
                                <div className="flex items-center text-sm">
                                  <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                                  <span>Room {period.room}</span>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Events</CardTitle>
              <CardDescription>Meetings, conferences, and other scheduled events</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingEvents.map((event, index) => (
                  <Card key={index}>
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        <div className="bg-primary/10 text-primary font-medium rounded-lg p-2 text-center min-w-[60px]">
                          <div className="text-xs text-muted-foreground">{event.date.split(", ")[0]}</div>
                          <div className="text-lg">{event.date.split(" ")[1].replace(",", "")}</div>
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium">{event.title}</h3>
                          <div className="mt-2 space-y-1">
                            <div className="flex items-center text-sm">
                              <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                              <span>{event.time}</span>
                            </div>
                            <div className="flex items-center text-sm">
                              <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                              <span>{event.location}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">
                <Calendar className="mr-2 h-4 w-4" />
                Add New Event
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Calendar View</CardTitle>
              <CardDescription>Monthly calendar with scheduled events</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">October 2023</h3>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      Previous
                    </Button>
                    <Button variant="outline" size="sm">
                      Today
                    </Button>
                    <Button variant="outline" size="sm">
                      Next
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-7 gap-2 text-center">
                  <div className="text-sm font-medium">Sun</div>
                  <div className="text-sm font-medium">Mon</div>
                  <div className="text-sm font-medium">Tue</div>
                  <div className="text-sm font-medium">Wed</div>
                  <div className="text-sm font-medium">Thu</div>
                  <div className="text-sm font-medium">Fri</div>
                  <div className="text-sm font-medium">Sat</div>

                  {/* Empty cells for days before the month starts */}
                  <div></div>

                  {/* Calendar days */}
                  {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => {
                    // Determine if there's an event on this day
                    const hasEvent = upcomingEvents.some(
                      (event) => Number.parseInt(event.date.split(" ")[1].replace(",", "")) === day,
                    )

                    // Skip days after the current date
                    if (day > 31) return <div key={day}></div>

                    return (
                      <div
                        key={day}
                        className={`aspect-square flex items-center justify-center rounded-md border text-sm ${
                          hasEvent ? "bg-primary/10 border-primary/20" : ""
                        } ${day === 16 ? "bg-primary text-primary-foreground" : ""}`}
                      >
                        {day}
                      </div>
                    )
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
