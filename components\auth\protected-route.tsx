"use client"

import { useAuth } from "@/contexts/auth-context"
import { AuthRecovery } from "./auth-recovery"
import { useAuthPersistence } from "@/hooks/use-auth-persistence"

interface ProtectedRouteProps {
  children: React.ReactNode
  allowedRoles?: string[]
}

export function ProtectedRoute({ children, allowedRoles }: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated } = useAuth()
  const { shouldAttemptRecovery, isRateLimited } = useAuthPersistence()

  // Use AuthRecovery for better error handling and retry logic
  return (
    <AuthRecovery
      showRetryButton={!isRateLimited}
      autoRetry={shouldAttemptRecovery()}
      maxRetries={3}
    >
      {/* Show loading spinner while authentication is being checked */}
      {isLoading ? (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      ) : !isAuthenticated || !user ? (
        // Don't render anything if not authenticated - let AuthRecovery handle it
        null
      ) : allowedRoles && !allowedRoles.includes(user.role) ? (
        // Don't render anything if user doesn't have required role - let middleware handle redirects
        null
      ) : (
        // Render children if authenticated and authorized
        children
      )}
    </AuthRecovery>
  )
}
