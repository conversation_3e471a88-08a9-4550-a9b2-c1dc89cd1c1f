"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Plus, Edit, Trash2, Star, Eye, EyeOff } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { superAdminService } from "@/lib/backend-api"

interface Feature {
  id: number
  title: string
  description: string
  icon: string
  benefits: string[]
  category: string
  is_active: boolean
  order_index: number
  created_at: string
  updated_at: string
}

export default function FeaturesManagementPage() {
  const [features, setFeatures] = useState<Feature[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [dialogOpen, setDialogOpen] = useState<boolean>(false)
  const [editingFeature, setEditingFeature] = useState<Feature | null>(null)
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    icon: "",
    benefits: [""],
    category: "",
    is_active: true,
    order_index: 0,
  })
  const { toast } = useToast()

  useEffect(() => {
    fetchFeatures()
  }, [])

  const fetchFeatures = async () => {
    try {
      setLoading(true)
      const response = await superAdminService.getFeatures()
      
      if (response.success && response.data) {
        setFeatures(response.data)
      } else {
        toast({
          title: "Warning",
          description: "Failed to fetch features. Using fallback data.",
          variant: "destructive",
        })
        // You can add fallback/mock data here if needed
        setFeatures([])
      }
    } catch (err: any) {
      console.error("Error fetching features:", err)
      toast({
        title: "Error",
        description: "Failed to fetch features",
        variant: "destructive",
      })
      setFeatures([])
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const response = editingFeature 
        ? await superAdminService.updateFeatures({ ...formData, id: editingFeature.id })
        : await superAdminService.updateFeatures(formData)

      if (response.success) {
        toast({
          title: "Success",
          description: editingFeature ? "Feature updated successfully" : "Feature created successfully",
        })
        
        setDialogOpen(false)
        resetForm()
        fetchFeatures() // Refresh the list
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to save feature",
          variant: "destructive",
        })
      }
    } catch (err: any) {
      console.error("Error saving feature:", err)
      toast({
        title: "Error",
        description: err?.message || "Failed to save feature",
        variant: "destructive",
      })
    }
  }

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      icon: "",
      benefits: [""],
      category: "",
      is_active: true,
      order_index: 0,
    })
    setEditingFeature(null)
  }

      const method = editingFeature ? "PUT" : "POST"

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          benefits: formData.benefits.filter((b) => b.trim() !== ""),
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: `Feature ${editingFeature ? "updated" : "created"} successfully`,
        })
        fetchFeatures()
        setDialogOpen(false)
        resetForm()
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${editingFeature ? "update" : "create"} feature`,
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (id: number) => {
    if (!confirm("Are you sure you want to delete this feature?")) return

    try {
      const response = await fetch(`/api/super-admin/features/${id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: "Feature deleted successfully",
        })
        fetchFeatures()
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete feature",
        variant: "destructive",
      })
    }
  }

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      icon: "",
      benefits: [""],
      category: "",
      is_active: true,
      order_index: 0,
    })
    setEditingFeature(null)
  }

  const openEditDialog = (feature: Feature) => {
    setEditingFeature(feature)
    setFormData({
      title: feature.title,
      description: feature.description,
      icon: feature.icon,
      benefits: feature.benefits.length > 0 ? feature.benefits : [""],
      category: feature.category,
      is_active: feature.is_active,
      order_index: feature.order_index,
    })
    setDialogOpen(true)
  }

  const addBenefit = () => {
    setFormData((prev) => ({
      ...prev,
      benefits: [...prev.benefits, ""],
    }))
  }

  const updateBenefit = (index: number, value: string) => {
    setFormData((prev) => ({
      ...prev,
      benefits: prev.benefits.map((benefit, i) => (i === index ? value : benefit)),
    }))
  }

  const removeBenefit = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      benefits: prev.benefits.filter((_, i) => i !== index),
    }))
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Features Management</h1>
          <p className="text-gray-600">Manage website features and capabilities</p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="mr-2 h-4 w-4" />
              Add Feature
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{editingFeature ? "Edit Feature" : "Add New Feature"}</DialogTitle>
              <DialogDescription>
                {editingFeature ? "Update the feature details" : "Create a new feature for the website"}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="icon">Icon</Label>
                  <Input
                    id="icon"
                    value={formData.icon}
                    onChange={(e) => setFormData((prev) => ({ ...prev, icon: e.target.value }))}
                    placeholder="e.g., Users, BookOpen, Settings"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => setFormData((prev) => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="core">Core</SelectItem>
                      <SelectItem value="academic">Academic</SelectItem>
                      <SelectItem value="administrative">Administrative</SelectItem>
                      <SelectItem value="communication">Communication</SelectItem>
                      <SelectItem value="reporting">Reporting</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="order_index">Order</Label>
                  <Input
                    id="order_index"
                    type="number"
                    value={formData.order_index}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, order_index: Number.parseInt(e.target.value) || 0 }))
                    }
                  />
                </div>
              </div>

              <div>
                <Label>Benefits</Label>
                {formData.benefits.map((benefit, index) => (
                  <div key={index} className="flex gap-2 mt-2">
                    <Input
                      value={benefit}
                      onChange={(e) => updateBenefit(index, e.target.value)}
                      placeholder="Enter benefit"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeBenefit(index)}
                      disabled={formData.benefits.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={addBenefit} className="mt-2">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Benefit
                </Button>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, is_active: checked }))}
                />
                <Label htmlFor="is_active">Active</Label>
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">{editingFeature ? "Update" : "Create"} Feature</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {features.map((feature) => (
          <Card key={feature.id} className={!feature.is_active ? "opacity-50" : ""}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  <Star className="h-5 w-5 text-emerald-600" />
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </div>
                <div className="flex items-center space-x-1">
                  {feature.is_active ? (
                    <Eye className="h-4 w-4 text-green-600" />
                  ) : (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              </div>
              <CardDescription>{feature.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Badge variant="secondary">{feature.category}</Badge>
                  <span className="text-sm text-gray-500">Order: {feature.order_index}</span>
                </div>

                {feature.benefits && feature.benefits.length > 0 && (
                  <div>
                    <p className="text-sm font-medium mb-1">Benefits:</p>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {feature.benefits.slice(0, 3).map((benefit, index) => (
                        <li key={index} className="flex items-center">
                          <span className="w-1 h-1 bg-emerald-600 rounded-full mr-2"></span>
                          {benefit}
                        </li>
                      ))}
                      {feature.benefits.length > 3 && (
                        <li className="text-xs text-gray-500">+{feature.benefits.length - 3} more</li>
                      )}
                    </ul>
                  </div>
                )}

                <div className="flex justify-end space-x-2 pt-2">
                  <Button variant="outline" size="sm" onClick={() => openEditDialog(feature)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleDelete(feature.id)}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {features.length === 0 && (
        <div className="text-center py-12">
          <Star className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No features</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating a new feature.</p>
        </div>
      )}
    </div>
  )
}
