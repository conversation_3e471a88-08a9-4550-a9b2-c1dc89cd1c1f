"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Building,
  Search,
  MoreHorizontal,
  Download,
  X,
  CreditCard,
  Calendar,
  CheckCircle,
  AlertCircle,
  BarChart,
  TrendingUp,
  Edit,
  Plus,
} from "lucide-react"
import { superAdminService } from "@/lib/backend-api"
import { useToast } from "@/hooks/use-toast"

// TypeScript interfaces
interface Institution {
  id: string
  name: string
  type?: string
  location?: string
}

interface Subscription {
  id: string
  institution: Institution
  plan: string
  status: 'Active' | 'Inactive' | 'Pending' | 'Suspended'
  startDate: string
  endDate: string
  amount: string
  billingCycle: 'Monthly' | 'Yearly'
  autoRenew: boolean
  paymentMethod: string
  lastPayment: string
  nextPayment: string
}

interface Payment {
  id: string
  institution: Institution
  date: string
  amount: string
  status: 'Paid' | 'Pending' | 'Failed'
  method: string
  invoice: string
  plan: string
}

interface SubscriptionPlan {
  id: string
  name: string
  price: string
  period: string
  description: string
  features: string[]
  storage: string
  popular?: boolean
}

interface SubscriptionStats {
  totalSubscriptions: number
  activeSubscriptions: number
  pendingSubscriptions: number
  monthlyRevenue: string
  subscriptionsGrowth: string
  revenueGrowth: string
}

// Mock data for subscriptions
const mockSubscriptions = [
  {
    id: "1",
    institution: {
      id: "1",
      name: "Lusaka Primary School",
      type: "Primary",
      location: "Lusaka",
    },
    plan: "Premium",
    status: "Active",
    startDate: "January 1, 2025",
    endDate: "December 31, 2025",
    amount: "K2,500",
    billingCycle: "Monthly",
    autoRenew: true,
    paymentMethod: "Credit Card",
    lastPayment: "May 1, 2025",
    nextPayment: "June 1, 2025",
  },
  {
    id: "2",
    institution: {
      id: "2",
      name: "Kitwe Secondary School",
      type: "Secondary",
      location: "Kitwe",
    },
    plan: "Standard",
    status: "Active",
    startDate: "May 1, 2025",
    endDate: "October 31, 2025",
    amount: "K1,200",
    billingCycle: "Monthly",
    autoRenew: true,
    paymentMethod: "Bank Transfer",
    lastPayment: "May 1, 2025",
    nextPayment: "June 1, 2025",
  },
  {
    id: "3",
    institution: {
      id: "3",
      name: "Ndola Academy",
      type: "Primary",
      location: "Ndola",
    },
    plan: "Premium",
    status: "Active",
    startDate: "April 1, 2025",
    endDate: "March 31, 2026",
    amount: "K2,500",
    billingCycle: "Monthly",
    autoRenew: true,
    paymentMethod: "Credit Card",
    lastPayment: "May 1, 2025",
    nextPayment: "June 1, 2025",
  },
  {
    id: "4",
    institution: {
      id: "4",
      name: "Livingstone College",
      type: "College",
      location: "Livingstone",
    },
    plan: "Enterprise",
    status: "Pending",
    startDate: "Pending Approval",
    endDate: "Pending Approval",
    amount: "K2,500",
    billingCycle: "Monthly",
    autoRenew: true,
    paymentMethod: "Pending",
    lastPayment: "N/A",
    nextPayment: "Pending Approval",
  },
]

// Mock data for payment history
const mockPayments = [
  {
    id: "1",
    institution: {
      id: "1",
      name: "Lusaka Primary School",
    },
    date: "May 1, 2025",
    amount: "K2,500",
    status: "Paid",
    method: "Credit Card",
    invoice: "INV-2025-005",
    plan: "Premium",
  },
  {
    id: "2",
    institution: {
      id: "2",
      name: "Kitwe Secondary School",
    },
    date: "May 1, 2025",
    amount: "K1,200",
    status: "Paid",
    method: "Bank Transfer",
    invoice: "INV-2025-006",
    plan: "Standard",
  },
  {
    id: "3",
    institution: {
      id: "3",
      name: "Ndola Academy",
    },
    date: "May 1, 2025",
    amount: "K2,500",
    status: "Paid",
    method: "Credit Card",
    invoice: "INV-2025-007",
    plan: "Premium",
  },
  {
    id: "4",
    institution: {
      id: "1",
      name: "Lusaka Primary School",
    },
    date: "April 1, 2025",
    amount: "K2,500",
    status: "Paid",
    method: "Credit Card",
    invoice: "INV-2025-004",
    plan: "Premium",
  },
  {
    id: "5",
    institution: {
      id: "2",
      name: "Kitwe Secondary School",
    },
    date: "April 1, 2025",
    amount: "K1,200",
    status: "Paid",
    method: "Bank Transfer",
    invoice: "INV-2025-003",
    plan: "Standard",
  },
]

// Mock data for subscription plans
const subscriptionPlans = [
  {
    id: "basic",
    name: "Basic",
    price: "K500",
    period: "per month",
    description: "Perfect for small schools",
    features: [
      "Up to 200 students",
      "Basic student management",
      "Attendance tracking",
      "5 staff accounts",
      "Email support",
    ],
    storage: "50 GB",
  },
  {
    id: "standard",
    name: "Standard",
    price: "K1,200",
    period: "per month",
    description: "Ideal for medium-sized schools",
    features: [
      "Up to 500 students",
      "Advanced student management",
      "Fee management",
      "Timetable scheduling",
      "15 staff accounts",
      "Priority email support",
      "Parent portal access",
    ],
    storage: "75 GB",
    popular: true,
  },
  {
    id: "premium",
    name: "Premium",
    price: "K2,500",
    period: "per month",
    description: "For large educational institutions",
    features: [
      "Unlimited students",
      "Complete management suite",
      "Advanced analytics",
      "Unlimited staff accounts",
      "24/7 priority support",
      "Custom integrations",
      "Multi-campus support",
    ],
    storage: "100 GB",
  },
  {
    id: "enterprise",
    name: "Enterprise",
    price: "Custom",
    period: "contact sales",
    description: "For large multi-campus institutions",
    features: [
      "Unlimited students",
      "Complete management suite",
      "Advanced analytics",
      "Unlimited staff accounts",
      "24/7 priority support",
      "Custom integrations",
      "Multi-campus support",
      "Dedicated account manager",
      "Custom branding",
      "API access",
    ],
    storage: "200 GB",
  },
]

export default function SubscriptionsPage() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [payments, setPayments] = useState<Payment[]>([])
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [stats, setStats] = useState<SubscriptionStats | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string>("")
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [planFilter, setPlanFilter] = useState<string>("")
  const [statusFilter, setStatusFilter] = useState<string>("")
  
  const { toast } = useToast()

  // Fetch all data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        setError("")

        // Fetch subscriptions data using the backend API service
        const response = await superAdminService.getAllSubscriptions()
        
        if (response.success && response.data) {
          setSubscriptions(response.data.subscriptions || mockSubscriptions)
          setPayments(response.data.payments || mockPayments)
          setPlans(response.data.plans || mockPlans)
          setStats(response.data.stats || {
            totalSubscriptions: 0,
            activeSubscriptions: 0,
            pendingSubscriptions: 0,
            monthlyRevenue: "K0",
            subscriptionsGrowth: "0%",
            revenueGrowth: "0%"
          })
        } else {
          // Fallback to mock data on error
          setSubscriptions(mockSubscriptions)
          setPayments(mockPayments)
          setPlans(mockPlans)
          setStats({
            totalSubscriptions: mockSubscriptions.length,
            activeSubscriptions: mockSubscriptions.filter(s => s.status === 'Active').length,
            pendingSubscriptions: mockSubscriptions.filter(s => s.status === 'Pending').length,
            monthlyRevenue: "K15,200",
            subscriptionsGrowth: "+12%",
            revenueGrowth: "+8%"
          })
          
          if (response.error) {
            setError(response.error)
            toast({
              title: "Warning",
              description: "Failed to fetch subscription data. Using fallback data.",
              variant: "destructive",
            })
          }
        }
      } catch (err: any) {
        console.error("Error fetching subscription data:", err)
        setError(err?.message || "Failed to fetch subscription data")
        
        // Fallback to mock data
        setSubscriptions(mockSubscriptions)
        setPayments(mockPayments)
        setPlans(mockPlans)
        setStats({
          totalSubscriptions: mockSubscriptions.length,
          activeSubscriptions: mockSubscriptions.filter(s => s.status === 'Active').length,
          pendingSubscriptions: mockSubscriptions.filter(s => s.status === 'Pending').length,
          monthlyRevenue: "K15,200",
          subscriptionsGrowth: "+12%",
          revenueGrowth: "+8%"
        })
        
        toast({
          title: "Error",
          description: "Failed to fetch subscription data. Using fallback data.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Update subscription handler
  const handleUpdateSubscription = async (id: string, updates: Partial<Subscription>) => {
    try {
      const response = await superAdminService.updateSubscription(id, updates)
      
      if (response.success) {
        setSubscriptions(prev => 
          prev.map(subscription => 
            subscription.id === id ? { ...subscription, ...updates } : subscription
          )
        )
        
        toast({
          title: "Success",
          description: "Subscription updated successfully",
        })
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to update subscription",
          variant: "destructive",
        })
      }
    } catch (err: any) {
      console.error("Error updating subscription:", err)
      toast({
        title: "Error",
        description: err?.message || "Failed to update subscription",
        variant: "destructive",
      })
    }
  }

  const filteredSubscriptions = subscriptions.filter((subscription: Subscription) => {
    const institutionName = subscription.institution?.name || ""
    const institutionLocation = subscription.institution?.location || ""
    
    const matchesSearch =
      institutionName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      institutionLocation.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesPlan = planFilter ? subscription.plan === planFilter : true
    const matchesStatus = statusFilter ? subscription.status === statusFilter : true

    return matchesSearch && matchesPlan && matchesStatus
  })

  // Calculate revenue statistics
  const totalMonthlyRevenue = stats?.monthlyRevenue || payments
    .filter((payment: Payment) => {
      const paymentDate = new Date(payment.date)
      const currentDate = new Date()
      return (
        paymentDate.getMonth() === currentDate.getMonth() && paymentDate.getFullYear() === currentDate.getFullYear()
      )
    })
    .reduce((total: number, payment: Payment) => {
      return total + (Number.parseInt(payment.amount?.toString().replace(/[^0-9]/g, "") || "0"))
    }, 0)

  const activeSubscriptions = stats?.activeSubscriptions || subscriptions.filter((sub: Subscription) => sub.status === "Active").length
  const pendingSubscriptions = stats?.pendingSubscriptions || subscriptions.filter((sub: Subscription) => sub.status === "Pending").length

  // Distribution of plans
  const planDistribution = {
    Basic: subscriptions.filter((sub: Subscription) => sub.plan === "Basic").length,
    Standard: subscriptions.filter((sub: Subscription) => sub.plan === "Standard").length,
    Premium: subscriptions.filter((sub: Subscription) => sub.plan === "Premium").length,
    Enterprise: subscriptions.filter((sub: Subscription) => sub.plan === "Enterprise").length,
  }

  // Get most popular plan
  const planEntries = Object.entries(planDistribution as Record<string, number>)
  const mostPopularPlan = planEntries.reduce((a, b) => (a[1] > b[1] ? a : b))[0]
  const maxPlanCount = Math.max(...Object.values(planDistribution as Record<string, number>))

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Subscriptions</h1>
          <p className="text-gray-500">Manage subscription plans and payments for all institutions</p>
          {error && (
            <p className="text-red-500 text-sm mt-1">⚠️ {error}</p>
          )}
        </div>
        <div className="flex gap-2">
          <Link href="/super-admin/subscriptions/plans">
            <Button>
              <Edit className="mr-2 h-4 w-4" />
              Manage Plans
            </Button>
          </Link>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <CreditCard className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">K{totalMonthlyRevenue.toLocaleString()}</div>
            <p className="text-xs text-gray-500">
              <TrendingUp className="inline h-3 w-3 text-emerald-500 mr-1" />
              <span className="text-emerald-500 font-medium">+8.2%</span> from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <CheckCircle className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeSubscriptions}</div>
            <p className="text-xs text-gray-500">{pendingSubscriptions} pending approval</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Most Popular Plan</CardTitle>
            <BarChart className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mostPopularPlan}
            </div>
            <p className="text-xs text-gray-500">{maxPlanCount} institutions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Next Renewals</CardTitle>
            <Calendar className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeSubscriptions}</div>
            <p className="text-xs text-gray-500">Due on June 1, 2025</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="subscriptions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
          <TabsTrigger value="payments">Payment History</TabsTrigger>
          <TabsTrigger value="plans">Subscription Plans</TabsTrigger>
        </TabsList>

        <TabsContent value="subscriptions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Institution Subscriptions</CardTitle>
              <CardDescription>Manage subscription plans for all institutions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="flex-1 relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    placeholder="Search by institution name..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Select onValueChange={setPlanFilter} value={planFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by plan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Plans</SelectItem>
                      <SelectItem value="Basic">Basic</SelectItem>
                      <SelectItem value="Standard">Standard</SelectItem>
                      <SelectItem value="Premium">Premium</SelectItem>
                      <SelectItem value="Enterprise">Enterprise</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select onValueChange={setStatusFilter} value={statusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Pending">Pending</SelectItem>
                      <SelectItem value="Expired">Expired</SelectItem>
                    </SelectContent>
                  </Select>

                  {(planFilter || statusFilter) && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        setPlanFilter("")
                        setStatusFilter("")
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <p>Loading subscriptions...</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Institution</TableHead>
                        <TableHead>Plan</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Start Date</TableHead>
                        <TableHead>End Date</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Next Payment</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredSubscriptions.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center h-24">
                            No subscriptions found
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredSubscriptions.map((subscription: any) => (
                          <TableRow key={subscription.id}>
                            <TableCell>
                              <Link
                                href={`/super-admin/institutions/${subscription.institutionId || subscription.institution?.id}`}
                                className="flex items-center text-emerald-600 hover:underline"
                              >
                                <Building className="h-4 w-4 mr-1" />
                                {subscription.institutionName || subscription.institution?.name}
                              </Link>
                              <p className="text-xs text-gray-500">
                                {subscription.institution?.type || "N/A"}, {subscription.institution?.location || "N/A"}
                              </p>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">{subscription.plan}</Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                {subscription.status === "Active" ? (
                                  <CheckCircle className="h-4 w-4 text-emerald-500 mr-1" />
                                ) : subscription.status === "Pending" ? (
                                  <AlertCircle className="h-4 w-4 text-amber-500 mr-1" />
                                ) : (
                                  <AlertCircle className="h-4 w-4 text-red-500 mr-1" />
                                )}
                                <Badge
                                  variant={
                                    subscription.status === "Active"
                                      ? "default"
                                      : subscription.status === "Pending"
                                        ? "outline"
                                        : "destructive"
                                  }
                                >
                                  {subscription.status}
                                </Badge>
                              </div>
                            </TableCell>
                            <TableCell>{subscription.startDate}</TableCell>
                            <TableCell>{subscription.endDate}</TableCell>
                            <TableCell>
                              K{subscription.amount}/{(subscription.billingCycle || "Monthly").toLowerCase()}
                            </TableCell>
                            <TableCell>{subscription.nextPayment}</TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Actions</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>View Details</DropdownMenuItem>
                                  <DropdownMenuItem>Change Plan</DropdownMenuItem>
                                  {subscription.status === "Pending" && <DropdownMenuItem>Approve</DropdownMenuItem>}
                                  {subscription.status === "Active" && (
                                    <DropdownMenuItem>Cancel Subscription</DropdownMenuItem>
                                  )}
                                  <DropdownMenuItem>Payment History</DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payment History</CardTitle>
              <CardDescription>View all payment transactions</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <p>Loading payment history...</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Invoice</TableHead>
                        <TableHead>Institution</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Plan</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Payment Method</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {payments.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center h-24">
                            No payment history found
                          </TableCell>
                        </TableRow>
                      ) : (
                        payments.map((payment: any) => (
                          <TableRow key={payment.id}>
                            <TableCell className="font-medium">{payment.transactionId || payment.invoice}</TableCell>
                            <TableCell>
                              <Link
                                href={`/super-admin/institutions/${payment.institutionId || payment.institution?.id}`}
                                className="text-emerald-600 hover:underline"
                              >
                                {payment.institutionName || payment.institution?.name}
                              </Link>
                            </TableCell>
                            <TableCell>{payment.date}</TableCell>
                            <TableCell>K{payment.amount}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{payment.plan || "N/A"}</Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant={payment.status === "Paid" || payment.status === "Successful" ? "default" : "destructive"}>
                                {payment.status}
                              </Badge>
                            </TableCell>
                            <TableCell>{payment.method}</TableCell>
                            <TableCell className="text-right">
                              <Button variant="ghost" size="sm">
                                <Download className="h-4 w-4 mr-1" />
                                Invoice
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="plans" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Subscription Plans</CardTitle>
                <CardDescription>Manage available subscription plans</CardDescription>
              </div>
              <Link href="/super-admin/subscriptions/plans/new">
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Plan
                </Button>
              </Link>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                {plans.map((plan) => (
                  <Card key={plan.id} className={plan.popular ? "border-emerald-500" : ""}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <CardTitle>{plan.name}</CardTitle>
                        {plan.popular && <Badge>Most Popular</Badge>}
                      </div>
                      <CardDescription>{plan.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="mb-4">
                        <span className="text-3xl font-bold">{plan.price || `K${plan.monthlyPrice}`}</span>
                        <span className="text-gray-500"> {plan.period || "per month"}</span>
                      </div>
                      <div className="space-y-2">
                        {(plan.features || []).map((feature: string, index: number) => (
                          <div key={index} className="flex items-center">
                            <CheckCircle className="h-4 w-4 text-emerald-500 mr-2" />
                            <span className="text-sm">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                    <CardFooter className="flex flex-col gap-2">
                      <div className="text-sm text-gray-500 w-full">
                        <span className="font-medium">Storage:</span> {plan.storage || "N/A"}
                      </div>
                      <Link href={`/super-admin/subscriptions/plans/${plan.id}/edit`} className="w-full">
                        <Button variant="outline" className="w-full">
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Plan
                        </Button>
                      </Link>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
