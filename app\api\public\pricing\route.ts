import { NextResponse } from "next/server"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function GET() {
  try {
    const response = await fetch(`${BACKEND_URL}/api/v1/content/pricing`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      next: { revalidate: 300 }, // Cache for 5 minutes
    })

    if (!response.ok) {
      throw new Error("Failed to fetch pricing plans")
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching pricing plans:", error)
    // Return fallback data
    return NextResponse.json({
      success: true,
      data: [
        {
          id: 1,
          name: "Starter",
          description: "Perfect for small schools",
          price_monthly: 29,
          price_yearly: 290,
          currency: "USD",
          features: ["Up to 100 students", "Basic reporting", "Email support"],
          max_students: 100,
          max_teachers: 10,
          is_popular: false,
        },
        {
          id: 2,
          name: "Professional",
          description: "For growing institutions",
          price_monthly: 79,
          price_yearly: 790,
          currency: "USD",
          features: ["Up to 500 students", "Advanced reporting", "Priority support"],
          max_students: 500,
          max_teachers: 50,
          is_popular: true,
        },
      ],
    })
  }
}
