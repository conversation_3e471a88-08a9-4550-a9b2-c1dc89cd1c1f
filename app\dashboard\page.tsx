"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Users,
  GraduationCap,
  UserCircle,
  CreditCard,
  Calendar,
  TrendingUp,
  AlertCircle,
  CheckCircle,
} from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON><PERSON>, LineChart } from "@/components/dashboard/charts"
import { useToast } from "@/hooks/use-toast"

interface DashboardStats {
  students: number
  teachers: number
  staff: number
  classes: number
  subjects: number
  attendanceRate: number
  upcomingExams: number
  pendingFees: number
}

interface Activity {
  id: string
  type: string
  title: string
  description: string
  timestamp: string
  data?: any
}

function DashboardContent() {
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState<DashboardStats>({
    students: 0,
    teachers: 0,
    staff: 0,
    classes: 0,
    subjects: 0,
    attendanceRate: 0,
    upcomingExams: 0,
    pendingFees: 0,
  })
  const [activities, setActivities] = useState<Activity[]>([])
  const { toast } = useToast()

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true)
        
        // Use mock data for now to avoid API issues
        const mockStats = {
          students: 1250,
          teachers: 45,
          staff: 12,
          classes: 32,
          subjects: 15,
          attendanceRate: 94.5,
          upcomingExams: 8,
          pendingFees: 23,
        }
        
        const mockActivities = [
          {
            id: '1',
            type: 'enrollment',
            title: 'New Student Enrollment',
            description: 'John Doe enrolled in Grade 10A',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '2',
            type: 'attendance',
            title: 'Attendance Recorded',
            description: 'Grade 8B attendance marked for today',
            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '3',
            type: 'grade',
            title: 'Grades Updated',
            description: 'Mathematics grades posted for Grade 9',
            timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          },
          {
            id: '4',
            type: 'fee',
            title: 'Fee Payment',
            description: 'Sarah Wilson paid term fees',
            timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
          },
        ]

        setStats(mockStats)
        setActivities(mockActivities)

      } catch (error) {
        console.error('Error fetching dashboard data:', error)
        toast({
          title: "Error",
          description: "Failed to load dashboard data. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-gray-500">Welcome back to your school management dashboard.</p>
        </div>
        <div className="flex gap-2">
          <Button>
            <Calendar className="mr-2 h-4 w-4" />
            Academic Calendar
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <GraduationCap className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "Loading..." : stats.students.toLocaleString()}</div>
            <p className="text-xs text-gray-500">
              <TrendingUp className="inline h-3 w-3 text-emerald-500 mr-1" />
              <span className="text-emerald-500 font-medium">Active students</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Teachers</CardTitle>
            <UserCircle className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "Loading..." : stats.teachers}</div>
            <p className="text-xs text-gray-500">
              <span className="text-blue-500 font-medium">{stats.classes}</span> classes managed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Attendance Rate</CardTitle>
            <Calendar className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "Loading..." : `${stats.attendanceRate.toFixed(1)}%`}</div>
            <Progress value={isLoading ? 0 : stats.attendanceRate} className="h-2 mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Exams</CardTitle>
            <CreditCard className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "Loading..." : stats.upcomingExams}</div>
            <div className="flex items-center justify-between mt-2">
              <span className="text-xs text-gray-500">
                {stats.pendingFees} pending fees
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="lg:col-span-4">
              <CardHeader>
                <CardTitle>Enrollment Trends</CardTitle>
                <CardDescription>Student enrollment over the past academic year</CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <LineChart />
              </CardContent>
            </Card>

            <Card className="lg:col-span-3">
              <CardHeader>
                <CardTitle>Performance by Class</CardTitle>
                <CardDescription>Average grades by classroom</CardDescription>
              </CardHeader>
              <CardContent>
                <BarChart />
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activities</CardTitle>
                <CardDescription>Latest updates from your school</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {isLoading ? (
                    Array.from({ length: 4 }).map((_, i) => (
                      <div key={i} className="flex items-start animate-pulse">
                        <div className="mr-3 w-5 h-5 bg-gray-200 rounded"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                        </div>
                      </div>
                    ))
                  ) : activities.length > 0 ? (
                    activities.slice(0, 4).map((activity) => {
                      const getActivityIcon = (type: string) => {
                        switch (type) {
                          case 'enrollment':
                            return <Users className="h-5 w-5 text-blue-500" />
                          case 'attendance':
                            return <Calendar className="h-5 w-5 text-green-500" />
                          case 'grade':
                            return <CheckCircle className="h-5 w-5 text-emerald-500" />
                          case 'fee':
                            return <CreditCard className="h-5 w-5 text-amber-500" />
                          default:
                            return <AlertCircle className="h-5 w-5 text-gray-500" />
                        }
                      }

                      const formatTime = (timestamp: string) => {
                        const date = new Date(timestamp)
                        const now = new Date()
                        const diffMs = now.getTime() - date.getTime()
                        const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
                        const diffDays = Math.floor(diffHours / 24)

                        if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
                        if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
                        return 'Just now'
                      }

                      return (
                        <div key={activity.id} className="flex items-start">
                          <div className="mr-3">{getActivityIcon(activity.type)}</div>
                          <div className="flex-1">
                            <p className="font-medium text-sm">{activity.title}</p>
                            <p className="text-xs text-gray-500 truncate">{activity.description}</p>
                            <p className="text-xs text-gray-400 mt-1">{formatTime(activity.timestamp)}</p>
                          </div>
                        </div>
                      )
                    })
                  ) : (
                    <div className="text-center text-gray-500 py-4">
                      <AlertCircle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p className="text-sm">No recent activities</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Upcoming Events</CardTitle>
                <CardDescription>School events in the next 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      date: "May 25",
                      title: "End of Term Exams",
                      type: "Exam",
                    },
                    {
                      date: "May 30",
                      title: "Sports Day",
                      type: "Event",
                    },
                    {
                      date: "June 10",
                      title: "Graduation Ceremony",
                      type: "Ceremony",
                    },
                    {
                      date: "June 15",
                      title: "School Holiday",
                      type: "Holiday",
                    },
                  ].map((event, i) => (
                    <div key={i} className="flex items-start">
                      <div className="mr-3 bg-emerald-100 text-emerald-700 px-2 py-1 rounded text-xs font-medium">
                        {event.date}
                      </div>
                      <div>
                        <p className="font-medium">{event.title}</p>
                        <p className="text-sm text-gray-500">{event.type}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Alerts & Notifications</CardTitle>
                <CardDescription>Important items requiring attention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      icon: <AlertCircle className="h-5 w-5 text-red-500" />,
                      title: "5 students with attendance below 75%",
                      action: "Review",
                    },
                    {
                      icon: <AlertCircle className="h-5 w-5 text-amber-500" />,
                      title: "Term fee payment deadline approaching",
                      action: "Send Reminder",
                    },
                    {
                      icon: <AlertCircle className="h-5 w-5 text-amber-500" />,
                      title: "3 teacher leave requests pending",
                      action: "Approve",
                    },
                    {
                      icon: <AlertCircle className="h-5 w-5 text-blue-500" />,
                      title: "System maintenance scheduled",
                      action: "View Details",
                    },
                  ].map((alert, i) => (
                    <div key={i} className="flex items-start justify-between">
                      <div className="flex items-start">
                        <div className="mr-3">{alert.icon}</div>
                        <p className="font-medium">{alert.title}</p>
                      </div>
                      <Button variant="ghost" size="sm" className="text-xs">
                        {alert.action}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Detailed Analytics</CardTitle>
              <CardDescription>Comprehensive data analysis will be displayed here</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-40 border rounded bg-gray-50">
                <p className="text-gray-500">Analytics content will be implemented here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Generated Reports</CardTitle>
              <CardDescription>Access and download various school reports</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-40 border rounded bg-gray-50">
                <p className="text-gray-500">Reports content will be implemented here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default function DashboardPage() {
  return <DashboardContent />
}
