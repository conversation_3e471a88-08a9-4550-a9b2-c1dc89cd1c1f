const express = require('express');
const { body, param, query } = require('express-validator');
const router = express.Router();
const { 
  getAttendance, 
  getAttendanceById, 
  createAttendanceRecord, 
  updateAttendanceRecord, 
  deleteAttendanceRecord, 
  getAttendanceStats,
  getStudentAttendanceSummary,
  bulkMarkAttendance
} = require('./attendance.controller');
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const { validateRequest } = require('../../../middleware/validateRequest');

// Apply authentication to all routes
router.use(authenticate);

/**
 * @route GET /api/v1/attendance
 * @desc Get attendance records with filtering and pagination
 * @access Private (Admin, Teacher, Staff)
 */
router.get('/', getAttendance);

/**
 * @route GET /api/v1/attendance/stats
 * @desc Get attendance statistics
 * @access Private (Admin, Teacher)
 */
router.get('/stats', restrictTo('SUPER_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'), getAttendanceStats);

/**
 * @route GET /api/v1/attendance/student/:studentId/summary
 * @desc Get student attendance summary
 * @access Private (Admin, Teacher, Staff)
 */
router.get(
  '/student/:studentId/summary', 
  param('studentId').isUUID().withMessage('Invalid student ID'),
  validateRequest,
  getStudentAttendanceSummary
);

/**
 * @route POST /api/v1/attendance/bulk
 * @desc Bulk mark attendance for multiple students
 * @access Private (Admin, Teacher)
 */
router.post(
  '/bulk',
  [
    body('classId').isUUID().withMessage('Valid class ID is required'),
    body('date').isISO8601().withMessage('Valid date is required'),
    body('attendanceRecords').isArray().withMessage('Attendance records must be an array'),
    body('attendanceRecords.*.studentId').isUUID().withMessage('Valid student ID is required'),
    body('attendanceRecords.*.status').isIn(['PRESENT', 'ABSENT', 'LATE', 'EXCUSED']).withMessage('Valid status is required'),
    validateRequest
  ],
  restrictTo('SUPER_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'),
  bulkMarkAttendance
);

/**
 * @route GET /api/v1/attendance/:id
 * @desc Get attendance record by ID
 * @access Private (Admin, Teacher, Staff)
 */
router.get(
  '/:id',
  param('id').isUUID().withMessage('Invalid attendance record ID'),
  validateRequest,
  getAttendanceById
);

/**
 * @route POST /api/v1/attendance
 * @desc Create new attendance record
 * @access Private (Admin, Teacher)
 */
router.post(
  '/',
  [
    body('studentId').isUUID().withMessage('Valid student ID is required'),
    body('classId').isUUID().withMessage('Valid class ID is required'),
    body('date').isISO8601().withMessage('Valid date is required'),
    body('status').isIn(['PRESENT', 'ABSENT', 'LATE', 'EXCUSED']).withMessage('Valid status is required'),
    body('notes').optional().isString().withMessage('Notes must be a string'),
    validateRequest
  ],
  restrictTo('SUPER_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'),
  createAttendanceRecord
);

/**
 * @route PUT /api/v1/attendance/:id
 * @desc Update attendance record
 * @access Private (Admin, Teacher)
 */
router.put(
  '/:id',
  [
    param('id').isUUID().withMessage('Invalid attendance record ID'),
    body('status').optional().isIn(['PRESENT', 'ABSENT', 'LATE', 'EXCUSED']).withMessage('Valid status is required'),
    body('notes').optional().isString().withMessage('Notes must be a string'),
    validateRequest
  ],
  restrictTo('SUPER_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'),
  updateAttendanceRecord
);

/**
 * @route DELETE /api/v1/attendance/:id
 * @desc Delete attendance record
 * @access Private (Admin, Teacher)
 */
router.delete(
  '/:id',
  param('id').isUUID().withMessage('Invalid attendance record ID'),
  validateRequest,
  restrictTo('SUPER_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'),
  deleteAttendanceRecord
);

module.exports = router;
