const http = require('http');

function testBackend() {
  console.log('🔍 Testing if backend is responding...');
  
  const options = {
    hostname: 'localhost',
    port: 4000,
    path: '/api/v1/classes',
    method: 'GET'
  };
  
  const req = http.request(options, (res) => {
    console.log(`✅ Backend responded with status: ${res.statusCode}`);
    
    if (res.statusCode === 401) {
      console.log('   🔒 Authentication required (expected)');
    } else if (res.statusCode === 200) {
      console.log('   📊 Classes endpoint accessible');
    }
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const jsonData = JSON.parse(data);
        console.log('   📄 Response data:', JSON.stringify(jsonData, null, 2));
      } catch (e) {
        console.log('   📄 Response (raw):', data);
      }
    });
  });
  
  req.on('error', (err) => {
    console.error('❌ Backend connection failed:', err.message);
  });
  
  req.end();
}

testBackend();
