const express = require('express');
const { body, param } = require('express-validator');
const schoolController = require('./school.controller');
const { validateRequest } = require('../../../middleware/validateRequest');
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const router = express.Router();

/**
 * @route GET /api/v1/schools
 * @desc Get all schools (with pagination and filtering)
 * @access Private
 */
router.get(
  '/',
  authenticate,
  schoolController.getSchools
);

/**
 * @route GET /api/v1/schools/:id
 * @desc Get school by ID
 * @access Private
 */
router.get(
  '/:id',
  authenticate,
  param('id').isUUID().withMessage('Invalid school ID'),
  validateRequest,
  schoolController.getSchoolById
);

/**
 * @route POST /api/v1/schools
 * @desc Create new school
 * @access Private (Admin)
 */
router.post(
  '/',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN'),
  [
    body('name').notEmpty().withMessage('School name is required'),
    body('type').optional().isIn(['PRIMARY', 'SECONDARY', 'TERTIARY', 'KINDERGARTEN', 'VOCATIONAL', 'SPECIAL_NEEDS']).withMessage('Invalid school type'),
    body('institutionId').isUUID().withMessage('Valid institution ID is required'),
    body('address').optional(),
    body('city').optional(),
    body('state').optional(),
    body('country').optional(),
    body('postalCode').optional(),
    body('phoneNumber').optional(),
    body('email').optional().isEmail().withMessage('Please provide a valid email'),
    body('website').optional().isURL().withMessage('Please provide a valid URL'),
    validateRequest,
  ],
  schoolController.createSchool
);

/**
 * @route PATCH /api/v1/schools/:id
 * @desc Update school
 * @access Private (Admin)
 */
router.patch(
  '/:id',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'),
  param('id').isUUID().withMessage('Invalid school ID'),
  [
    body('name').optional().notEmpty().withMessage('School name cannot be empty'),
    body('address').optional(),
    body('city').optional(),
    body('state').optional(),
    body('country').optional(),
    body('postalCode').optional(),
    body('phoneNumber').optional(),
    body('email').optional().isEmail().withMessage('Please provide a valid email'),
    body('website').optional().isURL().withMessage('Please provide a valid URL'),
    body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
    validateRequest,
  ],
  schoolController.updateSchool
);

/**
 * @route DELETE /api/v1/schools/:id
 * @desc Delete school
 * @access Private (Admin)
 */
router.delete(
  '/:id',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN'),
  param('id').isUUID().withMessage('Invalid school ID'),
  validateRequest,
  schoolController.deleteSchool
);

/**
 * @route GET /api/v1/schools/institution/:institutionId
 * @desc Get schools by institution
 * @access Private
 */
router.get(
  '/institution/:institutionId',
  authenticate,
  param('institutionId').isUUID().withMessage('Invalid institution ID'),
  validateRequest,
  schoolController.getSchoolsByInstitution
);

module.exports = router;
