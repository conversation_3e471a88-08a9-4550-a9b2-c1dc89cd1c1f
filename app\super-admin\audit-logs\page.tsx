"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Search,
  Download,
  Clock,
  User,
  Building,
  Shield,
  FileText,
  Database,
  Settings,
  AlertTriangle,
  Info,
} from "lucide-react"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { superAdminService } from "@/lib/backend-api"
import { useToast } from "@/hooks/use-toast"

// TypeScript interfaces
interface AuditLogUser {
  id: string
  name: string
  email: string
}

interface AuditLog {
  id: string
  action: string
  user: AuditLogUser
  entity: string | null
  entityType: string | null
  ip: string
  userAgent: string
  timestamp: string
  details: string
  category: string
  severity: 'Info' | 'Warning' | 'Error' | 'Critical'
}

interface PaginationState {
  currentPage: number
  totalPages: number
  totalCount: number
  pageSize: number
}

// Mock data for audit logs
const mockAuditLogs = [
  {
    id: "1",
    action: "User Login",
    user: {
      id: "1",
      name: "Super Admin",
      email: "<EMAIL>",
    },
    entity: null,
    entityType: null,
    ip: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    timestamp: "2025-05-22 09:15:12",
    details: "Successful login",
    category: "Authentication",
    severity: "Info",
  },
  {
    id: "2",
    action: "Institution Created",
    user: {
      id: "1",
      name: "Super Admin",
      email: "<EMAIL>",
    },
    entity: {
      id: "5",
      name: "Kabwe Primary School",
    },
    entityType: "Institution",
    ip: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    timestamp: "2025-05-22 10:30:45",
    details: "New institution created",
    category: "Data Management",
    severity: "Info",
  },
  {
    id: "3",
    action: "User Created",
    user: {
      id: "1",
      name: "Super Admin",
      email: "<EMAIL>",
    },
    entity: {
      id: "10",
      name: "James Tembo",
    },
    entityType: "User",
    ip: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    timestamp: "2025-05-22 11:05:22",
    details: "New admin user created for Kabwe Primary School",
    category: "User Management",
    severity: "Info",
  },
  {
    id: "4",
    action: "Failed Login Attempt",
    user: {
      id: null,
      name: null,
      email: "<EMAIL>",
    },
    entity: null,
    entityType: null,
    ip: "***********00",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    timestamp: "2025-05-22 12:15:30",
    details: "Multiple failed login attempts",
    category: "Authentication",
    severity: "Warning",
  },
  {
    id: "5",
    action: "Subscription Changed",
    user: {
      id: "1",
      name: "Super Admin",
      email: "<EMAIL>",
    },
    entity: {
      id: "3",
      name: "Ndola Academy",
    },
    entityType: "Institution",
    ip: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    timestamp: "2025-05-22 14:20:10",
    details: "Subscription upgraded from Standard to Premium",
    category: "Billing",
    severity: "Info",
  },
  {
    id: "6",
    action: "Security Settings Changed",
    user: {
      id: "1",
      name: "Super Admin",
      email: "<EMAIL>",
    },
    entity: null,
    entityType: null,
    ip: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    timestamp: "2025-05-22 15:45:33",
    details: "Two-factor authentication requirement enabled for all admin users",
    category: "Security",
    severity: "Important",
  },
  {
    id: "7",
    action: "Data Export",
    user: {
      id: "2",
      name: "Michael Banda",
      email: "<EMAIL>",
    },
    entity: {
      id: "1",
      name: "Lusaka Primary School",
    },
    entityType: "Institution",
    ip: "***********",
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
    timestamp: "2025-05-22 16:10:05",
    details: "Student data exported to CSV",
    category: "Data Management",
    severity: "Info",
  },
  {
    id: "8",
    action: "API Key Generated",
    user: {
      id: "1",
      name: "Super Admin",
      email: "<EMAIL>",
    },
    entity: null,
    entityType: null,
    ip: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    timestamp: "2025-05-22 16:55:20",
    details: "New API key generated for system integration",
    category: "API",
    severity: "Info",
  },
  {
    id: "9",
    action: "Unauthorized Access Attempt",
    user: {
      id: "3",
      name: "John Banda",
      email: "<EMAIL>",
    },
    entity: {
      id: "1",
      name: "Lusaka Primary School",
    },
    entityType: "Institution",
    ip: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    timestamp: "2025-05-22 17:30:15",
    details: "Attempted to access unauthorized institution data",
    category: "Security",
    severity: "Alert",
  },
  {
    id: "10",
    action: "System Settings Changed",
    user: {
      id: "1",
      name: "Super Admin",
      email: "<EMAIL>",
    },
    entity: null,
    entityType: null,
    ip: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    timestamp: "2025-05-22 18:05:40",
    details: "Email notification settings updated",
    category: "System",
    severity: "Info",
  },
]

export default function AuditLogsPage() {
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([])
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [categoryFilter, setCategoryFilter] = useState<string>("")
  const [severityFilter, setSeverityFilter] = useState<string>("")
  const [userFilter, setUserFilter] = useState<string>("")
  const [dateRange, setDateRange] = useState<{from: Date | undefined; to: Date | undefined}>({
    from: new Date(2025, 4, 15), // May 15, 2025
    to: new Date(2025, 4, 22), // May 22, 2025
  })
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 20
  })
  
  const { toast } = useToast()

  const fetchAuditLogs = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const params = {
        page: pagination.currentPage,
        limit: pagination.pageSize,
        ...(searchQuery && { search: searchQuery }),
        ...(categoryFilter && categoryFilter !== 'all' && { category: categoryFilter }),
        ...(severityFilter && severityFilter !== 'all' && { severity: severityFilter }),
        ...(userFilter && { userId: userFilter }),
        ...(dateRange.from && { startDate: dateRange.from.toISOString() }),
        ...(dateRange.to && { endDate: dateRange.to.toISOString() })
      }

      const response = await superAdminService.getAuditLogs(params)
      
      if (response.success && response.data) {
        setAuditLogs(response.data.logs || response.data || mockAuditLogs)
        setPagination(prev => ({
          ...prev,
          totalPages: Math.ceil((response.data?.total || 0) / prev.pageSize),
          totalCount: response.data?.total || 0
        }))
      } else {
        // Fallback to mock data on error
        setAuditLogs(mockAuditLogs)
        setPagination(prev => ({
          ...prev,
          totalPages: Math.ceil(mockAuditLogs.length / prev.pageSize),
          totalCount: mockAuditLogs.length
        }))
        
        if (response.error) {
          setError(response.error)
          toast({
            title: "Warning",
            description: "Failed to fetch audit logs. Using fallback data.",
            variant: "destructive",
          })
        }
      }
    } catch (err: any) {
      console.error('Error fetching audit logs:', err)
      setError(err?.message || 'Failed to fetch audit logs')
      
      // Fallback to mock data
      setAuditLogs(mockAuditLogs)
      setPagination(prev => ({
        ...prev,
        totalPages: Math.ceil(mockAuditLogs.length / prev.pageSize),
        totalCount: mockAuditLogs.length
      }))
      
      toast({
        title: "Error",
        description: "Failed to fetch audit logs. Using fallback data.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchAuditLogs()
  }, [pagination.currentPage, searchQuery, categoryFilter, severityFilter, userFilter, dateRange])

  const filteredLogs = auditLogs.filter((log: AuditLog) => {
    const matchesSearch =
      log.action?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.user?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.user?.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.details?.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory = categoryFilter ? log.category === categoryFilter : true
    const matchesSeverity = severityFilter ? log.severity === severityFilter : true
    const matchesUser = userFilter
      ? log.user?.id === userFilter || log.user?.email?.includes(userFilter)
      : true

    return matchesSearch && matchesCategory && matchesSeverity && matchesUser
  })

  const handleExportLogs = async () => {
    try {
      const response = await fetch('/api/super-admin/audit-logs?type=export')
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
      }
    } catch (error) {
      console.error('Error exporting logs:', error)
    }
  }

  const getCategoryIcon = (category) => {
    switch (category) {
      case "Authentication":
        return <User className="h-4 w-4" />
      case "Data Management":
        return <Database className="h-4 w-4" />
      case "User Management":
        return <User className="h-4 w-4" />
      case "Billing":
        return <FileText className="h-4 w-4" />
      case "Security":
        return <Shield className="h-4 w-4" />
      case "API":
        return <Settings className="h-4 w-4" />
      case "System":
        return <Settings className="h-4 w-4" />
      default:
        return <Info className="h-4 w-4" />
    }
  }

  const getSeverityBadge = (severity) => {
    switch (severity) {
      case "Info":
        return (
          <Badge variant="outline" className="flex w-fit items-center gap-1">
            <Info className="h-3 w-3" />
            Info
          </Badge>
        )
      case "Warning":
        return (
          <Badge variant="outline" className="flex w-fit items-center gap-1 border-yellow-500 text-yellow-500">
            <AlertTriangle className="h-3 w-3" />
            Warning
          </Badge>
        )
      case "Important":
        return (
          <Badge variant="outline" className="flex w-fit items-center gap-1 border-blue-500 text-blue-500">
            <Info className="h-3 w-3" />
            Important
          </Badge>
        )
      case "Alert":
        return (
          <Badge variant="destructive" className="flex w-fit items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            Alert
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="flex w-fit items-center gap-1">
            <Info className="h-3 w-3" />
            {severity}
          </Badge>
        )
    }
  }

  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Audit Logs</h1>
        <p className="text-muted-foreground">Track and monitor system activities and changes</p>
      </div>

      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Logs</TabsTrigger>
          <TabsTrigger value="authentication">Authentication</TabsTrigger>
          <TabsTrigger value="data">Data Changes</TabsTrigger>
          <TabsTrigger value="security">Security Events</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Audit Logs</CardTitle>
              <CardDescription>Comprehensive record of all system activities and changes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search logs..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        <SelectItem value="Authentication">Authentication</SelectItem>
                        <SelectItem value="Data Management">Data Management</SelectItem>
                        <SelectItem value="User Management">User Management</SelectItem>
                        <SelectItem value="Billing">Billing</SelectItem>
                        <SelectItem value="Security">Security</SelectItem>
                        <SelectItem value="API">API</SelectItem>
                        <SelectItem value="System">System</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select value={severityFilter} onValueChange={setSeverityFilter}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by severity" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Severities</SelectItem>
                        <SelectItem value="Info">Info</SelectItem>
                        <SelectItem value="Warning">Warning</SelectItem>
                        <SelectItem value="Important">Important</SelectItem>
                        <SelectItem value="Alert">Alert</SelectItem>
                      </SelectContent>
                    </Select>

                    <DateRangePicker date={dateRange} onDateChange={setDateRange} className="w-[280px]" />

                    <Button variant="outline" onClick={handleExportLogs}>
                      <Download className="mr-2 h-4 w-4" />
                      Export Logs
                    </Button>
                  </div>
                </div>

                {isLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <p>Loading audit logs...</p>
                  </div>
                ) : error ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="text-center">
                      <p className="text-red-500 mb-2">Error: {error}</p>
                      <Button onClick={fetchAuditLogs} variant="outline">
                        Retry
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Timestamp</TableHead>
                          <TableHead>Action</TableHead>
                          <TableHead>User</TableHead>
                          <TableHead>Entity</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Severity</TableHead>
                          <TableHead>IP Address</TableHead>
                          <TableHead className="text-right">Details</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredLogs.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={8} className="text-center h-24">
                              No audit logs found
                            </TableCell>
                          </TableRow>
                        ) : (
                          filteredLogs.map((log: any) => (
                            <TableRow key={log.id}>
                              <TableCell className="whitespace-nowrap">
                                <div className="flex items-center">
                                  <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                                  {log.timestamp}
                                </div>
                              </TableCell>
                              <TableCell className="font-medium">{log.action}</TableCell>
                              <TableCell>
                                {log.user?.name ? (
                                  <div>
                                    <div>{log.user.name}</div>
                                    <div className="text-xs text-muted-foreground">{log.user.email}</div>
                                  </div>
                                ) : (
                                  <span className="text-muted-foreground">{log.user?.email || log.userEmail || "Unknown"}</span>
                                )}
                              </TableCell>
                              <TableCell>
                                {log.entity ? (
                                  <div className="flex items-center">
                                    {log.entityType === "Institution" ? (
                                      <Building className="mr-1 h-4 w-4 text-muted-foreground" />
                                    ) : (
                                      <User className="mr-1 h-4 w-4 text-muted-foreground" />
                                    )}
                                    {log.entity.name || log.entityName}
                                  </div>
                                ) : (
                                  <span className="text-muted-foreground">N/A</span>
                                )}
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline" className="flex w-fit items-center gap-1">
                                  {getCategoryIcon(log.category)}
                                  {log.category}
                                </Badge>
                              </TableCell>
                              <TableCell>{getSeverityBadge(log.severity)}</TableCell>
                              <TableCell>{log.ip || log.ipAddress}</TableCell>
                              <TableCell className="text-right">
                                <Button variant="ghost" size="sm">
                                  View
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="authentication" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Authentication Logs</CardTitle>
              <CardDescription>Login attempts, password changes, and account activities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center items-center h-64">
                <div className="text-center">
                  <User className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p>Authentication logs will be displayed here</p>
                  <Button variant="outline" className="mt-4">
                    View All Authentication Logs
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Data Change Logs</CardTitle>
              <CardDescription>Records of data creation, modification, and deletion</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center items-center h-64">
                <div className="text-center">
                  <Database className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p>Data change logs will be displayed here</p>
                  <Button variant="outline" className="mt-4">
                    View All Data Change Logs
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Event Logs</CardTitle>
              <CardDescription>Security-related events and potential issues</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center items-center h-64">
                <div className="text-center">
                  <Shield className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p>Security event logs will be displayed here</p>
                  <Button variant="outline" className="mt-4">
                    View All Security Logs
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
