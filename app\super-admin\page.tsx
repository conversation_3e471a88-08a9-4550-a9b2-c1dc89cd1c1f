"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Building, Users, CreditCard, TrendingUp, CheckCircle, Clock, Shield, Plus } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON><PERSON>, LineChart } from "@/components/dashboard/charts"
import { Badge } from "@/components/ui/badge"
import { superAdminService } from "@/lib/backend-api"
import { BackendStatus, useBackendStatus } from "@/components/super-admin/backend-status"
import { OfflineDashboard } from "@/components/super-admin/offline-dashboard"

interface Activity {
  id: string;
  action: string;
  details: string;
  timestamp: string;
  user: string;
}

interface Institution {
  id: string;
  name: string;
  type: string;
  submittedDate: string;
  adminContact?: string;
}

export default function SuperAdminDashboardPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState({
    totalInstitutions: 0,
    activeInstitutions: 0,
    totalUsers: 0,
    totalRevenue: 0,
    pendingApprovals: 0,
    systemUptime: 0,
  })
  const [recentActivities, setRecentActivities] = useState<Activity[]>([])
  const [pendingInstitutions, setPendingInstitutions] = useState<Institution[]>([])
  const [institutionGrowth, setInstitutionGrowth] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)
  const backendStatus = useBackendStatus()

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true)
        
        // Fetch dashboard statistics using backend API service
        const dashboardResult = await superAdminService.getDashboardAnalytics()
        
        if (dashboardResult.success) {
          setStats(dashboardResult.data.stats)
          setRecentActivities(dashboardResult.data.recentActivities || [])
          setInstitutionGrowth(dashboardResult.data.institutionGrowth || [])
        } else {
          throw new Error(dashboardResult.error || 'Failed to fetch dashboard data')
        }

        // Fetch pending approvals
        const pendingResult = await superAdminService.getPendingInstitutions()
        
        if (pendingResult.success) {
          setPendingInstitutions(pendingResult.data || [])
        }
        
      } catch (err: any) {
        console.error('Dashboard API error:', err)
        setError(err?.message || 'An error occurred while fetching dashboard data')
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[60vh] text-red-600">
        {error}
      </div>
    )
  }

  // Show offline dashboard if backend is not available
  if (backendStatus === false) {
    return (
      <div className="space-y-6">
        <BackendStatus />
        <OfflineDashboard />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <BackendStatus />
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Super Admin Dashboard</h1>
          <p className="text-gray-500">System-wide overview and management</p>
        </div>
        <div className="flex gap-2">
          <Link href="/super-admin/institutions/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Institution
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Institutions</CardTitle>
            <Building className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "Loading..." : stats.totalInstitutions}</div>
            <p className="text-xs text-gray-500">
              <span className="text-emerald-500 font-medium">{stats.activeInstitutions} active</span> (
              {Math.round((stats.activeInstitutions / stats.totalInstitutions) * 100)}%)
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "Loading..." : stats.totalUsers.toLocaleString()}</div>
            <p className="text-xs text-gray-500">
              <TrendingUp className="inline h-3 w-3 text-emerald-500 mr-1" />
              <span className="text-emerald-500 font-medium">+12.5%</span> from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <CreditCard className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? "Loading..." : `K${stats.totalRevenue.toLocaleString()}`}
            </div>
            <p className="text-xs text-gray-500">
              <TrendingUp className="inline h-3 w-3 text-emerald-500 mr-1" />
              <span className="text-emerald-500 font-medium">+8.2%</span> from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
            <Shield className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "Loading..." : `${stats.systemUptime}%`}</div>
            <Progress value={isLoading ? 0 : stats.systemUptime} className="h-2 mt-2" />
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="institutions">Institutions</TabsTrigger>
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="lg:col-span-4">
              <CardHeader>
                <CardTitle>Institution Growth</CardTitle>
                <CardDescription>New institutions registered over time</CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <LineChart />
              </CardContent>
            </Card>

            <Card className="lg:col-span-3">
              <CardHeader>
                <CardTitle>Subscription Distribution</CardTitle>
                <CardDescription>Breakdown by subscription plan</CardDescription>
              </CardHeader>
              <CardContent>
                <BarChart />
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activities</CardTitle>
                <CardDescription>Latest system events</CardDescription>
              </CardHeader>
              <CardContent>
                {error && (
                  <div className="text-red-500 text-sm mb-4">
                    Error loading data: {error}
                  </div>
                )}
                <div className="space-y-4">
                  {isLoading ? (
                    <div className="space-y-4">
                      {[1, 2, 3, 4].map((i) => (
                        <div key={i} className="flex items-start animate-pulse">
                          <div className="w-5 h-5 bg-gray-200 rounded mr-3"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : recentActivities.length > 0 ? (
                    recentActivities.slice(0, 4).map((activity) => (
                      <div key={activity.id} className="flex items-start">
                        <div className="mr-3">
                          {activity.action.includes('APPROVED') ? (
                            <CheckCircle className="h-5 w-5 text-emerald-500" />
                          ) : activity.action.includes('USER') ? (
                            <Users className="h-5 w-5 text-blue-500" />
                          ) : activity.action.includes('PAYMENT') ? (
                            <CreditCard className="h-5 w-5 text-purple-500" />
                          ) : (
                            <Shield className="h-5 w-5 text-amber-500" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{activity.details}</p>
                          <p className="text-sm text-gray-500">
                            {new Date(activity.timestamp).toLocaleString()} • {activity.user}
                          </p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-center justify-center h-20 text-gray-500">
                      No recent activities
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Pending Approvals</CardTitle>
                <CardDescription>Institutions awaiting approval</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex items-start justify-between animate-pulse">
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                          <div className="flex items-center gap-2">
                            <div className="h-6 bg-gray-200 rounded w-20"></div>
                            <div className="h-3 bg-gray-200 rounded w-16"></div>
                          </div>
                        </div>
                        <div className="h-8 bg-gray-200 rounded w-16"></div>
                      </div>
                    ))}
                  </div>
                ) : pendingInstitutions.length === 0 ? (
                  <div className="flex items-center justify-center h-40 border rounded bg-gray-50">
                    <p className="text-gray-500">No pending approvals</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {pendingInstitutions.slice(0, 3).map((institution) => (
                      <div key={institution.id} className="flex items-start justify-between">
                        <div>
                          <p className="font-medium">{institution.name}</p>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{institution.type}</Badge>
                            <p className="text-xs text-gray-500">
                              {new Date(institution.submittedDate).toLocaleDateString()}
                            </p>
                          </div>
                          {institution.adminContact && (
                            <p className="text-xs text-gray-500 mt-1">
                              Contact: {institution.adminContact}
                            </p>
                          )}
                        </div>
                        <Link href={`/super-admin/institutions/${institution.id}`}>
                          <Button size="sm">Review</Button>
                        </Link>
                      </div>
                    ))}
                    {pendingInstitutions.length > 3 && (
                      <div className="pt-2 border-t">
                        <Link href="/super-admin/institutions?filter=pending">
                          <Button variant="outline" size="sm" className="w-full">
                            View All {pendingInstitutions.length} Pending
                          </Button>
                        </Link>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Status</CardTitle>
                <CardDescription>Current system health and alerts</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      icon: <CheckCircle className="h-5 w-5 text-emerald-500" />,
                      title: "API Services",
                      status: "Operational",
                    },
                    {
                      icon: <CheckCircle className="h-5 w-5 text-emerald-500" />,
                      title: "Database",
                      status: "Operational",
                    },
                    {
                      icon: <CheckCircle className="h-5 w-5 text-emerald-500" />,
                      title: "Storage",
                      status: "Operational",
                    },
                    {
                      icon: <Clock className="h-5 w-5 text-amber-500" />,
                      title: "Backup Service",
                      status: "Maintenance",
                    },
                  ].map((service, i) => (
                    <div key={i} className="flex items-start justify-between">
                      <div className="flex items-start">
                        <div className="mr-3">{service.icon}</div>
                        <p className="font-medium">{service.title}</p>
                      </div>
                      <Badge variant={service.status === "Operational" ? "default" : "outline"}>{service.status}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="institutions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Institution Management</CardTitle>
              <CardDescription>View and manage all registered institutions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-40 border rounded bg-gray-50">
                <Link href="/super-admin/institutions">
                  <Button>
                    <Building className="mr-2 h-4 w-4" />
                    View All Institutions
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscriptions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Subscription Management</CardTitle>
              <CardDescription>View and manage subscription plans and payments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-40 border rounded bg-gray-50">
                <Link href="/super-admin/subscriptions">
                  <Button>
                    <CreditCard className="mr-2 h-4 w-4" />
                    Manage Subscriptions
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
