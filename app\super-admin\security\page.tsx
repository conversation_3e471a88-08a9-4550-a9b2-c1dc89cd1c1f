"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  Shield,
  Lock,
  Key,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Eye,
  Download,
  Clock,
  Search,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

// Define interfaces for type safety
interface SecuritySettings {
  twoFactorAuth: boolean
  passwordExpiry: number
  minPasswordLength: number
  requireSpecialChars: boolean
  requireNumbers: boolean
  requireUppercase: boolean
  maxLoginAttempts: number
  sessionTimeout: number
  ipRestriction: boolean
  allowedIPs: string
}

interface SecurityLog {
  id: string
  event: string
  user: string
  ip: string
  location: string
  timestamp: string
  status: string
}

export default function SecurityPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorAuth: false,
    passwordExpiry: 90,
    minPasswordLength: 8,
    requireSpecialChars: true,
    requireNumbers: true,
    requireUppercase: true,
    maxLoginAttempts: 5,
    sessionTimeout: 30,
    ipRestriction: false,
    allowedIPs: "",
  })
  const [securityLogs, setSecurityLogs] = useState<SecurityLog[]>([])
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    const fetchSecurityData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Fetch security settings
        const settingsResponse = await fetch('/api/super-admin/security?endpoint=settings')
        if (settingsResponse.ok) {
          const settingsResult = await settingsResponse.json()
          if (settingsResult.success) {
            setSecuritySettings(settingsResult.data)
          }
        }

        // Fetch security logs
        const logsResponse = await fetch('/api/super-admin/security?endpoint=logs')
        if (logsResponse.ok) {
          const logsResult = await logsResponse.json()
          if (logsResult.success) {
            setSecurityLogs(logsResult.data || [])
          }
        }

      } catch (err: any) {
        console.error('Security API error:', err)
        setError(err?.message || 'Failed to fetch security data')
      } finally {
        setIsLoading(false)
      }
    }

    fetchSecurityData()
  }, [])

  const handleSettingChange = (setting: keyof SecuritySettings, value: any) => {
    setSecuritySettings({
      ...securitySettings,
      [setting]: value,
    })

    toast({
      title: "Setting updated",
      description: "Security setting has been updated successfully.",
    })
  }

  const handleSaveSettings = () => {
    // Simulate API call to save settings
    toast({
      title: "Settings saved",
      description: "All security settings have been saved successfully.",
    })
  }

  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Security Settings</h1>
        <p className="text-muted-foreground">Manage system-wide security settings and policies</p>
      </div>

      <Tabs defaultValue="settings" className="space-y-4">
        <TabsList>
          <TabsTrigger value="settings">Security Settings</TabsTrigger>
          <TabsTrigger value="logs">Security Logs</TabsTrigger>
          <TabsTrigger value="api">API Security</TabsTrigger>
        </TabsList>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lock className="mr-2 h-5 w-5" />
                Authentication Settings
              </CardTitle>
              <CardDescription>Configure authentication and password policies</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="two-factor">Two-factor authentication</Label>
                    <p className="text-sm text-muted-foreground">
                      Require two-factor authentication for all admin users
                    </p>
                  </div>
                  <Switch
                    id="two-factor"
                    checked={securitySettings.twoFactorAuth}
                    onCheckedChange={(checked) => handleSettingChange("twoFactorAuth", checked)}
                  />
                </div>

                <Separator />

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="password-expiry">Password expiry (days)</Label>
                    <Input
                      id="password-expiry"
                      type="number"
                      value={securitySettings.passwordExpiry}
                      onChange={(e) => handleSettingChange("passwordExpiry", Number.parseInt(e.target.value))}
                    />
                    <p className="text-sm text-muted-foreground">
                      Number of days before passwords expire (0 for never)
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="min-password-length">Minimum password length</Label>
                    <Input
                      id="min-password-length"
                      type="number"
                      value={securitySettings.minPasswordLength}
                      onChange={(e) => handleSettingChange("minPasswordLength", Number.parseInt(e.target.value))}
                    />
                    <p className="text-sm text-muted-foreground">Minimum number of characters required</p>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="require-special"
                      checked={securitySettings.requireSpecialChars}
                      onCheckedChange={(checked) => handleSettingChange("requireSpecialChars", checked)}
                    />
                    <Label htmlFor="require-special">Require special characters</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="require-numbers"
                      checked={securitySettings.requireNumbers}
                      onCheckedChange={(checked) => handleSettingChange("requireNumbers", checked)}
                    />
                    <Label htmlFor="require-numbers">Require numbers</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="require-uppercase"
                      checked={securitySettings.requireUppercase}
                      onCheckedChange={(checked) => handleSettingChange("requireUppercase", checked)}
                    />
                    <Label htmlFor="require-uppercase">Require uppercase letters</Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5" />
                Access Control Settings
              </CardTitle>
              <CardDescription>Configure login and session security</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="max-login-attempts">Maximum login attempts</Label>
                    <Input
                      id="max-login-attempts"
                      type="number"
                      value={securitySettings.maxLoginAttempts}
                      onChange={(e) => handleSettingChange("maxLoginAttempts", Number.parseInt(e.target.value))}
                    />
                    <p className="text-sm text-muted-foreground">Number of failed attempts before account is locked</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="session-timeout">Session timeout (minutes)</Label>
                    <Input
                      id="session-timeout"
                      type="number"
                      value={securitySettings.sessionTimeout}
                      onChange={(e) => handleSettingChange("sessionTimeout", Number.parseInt(e.target.value))}
                    />
                    <p className="text-sm text-muted-foreground">
                      Inactive time before user is automatically logged out
                    </p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="ip-restriction">IP address restriction</Label>
                      <p className="text-sm text-muted-foreground">Restrict admin access to specific IP addresses</p>
                    </div>
                    <Switch
                      id="ip-restriction"
                      checked={securitySettings.ipRestriction}
                      onCheckedChange={(checked) => handleSettingChange("ipRestriction", checked)}
                    />
                  </div>

                  {securitySettings.ipRestriction && (
                    <div className="mt-2">
                      <Label htmlFor="allowed-ips">Allowed IP addresses</Label>
                      <Input
                        id="allowed-ips"
                        placeholder="e.g. ***********, ********"
                        value={securitySettings.allowedIPs}
                        onChange={(e) => handleSettingChange("allowedIPs", e.target.value)}
                      />
                      <p className="text-sm text-muted-foreground mt-1">
                        Enter comma-separated IP addresses or CIDR ranges
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings}>Save All Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="mr-2 h-5 w-5" />
                Security Audit Logs
              </CardTitle>
              <CardDescription>Review security-related events and activities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center mb-4">
                <div className="relative w-64">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search logs..." className="pl-8" />
                </div>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export Logs
                </Button>
              </div>

              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <p>Loading security logs...</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Event</TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>IP Address</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Timestamp</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {securityLogs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell>{log.event}</TableCell>
                          <TableCell>{log.user}</TableCell>
                          <TableCell>{log.ip}</TableCell>
                          <TableCell>{log.location}</TableCell>
                          <TableCell>{log.timestamp}</TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                log.status === "Success"
                                  ? "default"
                                  : log.status === "Warning"
                                    ? "outline"
                                    : "destructive"
                              }
                              className="flex w-fit items-center gap-1"
                            >
                              {log.status === "Success" ? (
                                <CheckCircle className="h-3 w-3" />
                              ) : log.status === "Warning" ? (
                                <AlertTriangle className="h-3 w-3" />
                              ) : (
                                <XCircle className="h-3 w-3" />
                              )}
                              {log.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="api" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Key className="mr-2 h-5 w-5" />
                API Security
              </CardTitle>
              <CardDescription>Manage API keys and access controls</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Important</AlertTitle>
                <AlertDescription>
                  API keys grant programmatic access to the system. Keep your keys secure and never share them publicly.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Active API Keys</h3>
                  <Button>
                    <Key className="mr-2 h-4 w-4" />
                    Generate New Key
                  </Button>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Description</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead>Last Used</TableHead>
                        <TableHead>Expires</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>System Integration Key</TableCell>
                        <TableCell>May 15, 2025</TableCell>
                        <TableCell>May 22, 2025</TableCell>
                        <TableCell>May 15, 2026</TableCell>
                        <TableCell>
                          <Badge variant="default" className="flex w-fit items-center gap-1">
                            <CheckCircle className="h-3 w-3" />
                            Active
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">View</span>
                            </Button>
                            <Button variant="outline" size="sm">
                              <RefreshCw className="h-4 w-4" />
                              <span className="sr-only">Rotate</span>
                            </Button>
                            <Button variant="outline" size="sm" className="text-red-500">
                              <XCircle className="h-4 w-4" />
                              <span className="sr-only">Revoke</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Reporting API Key</TableCell>
                        <TableCell>April 10, 2025</TableCell>
                        <TableCell>May 21, 2025</TableCell>
                        <TableCell>April 10, 2026</TableCell>
                        <TableCell>
                          <Badge variant="default" className="flex w-fit items-center gap-1">
                            <CheckCircle className="h-3 w-3" />
                            Active
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">View</span>
                            </Button>
                            <Button variant="outline" size="sm">
                              <RefreshCw className="h-4 w-4" />
                              <span className="sr-only">Rotate</span>
                            </Button>
                            <Button variant="outline" size="sm" className="text-red-500">
                              <XCircle className="h-4 w-4" />
                              <span className="sr-only">Revoke</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Development Key</TableCell>
                        <TableCell>March 5, 2025</TableCell>
                        <TableCell>May 10, 2025</TableCell>
                        <TableCell>June 5, 2025</TableCell>
                        <TableCell>
                          <Badge variant="outline" className="flex w-fit items-center gap-1">
                            <AlertTriangle className="h-3 w-3" />
                            Expiring Soon
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">View</span>
                            </Button>
                            <Button variant="outline" size="sm">
                              <RefreshCw className="h-4 w-4" />
                              <span className="sr-only">Rotate</span>
                            </Button>
                            <Button variant="outline" size="sm" className="text-red-500">
                              <XCircle className="h-4 w-4" />
                              <span className="sr-only">Revoke</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">API Access Controls</h3>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="rate-limiting">Rate limiting</Label>
                      <p className="text-sm text-muted-foreground">Limit the number of API requests per minute</p>
                    </div>
                    <Switch id="rate-limiting" defaultChecked />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="ip-whitelist">IP whitelisting for API access</Label>
                      <p className="text-sm text-muted-foreground">Restrict API access to specific IP addresses</p>
                    </div>
                    <Switch id="ip-whitelist" />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="audit-logging">Enhanced API audit logging</Label>
                      <p className="text-sm text-muted-foreground">Log detailed information about all API requests</p>
                    </div>
                    <Switch id="audit-logging" defaultChecked />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button>Save API Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
