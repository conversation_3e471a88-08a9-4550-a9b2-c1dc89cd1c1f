const { PrismaClient } = require('@prisma/client');
const logger = require('../../../utils/logger');

const prisma = new PrismaClient();

const dashboardController = {
  // Get dashboard overview statistics
  async getDashboardStats(req, res) {
    try {
      const { institutionId } = req.user;

      // Get counts for main entities
      const [
        studentCount,
        teacherCount,
        staffCount,
        classCount,
        subjectCount,
        recentAttendance,
        upcomingExams,
        pendingFees
      ] = await Promise.all([
        // Student count
        prisma.student.count({
          where: { institutionId }
        }),
        
        // Teacher count
        prisma.teacher.count({
          where: { institutionId }
        }),
        
        // Staff count
        prisma.staff.count({
          where: { institutionId }
        }),
        
        // Class count
        prisma.class.count({
          where: { institutionId }
        }),
        
        // Subject count
        prisma.subject.count({
          where: { institutionId }
        }),
        
        // Recent attendance (today's attendance rate)
        prisma.attendance.aggregate({
          where: {
            institutionId,
            date: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
              lt: new Date(new Date().setHours(23, 59, 59, 999))
            }
          },
          _avg: {
            present: true
          },
          _count: true
        }),
        
        // Upcoming exams (next 7 days)
        prisma.exam.count({
          where: {
            institutionId,
            date: {
              gte: new Date(),
              lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
            }
          }
        }),
        
        // Pending fees
        prisma.feeRecord.count({
          where: {
            student: {
              institutionId
            },
            status: 'PENDING'
          }
        })
      ]);

      // Calculate attendance rate
      const attendanceRate = recentAttendance._count > 0 
        ? (recentAttendance._avg.present || 0) * 100 
        : 0;

      const stats = {
        students: studentCount,
        teachers: teacherCount,
        staff: staffCount,
        classes: classCount,
        subjects: subjectCount,
        attendanceRate: Math.round(attendanceRate * 100) / 100,
        upcomingExams,
        pendingFees
      };

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      logger.error('Error fetching dashboard stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch dashboard statistics',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  },

  // Get recent activities
  async getRecentActivities(req, res) {
    try {
      const { institutionId } = req.user;
      const limit = parseInt(req.query.limit) || 10;

      // Get recent activities from various sources
      const [recentEnrollments, recentAttendance, recentGrades, recentFees] = await Promise.all([
        // Recent student enrollments
        prisma.enrollment.findMany({
          where: {
            student: { institutionId },
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
            }
          },
          include: {
            student: { select: { firstName: true, lastName: true } },
            class: { select: { name: true } }
          },
          orderBy: { createdAt: 'desc' },
          take: Math.ceil(limit / 4)
        }),

        // Recent attendance records
        prisma.attendance.findMany({
          where: {
            institutionId,
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            }
          },
          include: {
            student: { select: { firstName: true, lastName: true } },
            class: { select: { name: true } }
          },
          orderBy: { createdAt: 'desc' },
          take: Math.ceil(limit / 4)
        }),

        // Recent grade entries
        prisma.grade.findMany({
          where: {
            student: { institutionId },
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            }
          },
          include: {
            student: { select: { firstName: true, lastName: true } },
            subject: { select: { name: true } }
          },
          orderBy: { createdAt: 'desc' },
          take: Math.ceil(limit / 4)
        }),

        // Recent fee payments
        prisma.feeRecord.findMany({
          where: {
            student: { institutionId },
            updatedAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            }
          },
          include: {
            student: { select: { firstName: true, lastName: true } }
          },
          orderBy: { updatedAt: 'desc' },
          take: Math.ceil(limit / 4)
        })
      ]);

      // Format activities with a common structure
      const activities = [];

      recentEnrollments.forEach(enrollment => {
        activities.push({
          id: `enrollment-${enrollment.id}`,
          type: 'enrollment',
          title: 'New Student Enrollment',
          description: `${enrollment.student.firstName} ${enrollment.student.lastName} enrolled in ${enrollment.class.name}`,
          timestamp: enrollment.createdAt,
          data: enrollment
        });
      });

      recentAttendance.forEach(attendance => {
        activities.push({
          id: `attendance-${attendance.id}`,
          type: 'attendance',
          title: 'Attendance Recorded',
          description: `${attendance.student.firstName} ${attendance.student.lastName} - ${attendance.present ? 'Present' : 'Absent'} in ${attendance.class.name}`,
          timestamp: attendance.createdAt,
          data: attendance
        });
      });

      recentGrades.forEach(grade => {
        activities.push({
          id: `grade-${grade.id}`,
          type: 'grade',
          title: 'Grade Recorded',
          description: `${grade.student.firstName} ${grade.student.lastName} - ${grade.grade} in ${grade.subject.name}`,
          timestamp: grade.createdAt,
          data: grade
        });
      });

      recentFees.forEach(fee => {
        activities.push({
          id: `fee-${fee.id}`,
          type: 'fee',
          title: 'Fee Status Updated',
          description: `${fee.student.firstName} ${fee.student.lastName} - Fee ${fee.status.toLowerCase()}`,
          timestamp: fee.updatedAt,
          data: fee
        });
      });

      // Sort by timestamp and limit
      activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      const limitedActivities = activities.slice(0, limit);

      res.json({
        success: true,
        data: limitedActivities
      });

    } catch (error) {
      logger.error('Error fetching recent activities:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch recent activities',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  },

  // Get analytics data for charts and graphs
  async getAnalytics(req, res) {
    try {
      const { institutionId } = req.user;
      const { period = '30d' } = req.query;

      // Calculate date range based on period
      let startDate;
      switch (period) {
        case '7d':
          startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      }

      const [attendanceData, enrollmentData, gradeDistribution, feeCollection] = await Promise.all([
        // Attendance trends
        prisma.attendance.groupBy({
          by: ['date'],
          where: {
            institutionId,
            date: { gte: startDate }
          },
          _avg: { present: true },
          _count: true,
          orderBy: { date: 'asc' }
        }),

        // Enrollment trends
        prisma.enrollment.groupBy({
          by: ['createdAt'],
          where: {
            student: { institutionId },
            createdAt: { gte: startDate }
          },
          _count: true,
          orderBy: { createdAt: 'asc' }
        }),

        // Grade distribution
        prisma.grade.groupBy({
          by: ['grade'],
          where: {
            student: { institutionId },
            createdAt: { gte: startDate }
          },
          _count: true
        }),

        // Fee collection
        prisma.feeRecord.groupBy({
          by: ['status'],
          where: {
            student: { institutionId },
            updatedAt: { gte: startDate }
          },
          _sum: { amount: true },
          _count: true
        })
      ]);

      const analytics = {
        attendance: attendanceData.map(item => ({
          date: item.date,
          rate: (item._avg.present || 0) * 100,
          count: item._count
        })),
        enrollments: enrollmentData.map(item => ({
          date: item.createdAt,
          count: item._count
        })),
        grades: gradeDistribution.map(item => ({
          grade: item.grade,
          count: item._count
        })),
        fees: feeCollection.map(item => ({
          status: item.status,
          amount: item._sum.amount || 0,
          count: item._count
        }))
      };

      res.json({
        success: true,
        data: analytics
      });

    } catch (error) {
      logger.error('Error fetching analytics data:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch analytics data',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
};

module.exports = dashboardController;
