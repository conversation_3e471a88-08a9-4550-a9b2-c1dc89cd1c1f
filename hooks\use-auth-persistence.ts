"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/contexts/auth-context"

interface AuthPersistenceState {
  lastAuthCheck: number | null
  authAttempts: number
  lastError: string | null
}

const AUTH_STORAGE_KEY = "auth_persistence"
const AUTH_CHECK_INTERVAL = 5 * 60 * 1000 // 5 minutes
const MAX_AUTH_ATTEMPTS = 5

export function useAuthPersistence() {
  const { user, isLoading, authError, retryAuth } = useAuth()
  const [persistenceState, setPersistenceState] = useState<AuthPersistenceState>({
    lastAuthCheck: null,
    authAttempts: 0,
    lastError: null
  })

  // Load persistence state from localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        const stored = localStorage.getItem(AUTH_STORAGE_KEY)
        if (stored) {
          const parsed = JSON.parse(stored)
          setPersistenceState(parsed)
        }
      } catch (error) {
        console.warn("Failed to load auth persistence state:", error)
      }
    }
  }, [])

  // Save persistence state to localStorage
  const savePersistenceState = (state: AuthPersistenceState) => {
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(state))
        setPersistenceState(state)
      } catch (error) {
        console.warn("Failed to save auth persistence state:", error)
      }
    }
  }

  // Update persistence state when auth state changes
  useEffect(() => {
    const now = Date.now()
    
    if (user && !isLoading) {
      // Successful authentication - reset attempts
      savePersistenceState({
        lastAuthCheck: now,
        authAttempts: 0,
        lastError: null
      })
    } else if (authError && !isLoading) {
      // Authentication error - increment attempts
      savePersistenceState({
        lastAuthCheck: now,
        authAttempts: persistenceState.authAttempts + 1,
        lastError: authError
      })
    }
  }, [user, isLoading, authError, persistenceState.authAttempts])

  // Periodic auth check
  useEffect(() => {
    if (!user || isLoading) return

    const interval = setInterval(async () => {
      const now = Date.now()
      const timeSinceLastCheck = now - (persistenceState.lastAuthCheck || 0)
      
      if (timeSinceLastCheck >= AUTH_CHECK_INTERVAL) {
        console.log("🔄 AuthPersistence: Performing periodic auth check...")
        try {
          await retryAuth()
        } catch (error) {
          console.error("Periodic auth check failed:", error)
        }
      }
    }, AUTH_CHECK_INTERVAL)

    return () => clearInterval(interval)
  }, [user, isLoading, persistenceState.lastAuthCheck, retryAuth])

  // Check if we should attempt auto-recovery
  const shouldAttemptRecovery = () => {
    return persistenceState.authAttempts < MAX_AUTH_ATTEMPTS && 
           !isLoading && 
           authError &&
           !user
  }

  // Clear persistence state (useful for logout)
  const clearPersistenceState = () => {
    if (typeof window !== "undefined") {
      localStorage.removeItem(AUTH_STORAGE_KEY)
      setPersistenceState({
        lastAuthCheck: null,
        authAttempts: 0,
        lastError: null
      })
    }
  }

  // Get time until next allowed auth attempt (for rate limiting)
  const getTimeUntilNextAttempt = () => {
    if (persistenceState.authAttempts === 0) return 0
    
    const backoffTime = Math.pow(2, persistenceState.authAttempts - 1) * 1000
    const timeSinceLastCheck = Date.now() - (persistenceState.lastAuthCheck || 0)
    
    return Math.max(0, backoffTime - timeSinceLastCheck)
  }

  return {
    persistenceState,
    shouldAttemptRecovery,
    clearPersistenceState,
    getTimeUntilNextAttempt,
    isRateLimited: persistenceState.authAttempts >= MAX_AUTH_ATTEMPTS
  }
}

// Hook for components that need to know about auth state without triggering checks
export function useAuthStatus() {
  const { user, isLoading, authError } = useAuth()
  
  return {
    isAuthenticated: !!user && !authError,
    isLoading,
    hasError: !!authError,
    user
  }
}

// Hook for handling auth-dependent data fetching
export function useAuthenticatedFetch() {
  const { user, authError } = useAuth()
  const [isReady, setIsReady] = useState(false)

  useEffect(() => {
    // Only consider ready when we have a user and no auth errors
    setIsReady(!!user && !authError)
  }, [user, authError])

  const authenticatedFetch = async (url: string, options: RequestInit = {}) => {
    if (!isReady) {
      throw new Error("Authentication not ready")
    }

    const response = await fetch(url, {
      ...options,
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    })

    if (response.status === 401) {
      throw new Error("Authentication required")
    }

    return response
  }

  return {
    isReady,
    authenticatedFetch
  }
}
