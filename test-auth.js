/**
 * Simple test script to verify authentication flow
 */

const FRONTEND_URL = 'http://localhost:3001'
const BACKEND_URL = 'http://localhost:4000'

async function testAuthFlow() {
  console.log('🔍 Testing authentication flow...\n')

  try {
    // Test 1: Check if backend is running
    console.log('1. Testing backend connectivity...')
    const backendResponse = await fetch(`${BACKEND_URL}/api/v1/auth/me`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer invalid-token',
        'Content-Type': 'application/json'
      }
    })
    console.log(`   Backend response: ${backendResponse.status} (expected 401)`)
    
    // Test 2: Test login endpoint
    console.log('\n2. Testing login endpoint...')
    const loginResponse = await fetch(`${FRONTEND_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
    })
    console.log(`   Login response: ${loginResponse.status}`)
    const loginData = await loginResponse.json()
    console.log(`   Login data:`, loginData)

    // Test 3: Test auth endpoint without token
    console.log('\n3. Testing auth endpoint without token...')
    const authResponse = await fetch(`${FRONTEND_URL}/api/auth`, {
      method: 'GET'
    })
    console.log(`   Auth response: ${authResponse.status} (expected 401)`)

    // Test 4: Test subjects endpoint without token
    console.log('\n4. Testing subjects endpoint without token...')
    const subjectsResponse = await fetch(`${FRONTEND_URL}/api/subjects`, {
      method: 'GET'
    })
    console.log(`   Subjects response: ${subjectsResponse.status} (expected 401)`)

    // Test 5: Test classes endpoint without token
    console.log('\n5. Testing classes endpoint without token...')
    const classesResponse = await fetch(`${FRONTEND_URL}/api/classes`, {
      method: 'GET'
    })
    console.log(`   Classes response: ${classesResponse.status} (expected 401)`)

    console.log('\n✅ Authentication flow test completed!')
    console.log('\nIf you see 401 responses for protected endpoints, that\'s expected behavior.')
    console.log('The issue might be with token storage or refresh mechanism.')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testAuthFlow()
