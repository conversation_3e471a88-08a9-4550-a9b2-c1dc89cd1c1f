const express = require('express');
const router = express.Router();
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const {
  getSecuritySettings,
  updateSecuritySettings,
  getSecurityLogs,
  getApiKeys,
  createApi<PERSON>ey,
  revokeApi<PERSON>ey,
  rotateApiKey,
  getSecurityStats
} = require('./security.controller');

// Protect all routes and restrict to SUPER_ADMIN
router.use(authenticate);
router.use(restrictTo('SUPER_ADMIN'));

// Security settings routes
router.get('/settings', getSecuritySettings);
router.put('/settings', updateSecuritySettings);

// Security logs routes
router.get('/logs', getSecurityLogs);
router.get('/stats', getSecurityStats);

// API key management routes
router.get('/api-keys', getApiKeys);
router.post('/api-keys', createApiKey);
router.delete('/api-keys/:id', revokeApiKey);
router.post('/api-keys/:id/rotate', rotateApiKey);

module.exports = router;
