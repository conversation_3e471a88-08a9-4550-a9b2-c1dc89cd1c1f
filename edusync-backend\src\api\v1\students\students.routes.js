const express = require('express');
const router = express.Router();
const studentsController = require('./students.controller');
const { authenticate } = require('../../../middleware/authenticate');
const { authorize } = require('../../../middleware/authorization');

// Apply authentication middleware to all routes
router.use(authenticate);

// GET /api/v1/students - Get all students with filtering and pagination
router.get('/', authorize(['ADMIN', 'TEACHER', 'STAFF']), studentsController.getStudents);

// GET /api/v1/students/stats - Get student statistics
router.get('/stats', authorize(['ADMIN', 'TEACHER']), studentsController.getStudentStats);

// GET /api/v1/students/:id - Get student by ID
router.get('/:id', authorize(['ADMIN', 'TEACHER', 'STAFF']), studentsController.getStudentById);

// POST /api/v1/students - Create new student
router.post('/', authorize(['ADMIN']), studentsController.createStudent);

// PUT /api/v1/students/:id - Update student
router.put('/:id', authorize(['ADMIN']), studentsController.updateStudent);

// DELETE /api/v1/students/:id - Delete student
router.delete('/:id', authorize(['ADMIN']), studentsController.deleteStudent);

// POST /api/v1/students/bulk - Bulk create students
router.post('/bulk', authorize(['ADMIN']), studentsController.bulkCreateStudents);

// GET /api/v1/students/:id/attendance - Get student attendance
router.get('/:id/attendance', authorize(['ADMIN', 'TEACHER', 'STAFF']), studentsController.getStudentAttendance);

// GET /api/v1/students/:id/grades - Get student grades
router.get('/:id/grades', authorize(['ADMIN', 'TEACHER', 'STAFF']), studentsController.getStudentGrades);

// GET /api/v1/students/:id/fees - Get student fees
router.get('/:id/fees', authorize(['ADMIN', 'STAFF']), studentsController.getStudentFees);

// GET /api/v1/students/:id/enrollments - Get student enrollments
router.get('/:id/enrollments', authorize(['ADMIN', 'TEACHER', 'STAFF']), studentsController.getStudentEnrollments);

module.exports = router;