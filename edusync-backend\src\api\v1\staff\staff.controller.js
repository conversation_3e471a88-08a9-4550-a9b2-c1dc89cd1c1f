const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Get all staff with pagination, filtering, and search
 */
const getStaff = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      department = '',
      jobTitle = '',
      status = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      institutionId
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Build where clause
    const where = {
      AND: []
    };

    // Filter by institution if provided
    if (institutionId) {
      where.AND.push({
        user: {
          institutions: {
            some: {
              institutionId: institutionId,
              isActive: true
            }
          }
        }
      });
    }

    // Add search conditions
    if (search) {
      where.AND.push({
        OR: [
          { user: { firstName: { contains: search, mode: 'insensitive' } } },
          { user: { lastName: { contains: search, mode: 'insensitive' } } },
          { user: { email: { contains: search, mode: 'insensitive' } } },
          { employeeId: { contains: search, mode: 'insensitive' } },
          { department: { contains: search, mode: 'insensitive' } },
          { jobTitle: { contains: search, mode: 'insensitive' } }
        ]
      });
    }

    // Add department filter
    if (department && department !== 'all') {
      where.AND.push({
        department: { contains: department, mode: 'insensitive' }
      });
    }

    // Add job title filter
    if (jobTitle && jobTitle !== 'all') {
      where.AND.push({
        jobTitle: { contains: jobTitle, mode: 'insensitive' }
      });
    }

    // If no conditions, remove the AND array
    if (where.AND.length === 0) {
      delete where.AND;
    }

    // Add status filter
    if (status && status !== 'all') {
      where.AND.push({
        user: {
          isActive: status === 'ACTIVE'
        }
      });
    }

    // If no conditions, remove the AND array
    if (where.AND && where.AND.length === 0) {
      delete where.AND;
    }

    // Build orderBy clause
    let orderBy = {};
    if (sortBy === 'name') {
      orderBy = { user: { firstName: sortOrder } };
    } else if (sortBy === 'department') {
      orderBy = { department: sortOrder };
    } else if (sortBy === 'jobTitle') {
      orderBy = { jobTitle: sortOrder };
    } else if (sortBy === 'hireDate') {
      orderBy = { hireDate: sortOrder };
    } else {
      orderBy = { user: { [sortBy]: sortOrder } };
    }

    // Fetch staff with relations
    const [staff, totalCount] = await Promise.all([
      prisma.staff.findMany({
        where,
        skip,
        take,
        orderBy,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phoneNumber: true,
              isActive: true,
              role: true,
              profileImageUrl: true,
              createdAt: true,
              updatedAt: true
            }
          },
          _count: {
            select: {
              recordedPayments: true,
              libraryIssuer: true,
              generatedReports: true
            }
          }
        }
      }),
      prisma.staff.count({ where })
    ]);

    const totalPages = Math.ceil(totalCount / take);

    res.json({
      success: true,
      data: staff,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalRecords: totalCount,
        hasNext: parseInt(page) < totalPages,
        hasPrev: parseInt(page) > 1
      }
    });
  } catch (error) {
    console.error('Error fetching staff:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch staff',
      error: error.message
    });
  }
};

/**
 * Get staff member by ID
 */
const getStaffById = async (req, res) => {
  try {
    const { id } = req.params;
    const { institutionId } = req.query;

    const staff = await prisma.staff.findFirst({
      where: {
        id,
        user: {
          institutionId: institutionId
        }
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            status: true,
            role: true,
            dateOfBirth: true,
            gender: true,
            address: true,
            emergencyContact: true,
            profileImage: true,
            createdAt: true,
            updatedAt: true
          }
        },
        _count: {
          select: {
            recordedPayments: true,
            libraryIssuer: true,
            generatedReports: true
          }
        }
      }
    });

    if (!staff) {
      return res.status(404).json({
        success: false,
        message: 'Staff member not found'
      });
    }

    res.json({
      success: true,
      data: staff
    });
  } catch (error) {
    console.error('Error fetching staff member:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch staff member',
      error: error.message
    });
  }
};

/**
 * Create new staff member
 */
const createStaff = async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      phone,
      dateOfBirth,
      gender,
      address,
      emergencyContact,
      employeeId,
      qualification,
      experience,
      department,
      jobTitle,
      salary,
      contractType,
      hireDate,
      institutionId
    } = req.body;

    // Validate required fields
    if (!firstName || !lastName || !email || !institutionId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: firstName, lastName, email, institutionId'
      });
    }

    // Check if user with email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Check if employeeId is unique within institution
    if (employeeId) {
      const existingEmployee = await prisma.staff.findFirst({
        where: {
          employeeId,
          user: {
            institutionId
          }
        }
      });

      if (existingEmployee) {
        return res.status(400).json({
          success: false,
          message: 'Employee ID already exists in this institution'
        });
      }
    }

    // Create staff with user in a transaction
    const staff = await prisma.$transaction(async (tx) => {
      // Create user first
      const user = await tx.user.create({
        data: {
          firstName,
          lastName,
          email,
          phone: phone || null,
          dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
          gender: gender || null,
          address: address || null,
          emergencyContact: emergencyContact || null,
          role: 'STAFF',
          status: 'ACTIVE',
          institutionId
        }
      });

      // Create staff record
      const staffRecord = await tx.staff.create({
        data: {
          userId: user.id,
          employeeId: employeeId || null,
          qualification: qualification || null,
          experience: experience ? parseInt(experience) : null,
          department: department || null,
          jobTitle: jobTitle || null,
          salary: salary ? parseFloat(salary) : null,
          contractType: contractType || null,
          hireDate: hireDate ? new Date(hireDate) : new Date()
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
              status: true,
              role: true,
              createdAt: true
            }
          }
        }
      });

      return staffRecord;
    });

    res.status(201).json({
      success: true,
      message: 'Staff member created successfully',
      data: staff
    });
  } catch (error) {
    console.error('Error creating staff member:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create staff member',
      error: error.message
    });
  }
};

/**
 * Update staff member
 */
const updateStaff = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      firstName,
      lastName,
      email,
      phone,
      dateOfBirth,
      gender,
      address,
      emergencyContact,
      employeeId,
      qualification,
      experience,
      department,
      jobTitle,
      salary,
      contractType,
      hireDate,
      status,
      institutionId
    } = req.body;

    // Check if staff exists and belongs to institution
    const existingStaff = await prisma.staff.findFirst({
      where: {
        id,
        user: {
          institutionId: institutionId
        }
      },
      include: {
        user: true
      }
    });

    if (!existingStaff) {
      return res.status(404).json({
        success: false,
        message: 'Staff member not found'
      });
    }

    // Check if email is being changed and if new email exists
    if (email && email !== existingStaff.user.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'User with this email already exists'
        });
      }
    }

    // Check if employeeId is being changed and if new employeeId exists
    if (employeeId && employeeId !== existingStaff.employeeId) {
      const existingEmployee = await prisma.staff.findFirst({
        where: {
          employeeId,
          user: {
            institutionId
          },
          NOT: {
            id: id
          }
        }
      });

      if (existingEmployee) {
        return res.status(400).json({
          success: false,
          message: 'Employee ID already exists in this institution'
        });
      }
    }

    // Update staff and user in a transaction
    const updatedStaff = await prisma.$transaction(async (tx) => {
      // Update user
      await tx.user.update({
        where: { id: existingStaff.userId },
        data: {
          ...(firstName && { firstName }),
          ...(lastName && { lastName }),
          ...(email && { email }),
          ...(phone !== undefined && { phone }),
          ...(dateOfBirth && { dateOfBirth: new Date(dateOfBirth) }),
          ...(gender && { gender }),
          ...(address !== undefined && { address }),
          ...(emergencyContact !== undefined && { emergencyContact }),
          ...(status && { status })
        }
      });

      // Update staff
      const staffRecord = await tx.staff.update({
        where: { id },
        data: {
          ...(employeeId !== undefined && { employeeId }),
          ...(qualification !== undefined && { qualification }),
          ...(experience !== undefined && { experience: experience ? parseInt(experience) : null }),
          ...(department !== undefined && { department }),
          ...(jobTitle !== undefined && { jobTitle }),
          ...(salary !== undefined && { salary: salary ? parseFloat(salary) : null }),
          ...(contractType !== undefined && { contractType }),
          ...(hireDate && { hireDate: new Date(hireDate) })
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
              status: true,
              role: true,
              updatedAt: true
            }
          }
        }
      });

      return staffRecord;
    });

    res.json({
      success: true,
      message: 'Staff member updated successfully',
      data: updatedStaff
    });
  } catch (error) {
    console.error('Error updating staff member:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update staff member',
      error: error.message
    });
  }
};

/**
 * Delete staff member
 */
const deleteStaff = async (req, res) => {
  try {
    const { id } = req.params;
    const { institutionId } = req.query;

    // Check if staff exists and belongs to institution
    const existingStaff = await prisma.staff.findFirst({
      where: {
        id,
        user: {
          institutionId: institutionId
        }
      }
    });

    if (!existingStaff) {
      return res.status(404).json({
        success: false,
        message: 'Staff member not found'
      });
    }

    // Delete staff (this will cascade delete the user due to foreign key constraints)
    await prisma.staff.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Staff member deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting staff member:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete staff member',
      error: error.message
    });
  }
};

/**
 * Get staff statistics
 */
const getStaffStats = async (req, res) => {
  try {
    const { institutionId } = req.query;

    const [
      totalStaff,
      activeStaff,
      onLeaveStaff,
      departmentStats,
      jobTitleStats
    ] = await Promise.all([
      prisma.staff.count({
        where: {
          user: {
            institutionId: institutionId
          }
        }
      }),
      prisma.staff.count({
        where: {
          user: {
            institutionId: institutionId,
            status: 'ACTIVE'
          }
        }
      }),
      prisma.staff.count({
        where: {
          user: {
            institutionId: institutionId,
            status: 'ON_LEAVE'
          }
        }
      }),
      prisma.staff.groupBy({
        by: ['department'],
        where: {
          user: {
            institutionId: institutionId
          },
          department: {
            not: null
          }
        },
        _count: {
          id: true
        }
      }),
      prisma.staff.groupBy({
        by: ['jobTitle'],
        where: {
          user: {
            institutionId: institutionId
          },
          jobTitle: {
            not: null
          }
        },
        _count: {
          id: true
        }
      })
    ]);

    res.json({
      success: true,
      data: {
        total: totalStaff,
        active: activeStaff,
        onLeave: onLeaveStaff,
        inactive: totalStaff - activeStaff - onLeaveStaff,
        byDepartment: departmentStats.map(stat => ({
          department: stat.department,
          count: stat._count.id
        })),
        byJobTitle: jobTitleStats.map(stat => ({
          jobTitle: stat.jobTitle,
          count: stat._count.id
        }))
      }
    });
  } catch (error) {
    console.error('Error fetching staff statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch staff statistics',
      error: error.message
    });
  }
};

module.exports = {
  getAllStaff: getStaff,
  getStaffById,
  createStaff,
  updateStaff,
  deleteStaff,
  getStaffStats
};
