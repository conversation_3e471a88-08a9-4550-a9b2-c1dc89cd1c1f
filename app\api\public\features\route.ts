import { NextResponse } from "next/server"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function GET() {
  try {
    const response = await fetch(`${BACKEND_URL}/api/v1/content/features`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      next: { revalidate: 300 }, // Cache for 5 minutes
    })

    if (!response.ok) {
      throw new Error("Failed to fetch features")
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching features:", error)
    // Return fallback data
    return NextResponse.json({
      success: true,
      data: [
        {
          id: 1,
          title: "Student Management",
          description: "Comprehensive student information system with enrollment, attendance, and academic tracking.",
          icon: "Users",
          benefits: ["Student profiles", "Enrollment management", "Academic history"],
          category: "core",
        },
        {
          id: 2,
          title: "Grade Management",
          description: "Efficient grading system with report cards, transcripts, and parent communication.",
          icon: "BookOpen",
          benefits: ["Digital gradebooks", "Report cards", "Progress tracking"],
          category: "academic",
        },
      ],
    })
  }
}
