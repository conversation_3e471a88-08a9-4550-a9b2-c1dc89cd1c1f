"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { ChevronLeft, ChevronRight, Plus, Download, Search, MoreHorizontal, Edit, Trash, Eye } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { format } from "date-fns"

// Mock data for academic calendar events
const mockEvents = [
  {
    id: "1",
    title: "First Day of School",
    date: "2023-08-15",
    endDate: "2023-08-15",
    type: "academic",
    description: "First day of the academic year 2023-2024.",
    allDay: true,
  },
  {
    id: "2",
    title: "Parent-Teacher Meeting",
    date: "2023-09-10",
    endDate: "2023-09-10",
    type: "meeting",
    description: "Quarterly parent-teacher meeting for all grades.",
    allDay: false,
    startTime: "14:00",
    endTime: "18:00",
  },
  {
    id: "3",
    title: "Mid-Term Examinations",
    date: "2023-10-15",
    endDate: "2023-10-25",
    type: "exam",
    description: "Mid-term examinations for all grades.",
    allDay: true,
  },
  {
    id: "4",
    title: "Annual Sports Day",
    date: "2023-11-05",
    endDate: "2023-11-05",
    type: "event",
    description: "Annual sports day celebration with various competitions.",
    allDay: true,
  },
  {
    id: "5",
    title: "Winter Break",
    date: "2023-12-20",
    endDate: "2024-01-05",
    type: "holiday",
    description: "Winter break for all students and staff.",
    allDay: true,
  },
  {
    id: "6",
    title: "Science Fair",
    date: "2024-01-20",
    endDate: "2024-01-20",
    type: "event",
    description: "Annual science fair for all students to showcase their projects.",
    allDay: true,
  },
  {
    id: "7",
    title: "Final Examinations",
    date: "2024-03-10",
    endDate: "2024-03-25",
    type: "exam",
    description: "Final examinations for all grades.",
    allDay: true,
  },
  {
    id: "8",
    title: "Result Declaration",
    date: "2024-04-15",
    endDate: "2024-04-15",
    type: "academic",
    description: "Declaration of results for the academic year 2023-2024.",
    allDay: false,
    startTime: "10:00",
    endTime: "12:00",
  },
  {
    id: "9",
    title: "Annual Day Celebration",
    date: "2024-02-15",
    endDate: "2024-02-15",
    type: "event",
    description: "Annual day celebration with cultural performances.",
    allDay: false,
    startTime: "16:00",
    endTime: "20:00",
  },
  {
    id: "10",
    title: "Summer Vacation",
    date: "2024-05-01",
    endDate: "2024-06-30",
    type: "holiday",
    description: "Summer vacation for all students.",
    allDay: true,
  },
]

// Event type colors
const eventTypeColors: Record<string, string> = {
  academic: "bg-blue-100 text-blue-800 border-blue-300",
  meeting: "bg-purple-100 text-purple-800 border-purple-300",
  exam: "bg-red-100 text-red-800 border-red-300",
  event: "bg-green-100 text-green-800 border-green-300",
  holiday: "bg-amber-100 text-amber-800 border-amber-300",
}

// Form schema for adding/editing events
const eventFormSchema = z
  .object({
    title: z.string().min(2, {
      message: "Event title must be at least 2 characters.",
    }),
    date: z.date({
      required_error: "Event date is required.",
    }),
    endDate: z.date({
      required_error: "End date is required.",
    }),
    type: z.string({
      required_error: "Event type is required.",
    }),
    description: z.string().optional(),
    allDay: z.boolean().default(true),
    startTime: z.string().optional(),
    endTime: z.string().optional(),
  })
  .refine(
    (data) => {
      if (!data.allDay && (!data.startTime || !data.endTime)) {
        return false
      }
      return true
    },
    {
      message: "Start time and end time are required for non-all-day events.",
      path: ["startTime", "endTime"],
    },
  )

export default function AcademicCalendarPage() {
  const router = useRouter()
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddEventDialogOpen, setIsAddEventDialogOpen] = useState(false)
  const [isViewEventDialogOpen, setIsViewEventDialogOpen] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState<any>(null)

  // Initialize form
  const form = useForm<z.infer<typeof eventFormSchema>>({
    resolver: zodResolver(eventFormSchema),
    defaultValues: {
      title: "",
      date: new Date(),
      endDate: new Date(),
      type: "academic",
      description: "",
      allDay: true,
    },
  })

  // Filter events based on search term
  const filteredEvents = mockEvents.filter(
    (event) =>
      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  // Get events for the selected date
  const eventsForSelectedDate = selectedDate
    ? filteredEvents.filter((event) => {
        const eventStartDate = new Date(event.date)
        const eventEndDate = new Date(event.endDate)
        const selected = new Date(selectedDate)

        // Reset time components for comparison
        selected.setHours(0, 0, 0, 0)
        eventStartDate.setHours(0, 0, 0, 0)
        eventEndDate.setHours(0, 0, 0, 0)

        return selected >= eventStartDate && selected <= eventEndDate
      })
    : []

  // Handle view event
  const handleViewEvent = (event: any) => {
    setSelectedEvent(event)
    setIsViewEventDialogOpen(true)
  }

  // Handle form submission
  function onSubmit(values: z.infer<typeof eventFormSchema>) {
    console.log(values)
    setIsAddEventDialogOpen(false)
    // In a real app, you would add the event to the database
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Academic Calendar</h1>
          <p className="text-muted-foreground">Manage academic events, holidays, and important dates</p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export Calendar
          </Button>
          <Dialog open={isAddEventDialogOpen} onOpenChange={setIsAddEventDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Event
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[550px]">
              <DialogHeader>
                <DialogTitle>Add Calendar Event</DialogTitle>
                <DialogDescription>Create a new event in the academic calendar.</DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Event Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter event title" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="date"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Start Date</FormLabel>
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date("2023-01-01")}
                            initialFocus
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="endDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>End Date</FormLabel>
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < form.getValues().date}
                            initialFocus
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Event Type</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select event type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="academic">Academic</SelectItem>
                            <SelectItem value="meeting">Meeting</SelectItem>
                            <SelectItem value="exam">Examination</SelectItem>
                            <SelectItem value="event">Event</SelectItem>
                            <SelectItem value="holiday">Holiday</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Enter event description" className="resize-none" rows={3} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="allDay"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>All Day Event</FormLabel>
                          <FormDescription>This event will take the entire day</FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  {!form.watch("allDay") && (
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="startTime"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Start Time</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="endTime"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>End Time</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}

                  <DialogFooter>
                    <Button type="submit">Add Event</Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="calendar">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          <TabsTrigger value="list">List View</TabsTrigger>
        </TabsList>

        <TabsContent value="calendar" className="space-y-4 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="md:col-span-2">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Calendar</CardTitle>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => {
                        const prevMonth = new Date(currentMonth)
                        prevMonth.setMonth(prevMonth.getMonth() - 1)
                        setCurrentMonth(prevMonth)
                      }}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <div className="font-medium">{format(currentMonth, "MMMM yyyy")}</div>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => {
                        const nextMonth = new Date(currentMonth)
                        nextMonth.setMonth(nextMonth.getMonth() + 1)
                        setCurrentMonth(nextMonth)
                      }}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  month={currentMonth}
                  onMonthChange={setCurrentMonth}
                  className="rounded-md border"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{selectedDate ? format(selectedDate, "MMMM d, yyyy") : "Select a date"}</CardTitle>
                <CardDescription>
                  {eventsForSelectedDate.length} event{eventsForSelectedDate.length !== 1 ? "s" : ""} on this day
                </CardDescription>
              </CardHeader>
              <CardContent>
                {eventsForSelectedDate.length > 0 ? (
                  <div className="space-y-4">
                    {eventsForSelectedDate.map((event) => {
                      const colorClass = eventTypeColors[event.type] || "bg-gray-100 text-gray-800 border-gray-300"

                      return (
                        <div
                          key={event.id}
                          className={`border rounded-md p-3 cursor-pointer ${colorClass}`}
                          onClick={() => handleViewEvent(event)}
                        >
                          <div className="font-medium">{event.title}</div>
                          {!event.allDay && (
                            <div className="text-sm mt-1">
                              {event.startTime} - {event.endTime}
                            </div>
                          )}
                          <div className="text-sm mt-1 line-clamp-2">{event.description}</div>
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">No events scheduled for this day</div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="list" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                <CardTitle>All Events</CardTitle>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search events..."
                      className="pl-8 w-[200px] sm:w-[300px]"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Filter by type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="academic">Academic</SelectItem>
                      <SelectItem value="meeting">Meeting</SelectItem>
                      <SelectItem value="exam">Examination</SelectItem>
                      <SelectItem value="event">Event</SelectItem>
                      <SelectItem value="holiday">Holiday</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md overflow-hidden">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="py-3 px-4 text-left font-medium">Event</th>
                      <th className="py-3 px-4 text-left font-medium">Date</th>
                      <th className="py-3 px-4 text-left font-medium">Type</th>
                      <th className="py-3 px-4 text-right font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredEvents.map((event) => {
                      const colorClass = eventTypeColors[event.type] || "bg-gray-100 text-gray-800"

                      return (
                        <tr key={event.id} className="border-b last:border-0">
                          <td className="py-3 px-4">
                            <div className="font-medium">{event.title}</div>
                            <div className="text-sm text-muted-foreground line-clamp-1">{event.description}</div>
                          </td>
                          <td className="py-3 px-4">
                            {event.date === event.endDate ? (
                              <div>{format(new Date(event.date), "MMM d, yyyy")}</div>
                            ) : (
                              <div>
                                {format(new Date(event.date), "MMM d, yyyy")} -
                                {format(new Date(event.endDate), "MMM d, yyyy")}
                              </div>
                            )}
                            {!event.allDay && (
                              <div className="text-sm text-muted-foreground">
                                {event.startTime} - {event.endTime}
                              </div>
                            )}
                          </td>
                          <td className="py-3 px-4">
                            <Badge className={colorClass}>
                              {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                            </Badge>
                          </td>
                          <td className="py-3 px-4 text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => handleViewEvent(event)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit Event
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-destructive">
                                  <Trash className="mr-2 h-4 w-4" />
                                  Delete Event
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* View Event Dialog */}
      <Dialog open={isViewEventDialogOpen} onOpenChange={setIsViewEventDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          {selectedEvent && (
            <>
              <DialogHeader>
                <DialogTitle>{selectedEvent.title}</DialogTitle>
                <DialogDescription>
                  <Badge className={eventTypeColors[selectedEvent.type]}>
                    {selectedEvent.type.charAt(0).toUpperCase() + selectedEvent.type.slice(1)}
                  </Badge>
                </DialogDescription>
              </DialogHeader>
              <div className="py-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium">Date</h4>
                    <p className="text-sm">
                      {selectedEvent.date === selectedEvent.endDate
                        ? format(new Date(selectedEvent.date), "MMMM d, yyyy")
                        : `${format(new Date(selectedEvent.date), "MMMM d, yyyy")} - ${format(new Date(selectedEvent.endDate), "MMMM d, yyyy")}`}
                    </p>
                  </div>

                  {!selectedEvent.allDay && (
                    <div>
                      <h4 className="text-sm font-medium">Time</h4>
                      <p className="text-sm">
                        {selectedEvent.startTime} - {selectedEvent.endTime}
                      </p>
                    </div>
                  )}

                  <div>
                    <h4 className="text-sm font-medium">Description</h4>
                    <p className="text-sm">{selectedEvent.description}</p>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsViewEventDialogOpen(false)}>
                  Close
                </Button>
                <Button>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
