import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import { verify } from "jsonwebtoken"

export async function GET() {
  try {
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get("session")?.value
    const accessToken = cookieStore.get("accessToken")?.value

    // First try to use session token (local JWT)
    if (sessionToken) {
      try {
        const decoded = verify(sessionToken, process.env.JWT_SECRET!) as any
        return NextResponse.json({
          user: {
            id: decoded.id,
            email: decoded.email,
            firstName: decoded.firstName || "",
            lastName: decoded.lastName || "",
            role: decoded.role,
            isEmailVerified: decoded.isEmailVerified || false,
          },
        })
      } catch (jwtError) {
        console.error("Session token verification failed:", jwtError)
        // Continue to try access token
      }
    }

    // Fallback to access token with backend call
    if (!accessToken) {
      return NextResponse.json({ user: null })
    }

    // Call your backend API to get user details
    const response = await fetch(
      `${process.env.BACKEND_URL}/api/v1/auth/me`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        // Add timeout to prevent hanging
        signal: AbortSignal.timeout(5000),
      }
    )

    if (!response.ok) {
      return NextResponse.json({ user: null })
    }

    const data = await response.json()

    return NextResponse.json({
      user: {
        id: data.id,
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        role: data.role,
        isEmailVerified: data.isEmailVerified,
      },
    })
  } catch (error) {
    console.error("Session check error:", error)
    return NextResponse.json({ user: null })
  }
}