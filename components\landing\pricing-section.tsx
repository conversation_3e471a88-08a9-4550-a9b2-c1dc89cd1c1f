"use client"

import { useState, useRef } from "react"
import { motion, useInView } from "framer-motion"
import { Check, HelpCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

export function PricingSection() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const [isAnnual, setIsAnnual] = useState(false)

  const plans = [
    {
      name: "Basic",
      price: isAnnual ? "K5,000" : "K500",
      period: isAnnual ? "per year" : "per month",
      description: "Perfect for small schools",
      features: [
        "Up to 200 students",
        "Basic student management",
        "Attendance tracking",
        "5 staff accounts",
        "Email support",
      ],
      highlighted: false,
      buttonText: "Get Started",
      discount: isAnnual ? "Save K1,000" : null,
    },
    {
      name: "Standard",
      price: isAnnual ? "K12,000" : "K1,200",
      period: isAnnual ? "per year" : "per month",
      description: "Ideal for medium-sized schools",
      features: [
        "Up to 500 students",
        "Advanced student management",
        "Fee management",
        "Timetable scheduling",
        "15 staff accounts",
        "Priority email support",
        "Parent portal access",
      ],
      highlighted: true,
      buttonText: "Most Popular",
      discount: isAnnual ? "Save K2,400" : null,
    },
    {
      name: "Premium",
      price: isAnnual ? "K25,000" : "K2,500",
      period: isAnnual ? "per year" : "per month",
      description: "For large educational institutions",
      features: [
        "Unlimited students",
        "Complete management suite",
        "Advanced analytics",
        "Unlimited staff accounts",
        "24/7 priority support",
        "Custom integrations",
        "Multi-campus support",
      ],
      highlighted: false,
      buttonText: "Contact Sales",
      discount: isAnnual ? "Save K5,000" : null,
    },
  ]

  const featureTooltips = {
    "Basic student management": "Manage student profiles, basic records, and simple reporting.",
    "Advanced student management":
      "Comprehensive student profiles, custom fields, advanced reporting, and student history tracking.",
    "Complete management suite":
      "Full access to all modules including HR, inventory, library, and transportation management.",
    "Fee management": "Create fee structures, generate invoices, track payments, and send reminders.",
    "Advanced analytics": "Customizable dashboards, trend analysis, predictive insights, and exportable reports.",
    "Custom integrations": "Connect with third-party systems like accounting software, payment gateways, and more.",
    "Multi-campus support": "Manage multiple branches or campuses under a single administration.",
  }

  return (
    <section id="pricing" ref={ref} className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="text-3xl sm:text-4xl font-bold text-gray-900"
          >
            Simple, Transparent Pricing
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto"
          >
            Choose the plan that fits your institution's needs
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex items-center justify-center mt-8"
          >
            <div className="flex items-center space-x-2">
              <span className={`text-sm ${!isAnnual ? "font-medium text-gray-900" : "text-gray-500"}`}>Monthly</span>
              <Switch checked={isAnnual} onCheckedChange={setIsAnnual} id="billing-toggle" />
              <Label
                htmlFor="billing-toggle"
                className={`text-sm ${isAnnual ? "font-medium text-gray-900" : "text-gray-500"}`}
              >
                Annual{" "}
                <span className="bg-emerald-100 text-emerald-800 text-xs px-2 py-0.5 rounded-full ml-1">Save 20%</span>
              </Label>
            </div>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
              className={`bg-white rounded-xl overflow-hidden shadow-lg transition-all hover:shadow-xl ${
                plan.highlighted ? "ring-2 ring-emerald-500 transform scale-105 md:scale-110" : ""
              }`}
            >
              {plan.highlighted && (
                <div className="bg-emerald-500 text-white text-center text-sm font-medium py-1">MOST POPULAR</div>
              )}
              <div className={`p-6 ${plan.highlighted ? "bg-emerald-50" : "bg-gray-50"}`}>
                <h3 className={`text-2xl font-bold ${plan.highlighted ? "text-emerald-600" : "text-gray-900"}`}>
                  {plan.name}
                </h3>
                <div className="mt-4 flex items-baseline">
                  <span className="text-4xl font-extrabold text-gray-900">{plan.price}</span>
                  <span className="ml-1 text-xl text-gray-500">{plan.period}</span>
                </div>
                {plan.discount && (
                  <span className="mt-1 inline-block bg-emerald-100 text-emerald-800 text-xs px-2 py-0.5 rounded-full">
                    {plan.discount}
                  </span>
                )}
                <p className="mt-2 text-gray-500">{plan.description}</p>
              </div>
              <div className="p-6">
                <ul className="space-y-4">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <Check className="h-5 w-5 text-emerald-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-600">{feature}</span>
                      {featureTooltips[feature] && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-gray-400 ml-1 cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="max-w-xs">{featureTooltips[feature]}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </li>
                  ))}
                </ul>
                <div className="mt-8">
                  <Link href="/auth/register">
                    <Button
                      className={`w-full ${plan.highlighted ? "bg-emerald-600 hover:bg-emerald-700" : ""}`}
                      variant={plan.highlighted ? "default" : "outline"}
                    >
                      {plan.buttonText}
                    </Button>
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <div className="mt-16 text-center max-w-3xl mx-auto">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Need a custom plan?</h3>
          <p className="text-gray-600 mb-6">
            We offer tailored solutions for educational institutions with specific requirements. Contact our sales team
            to discuss your needs.
          </p>
          <Link href="/contact">
            <Button variant="outline" size="lg">
              Contact Sales
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
