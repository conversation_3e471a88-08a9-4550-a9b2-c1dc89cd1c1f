import { cookies } from "next/headers"
import { NextRequest } from "next/server"

/**
 * Get authentication token from cookies
 * Handles the Next.js 15 requirement to await cookies()
 */
export async function getAuthToken(): Promise<string | null> {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get("accessToken")?.value || null
    // Only log token presence in development, never the actual token
    if (process.env.NODE_ENV === "development") {
      console.log("🔍 getAuthToken: accessToken from cookies:", token ? "✅ Found" : "❌ Not found")
    }
    return token
  } catch (error) {
    console.error("Error getting auth token:", error)
    return null
  }
}

/**
 * Get authentication token from request headers as fallback
 */
export function getAuthTokenFromHeaders(request: NextRequest): string | null {
  const authHeader = request.headers.get("authorization")
  if (authHeader && authHeader.startsWith("Bearer ")) {
    return authHeader.split(" ")[1]
  }
  return null
}

/**
 * Get authentication token with fallback to headers
 */
export async function getAuthTokenWithFallback(request: NextRequest): Promise<string | null> {
  console.log("🔍 getAuthTokenWithFallback: Trying cookies first...")
  // Try cookies first
  const tokenFromCookies = await getAuthToken()
  if (tokenFromCookies) {
    console.log("✅ getAuthTokenWithFallback: Found token in cookies")
    return tokenFromCookies
  }

  console.log("🔍 getAuthTokenWithFallback: No token in cookies, trying headers...")
  // Fallback to headers
  const tokenFromHeaders = getAuthTokenFromHeaders(request)
  console.log("🔍 getAuthTokenWithFallback: Token from headers:", tokenFromHeaders ? "✅ Found" : "❌ Not found")
  return tokenFromHeaders
}

/**
 * Refresh access token using refresh token (server-side)
 */
/**
 * Global refresh state to prevent multiple simultaneous refresh attempts
 */
let isRefreshingServer = false
let refreshPromiseServer: Promise<string | null> | null = null

export async function refreshAccessTokenServer(): Promise<string | null> {
  // If already refreshing, return the existing promise
  if (isRefreshingServer && refreshPromiseServer) {
    console.log("🔄 refreshAccessTokenServer: Already refreshing, waiting...")
    return refreshPromiseServer
  }

  isRefreshingServer = true
  refreshPromiseServer = (async () => {
    try {
      console.log("🔄 refreshAccessTokenServer: Starting token refresh...")
      const cookieStore = await cookies()
      const refreshToken = cookieStore.get("refreshToken")?.value

      if (!refreshToken) {
        console.log("❌ refreshAccessTokenServer: No refresh token found")
        return null
      }

      const backendUrl = process.env.BACKEND_URL || "http://localhost:4000"
      const response = await fetch(`${backendUrl}/api/v1/auth/refresh-token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Cookie: `refreshToken=${refreshToken}`,
        },
      })

      if (!response.ok) {
        console.log("❌ refreshAccessTokenServer: Backend refresh failed:", response.status)
        return null
      }

      const data = await response.json()
      const newAccessToken = data.data?.accessToken

      if (newAccessToken) {
        console.log("✅ refreshAccessTokenServer: Got new access token, updating cookie...")
        // Update the access token cookie
        cookieStore.set("accessToken", newAccessToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax",
          maxAge: 15 * 60, // 15 minutes
        })
        console.log("✅ refreshAccessTokenServer: Cookie updated successfully")
        return newAccessToken
      }

      console.log("❌ refreshAccessTokenServer: No access token in response")
      return null
    } catch (error) {
      console.error("❌ refreshAccessTokenServer: Error refreshing token:", error)
      return null
    } finally {
      isRefreshingServer = false
      refreshPromiseServer = null
    }
  })()

  return refreshPromiseServer
}

/**
 * JWT token validation without external calls
 */
function isTokenExpired(token: string): boolean {
  try {
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString())
    const currentTime = Math.floor(Date.now() / 1000)
    return payload.exp < currentTime
  } catch (error) {
    console.error("Error parsing token:", error)
    return true // Treat invalid tokens as expired
  }
}

/**
 * Get authentication token with automatic refresh on expiry
 */
export async function getValidAuthToken(request?: NextRequest): Promise<string | null> {
  let token = request ? await getAuthTokenWithFallback(request) : await getAuthToken()

  if (!token) {
    console.log("🔍 getValidAuthToken: No token found")
    return null
  }

  // Check if token is expired locally first (faster than backend call)
  if (isTokenExpired(token)) {
    console.log("🔄 getValidAuthToken: Token expired locally, attempting refresh...")
    const newToken = await refreshAccessTokenServer()
    console.log("🔄 getValidAuthToken: Refresh result:", newToken ? "✅ Success" : "❌ Failed")
    return newToken
  }

  console.log("✅ getValidAuthToken: Token is valid")
  return token
}
