// Institution registration controller
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const bcrypt = require('bcryptjs');
const catchAsync = require('../../../utils/catchAsync');
const AppError = require('../../../utils/appError');
const { sendEmail } = require('../../../services/emailService');
const { createToken } = require('../../../services/tokenService');
const { logAudit } = require('../../../services/auditService');

// Helper to generate a random verification token
const generateVerificationToken = () => {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
};

exports.registerInstitution = catchAsync(async (req, res, next) => {

  // Extract institution data from request
  const {
    institutionName,
    institutionType,
    address,
    city,
    state,
    country,
    postalCode,
    phone,
    institutionEmail,
    website,

    // School details
    schoolName,
    schoolType,
    schoolAddress,
    schoolCity,
    schoolState,
    schoolCountry,
    schoolPostalCode,
    schoolPhone,
    schoolEmail,
    schoolWebsite,

    // Admin details
    adminFirstName,
    adminLastName,
    adminEmail,
    adminPhone,
    adminPassword,

    // Additional info
    studentCount,
    teacherCount,
    subscriptionPlan,
    referralSource,
    specialRequirements
  } = req.body;

  // Validate required fields
  if (!institutionName || !institutionEmail || !adminFirstName || !adminLastName || !adminEmail || !adminPassword) {
    return next(new AppError('Missing required fields: institutionName, institutionEmail, adminFirstName, adminLastName, adminEmail, and adminPassword are required', 400));
  }

  // Validate school fields - use institution data as defaults if school data not provided
  const finalSchoolName = schoolName || institutionName;
  const validSchoolTypes = ['PRIMARY', 'SECONDARY', 'TERTIARY', 'KINDERGARTEN', 'VOCATIONAL', 'SPECIAL_NEEDS'];
  const finalSchoolType = schoolType && validSchoolTypes.includes(schoolType) ? schoolType : 'TERTIARY';
  const finalSchoolAddress = schoolAddress || address;
  const finalSchoolCity = schoolCity || city;
  const finalSchoolState = schoolState || state;
  const finalSchoolCountry = schoolCountry || country;
  const finalSchoolPostalCode = schoolPostalCode || postalCode;
  const finalSchoolPhone = schoolPhone || phone;
  const finalSchoolEmail = schoolEmail || institutionEmail;
  const finalSchoolWebsite = schoolWebsite || website;

  // Validate school email if provided
  if (finalSchoolEmail && finalSchoolEmail !== institutionEmail) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(finalSchoolEmail)) {
      return next(new AppError('Please provide a valid school email address', 400));
    }
  }

  // Check if institution email already exists
  const existingInstitution = await prisma.institution.findFirst({
    where: {
      email: institutionEmail
    }
  });

  if (existingInstitution) {
    return next(new AppError('An institution with this email already exists', 400));
  }

  // Check if admin email already exists
  const existingUser = await prisma.user.findUnique({
    where: { email: adminEmail }
  });

  if (existingUser) {
    return next(new AppError('A user with this email already exists', 400));
  }
  
  // Handle file upload if present
  let logoUrl = null;
  if (req.file) {
    try {
      // Store relative path for the logo
      logoUrl = `/uploads/${req.file.filename}`;
      console.log('Logo uploaded successfully:', logoUrl);
    } catch (fileError) {
      console.error('Error processing uploaded file:', fileError);
      // Continue without logo if file processing fails
      logoUrl = null;
    }
  }
  
  // Create transaction with increased timeout
  const result = await prisma.$transaction(
    async (tx) => {
      const hashedPassword = await bcrypt.hash(adminPassword, 12);
      const emailVerificationToken = generateVerificationToken();
      
      // 1. Create the institution
      const institution = await tx.institution.create({
        data: {
          name: institutionName,
          email: institutionEmail,
          logo: logoUrl,
          subscriptionStatus: 'TRIAL',
          subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          // Additional registration fields from the schema
          address: address || null,
          city: city || null,
          state: state || null,
          country: country || null,
          postalCode: postalCode || null,
          phoneNumber: phone || null,
          website: website || null,
          registrationDate: new Date(),
          verificationStatus: false,
          studentCount: studentCount ? parseInt(studentCount) : null,
          teacherCount: teacherCount ? parseInt(teacherCount) : null,
          referralSource: referralSource || null,
          specialRequirements: specialRequirements || null,
        },
      });
      
      // 2. Create the admin user
      const user = await tx.user.create({
        data: {
          email: adminEmail,
          passwordHash: hashedPassword,
          firstName: adminFirstName,
          lastName: adminLastName,
          phoneNumber: adminPhone,
          role: 'INSTITUTION_ADMIN',
          emailVerificationToken,
          emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      });
      
      // 3. Create the school under the institution
      const school = await tx.school.create({
        data: {
          name: finalSchoolName,
          type: finalSchoolType,
          institutionId: institution.id,
          address: finalSchoolAddress || null,
          city: finalSchoolCity || null,
          state: finalSchoolState || null,
          country: finalSchoolCountry || null,
          postalCode: finalSchoolPostalCode || null,
          phoneNumber: finalSchoolPhone || null,
          email: finalSchoolEmail || null,
          website: finalSchoolWebsite || null,
          isActive: true,
        },
      });

      // 4. Link the user to the institution
      await tx.institutionUser.create({
        data: {
          userId: user.id,
          institutionId: institution.id,
          role: 'INSTITUTION_ADMIN',
        },
      });

      // 5. Link the user to the school
      await tx.schoolUser.create({
        data: {
          userId: user.id,
          schoolId: school.id,
          role: 'SCHOOL_ADMIN',
        },
      });
      
      // 6. Create an audit log entry
      await tx.auditLog.create({
        data: {
          userId: user.id,
          action: 'INSTITUTION_REGISTRATION',
          details: `Registered institution ${institutionName}`,
          resourceType: 'Institution',
          resourceId: institution.id,
        },
      });
      
      return { user, institution, school, verificationToken: emailVerificationToken };
    },
    {
      timeout: 30000, // Increase timeout to 30 seconds
      maxWait: 10000,  // Maximum time to wait for transaction to start
    }
  );
  
  // Send verification email (handle failures gracefully)
  let emailSent = false;
  try {
    const verificationLink = `${process.env.BACKEND_URL || 'http://localhost:4000'}/api/v1/institutions/verify-email/${result.verificationToken}`;

    await sendEmail({
      to: adminEmail,
      subject: 'Verify your email - EduSync',
      html: `
        <h1>Welcome to EduSync!</h1>
        <p>Thank you for registering ${institutionName}!</p>
        <p>Please verify your email by clicking the link below:</p>
        <a href="${verificationLink}">Verify Email</a>
        <p>This link will expire in 24 hours.</p>
        <p>If you did not request this, please ignore this email.</p>
      `,
    });
    emailSent = true;
  } catch (emailError) {
    console.error('Failed to send verification email:', emailError);
    // Don't fail the registration if email fails
    // The user can still verify later or admin can resend
  }
  
  // Return success response
  res.status(201).json({
    status: 'success',
    message: emailSent
      ? 'Institution registered successfully! Please check your email to verify your account.'
      : 'Institution registered successfully! Email verification will be sent shortly.',
    data: {
      institutionId: result.institution.id,
      verificationToken: result.verificationToken,
      emailSent: emailSent,
      institution: {
        id: result.institution.id,
        name: result.institution.name,
        email: result.institution.email,
      },
      school: {
        id: result.school.id,
        name: result.school.name,
        type: result.school.type,
        email: result.school.email,
      },
      user: {
        id: result.user.id,
        email: result.user.email,
        firstName: result.user.firstName,
        lastName: result.user.lastName,
        role: result.user.role,
      },
    },
  });
});

exports.verifyEmail = catchAsync(async (req, res, next) => {
  const { token } = req.params;
  
  // Find user with this verification token
  const user = await prisma.user.findFirst({
    where: {
      emailVerificationToken: token,
      emailVerificationExpires: {
        gt: new Date(),
      },
    },
  });
  
  if (!user) {
    return next(new AppError('Invalid or expired verification token', 400));
  }
  
  // Update user to verified
  await prisma.user.update({
    where: { id: user.id },
    data: {
      isEmailVerified: true,
      emailVerificationToken: null,
      emailVerificationExpires: null,
    },
  });
  
  // Create audit log
  await logAudit({
    userId: user.id,
    action: 'EMAIL_VERIFICATION',
    details: 'Email verified successfully',
    resourceType: 'User',
    resourceId: user.id,
  });
  
  // Redirect to frontend verification success page
  res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:3001'}/auth/verify-email/success`);
});

// Get all institutions (admin only)
exports.getAllInstitutions = catchAsync(async (req, res, next) => {
  // Pagination parameters
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;
  
  // Filtering options
  const filter = {};
  if (req.query.isActive) {
    filter.isActive = req.query.isActive === 'true';
  }
  if (req.query.subscriptionStatus) {
    filter.subscriptionStatus = req.query.subscriptionStatus;
  }
  
  // Get institutions with pagination
  const institutions = await prisma.institution.findMany({
    where: filter,
    skip,
    take: limit,
    orderBy: {
      createdAt: 'desc'
    },
    include: {
      schools: {
        select: {
          id: true,
          name: true,
        }
      },
      users: {
        select: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              role: true,
            }
          }
        },
        where: {
          role: 'INSTITUTION_ADMIN'
        },
        take: 1
      }
    }
  });
  
  // Get total count for pagination
  const total = await prisma.institution.count({ where: filter });
  
  res.status(200).json({
    status: 'success',
    data: {
      institutions,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// Get a single institution
exports.getInstitution = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  const institution = await prisma.institution.findUnique({
    where: { id },
    include: {
      schools: true,
      users: {
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              role: true,
              isActive: true
            }
          }
        }
      }
    }
  });
  
  if (!institution) {
    return next(new AppError('Institution not found', 404));
  }
  
  res.status(200).json({
    status: 'success',
    data: {
      institution
    }
  });
});

// Update an institution
exports.updateInstitution = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  // Check if institution exists
  const institution = await prisma.institution.findUnique({
    where: { id }
  });
  
  if (!institution) {
    return next(new AppError('Institution not found', 404));
  }
  
  // Extract update data
  const {
    name,
    domain,
    subscriptionStatus,
    subscriptionEndDate,
    isActive,
    primaryColor,
    secondaryColor
  } = req.body;
  
  // Handle logo update if provided
  let logoUrl = institution.logo;
  if (req.file) {
    // In a real app, you would upload to a cloud storage
    logoUrl = `/uploads/${req.file.filename}`;
  }
  
  // Update the institution
  const updatedInstitution = await prisma.institution.update({
    where: { id },
    data: {
      name,
      domain,
      logo: logoUrl,
      primaryColor,
      secondaryColor,
      isActive: isActive !== undefined ? isActive : institution.isActive,
      subscriptionStatus: subscriptionStatus || institution.subscriptionStatus,
      subscriptionEndDate: subscriptionEndDate ? new Date(subscriptionEndDate) : institution.subscriptionEndDate,
      updatedAt: new Date()
    }
  });
  
  // Create audit log
  await logAudit({
    userId: req.user.id,
    action: 'INSTITUTION_UPDATE',
    details: `Updated institution ${name}`,
    resourceType: 'Institution',
    resourceId: id,
  });
  
  res.status(200).json({
    status: 'success',
    data: {
      institution: updatedInstitution
    }
  });
});

// Delete an institution
exports.deleteInstitution = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  // Check if institution exists
  const institution = await prisma.institution.findUnique({
    where: { id }
  });
  
  if (!institution) {
    return next(new AppError('Institution not found', 404));
  }
  
  // Delete the institution (this will cascade delete related records due to Prisma relations)
  await prisma.institution.delete({
    where: { id }
  });
  
  // Create audit log
  await logAudit({
    userId: req.user.id,
    action: 'INSTITUTION_DELETE',
    details: `Deleted institution ${institution.name}`,
    resourceType: 'Institution',
    resourceId: id,
  });
  
  res.status(204).json({
    status: 'success',
    data: null
  });
});
