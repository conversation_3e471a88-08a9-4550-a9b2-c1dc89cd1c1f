/**
 * Error handling utilities for the application
 */

/**
 * Format API errors for displaying to users
 */
export function formatApiError(error: any): string {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  if (error?.error) {
    return error.error;
  }
  
  return 'An unexpected error occurred. Please try again.';
}

/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  status: number;
  data: any;
  
  constructor(message: string, status: number = 500, data: any = null) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

/**
 * Format validation errors from the backend
 */
export function formatValidationErrors(errors: Record<string, string[]>): Record<string, string> {
  const formattedErrors: Record<string, string> = {};
  
  Object.entries(errors).forEach(([field, messages]) => {
    formattedErrors[field] = Array.isArray(messages) ? messages[0] : messages as unknown as string;
  });
  
  return formattedErrors;
}

/**
 * Handle API response and throw error if not successful
 */
export function handleApiResponse(response: any) {
  if (!response.success) {
    throw new ApiError(
      response.error || 'An unexpected error occurred',
      response.status || 500,
      response.data
    );
  }
  
  return response.data;
}
