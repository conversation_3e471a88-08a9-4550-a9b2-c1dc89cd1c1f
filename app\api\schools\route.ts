import { NextResponse } from "next/server"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    // Forward the request to the backend API
    const response = await fetch(`${BACKEND_URL}/api/v1/schools`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: request.headers.get("authorization") || "",
        Cookie: request.headers.get("cookie") || "",
      },
      body: JSON.stringify(body),
    })

    const responseData = await response.json()

    if (!response.ok) {
      return NextResponse.json(responseData, { status: response.status })
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error("Schools API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    const institutionId = searchParams.get('institutionId')

    // Get auth token from cookies or headers
    const authToken =
      request.headers.get("authorization")?.replace("Bearer ", "") ||
      request.headers.get("cookie")?.split("accessToken=")[1]?.split(";")[0]

    // Build query string
    const queryString = new URLSearchParams(queryParams).toString()

    // Build headers
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    }

    // Add authorization header if token is available
    if (authToken) {
      headers["Authorization"] = `Bearer ${authToken}`
    }

    // Forward the request to the backend API
    const response = await fetch(`${BACKEND_URL}/api/v1/schools${queryString ? `?${queryString}` : ''}`, {
      headers,
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Schools API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
