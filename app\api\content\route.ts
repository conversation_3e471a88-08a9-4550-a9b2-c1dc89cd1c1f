import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'features'
    
    // Extract query parameters for backend
    const queryParams = new URLSearchParams()
    searchParams.forEach((value, key) => {
      if (key !== 'type') {
        queryParams.append(key, value)
      }
    })

    const backendUrl = process.env.BACKEND_URL || 'http://localhost:4000'
    const endpoint = `/api/v1/super-admin/content/${type}`
    
    const response = await fetch(`${backendUrl}${endpoint}?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.get('Authorization') || '',
        'X-User-Id': request.headers.get('X-User-Id') || '',
        'X-User-Role': request.headers.get('X-User-Role') || '',
        'X-Institution-Id': request.headers.get('X-Institution-Id') || '',
      },
    })

    const data = await response.json()
    
    return NextResponse.json(data, { status: response.status })
  } catch (error) {
    console.error('Content API Error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to fetch content data'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'features'
    
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:4000'
    const endpoint = `/api/v1/super-admin/content/${type}`
    
    const response = await fetch(`${backendUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.get('Authorization') || '',
        'X-User-Id': request.headers.get('X-User-Id') || '',
        'X-User-Role': request.headers.get('X-User-Role') || '',
        'X-Institution-Id': request.headers.get('X-Institution-Id') || '',
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()
    
    return NextResponse.json(data, { status: response.status })
  } catch (error) {
    console.error('Content API Error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to create content'
      },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'features'
    
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:4000'
    const endpoint = `/api/v1/super-admin/content/${type}`
    
    const response = await fetch(`${backendUrl}${endpoint}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.get('Authorization') || '',
        'X-User-Id': request.headers.get('X-User-Id') || '',
        'X-User-Role': request.headers.get('X-User-Role') || '',
        'X-Institution-Id': request.headers.get('X-Institution-Id') || '',
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()
    
    return NextResponse.json(data, { status: response.status })
  } catch (error) {
    console.error('Content API Error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to update content'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'features'
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing ID parameter',
          message: 'Content ID is required for deletion'
        },
        { status: 400 }
      )
    }
    
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:4000'
    const endpoint = `/api/v1/super-admin/content/${type}/${id}`
    
    const response = await fetch(`${backendUrl}${endpoint}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.get('Authorization') || '',
        'X-User-Id': request.headers.get('X-User-Id') || '',
        'X-User-Role': request.headers.get('X-User-Role') || '',
        'X-Institution-Id': request.headers.get('X-Institution-Id') || '',
      },
    })

    const data = await response.json()
    
    return NextResponse.json(data, { status: response.status })
  } catch (error) {
    console.error('Content API Error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to delete content'
      },
      { status: 500 }
    )
  }
}
