const express = require('express');
const router = express.Router();
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const {
  getSubscriptionStats,
  getSubscriptions,
  getSubscriptionById,
  createSubscription,
  updateSubscription,
  cancelSubscription,
  getPaymentHistory,
  generateSubscriptionReport,
  getSubscriptionPlans,
  createSubscriptionPlan,
  updateSubscriptionPlan,
  deleteSubscriptionPlan
} = require('./subscriptions.controller');

// Protect all routes and restrict to SUPER_ADMIN
router.use(authenticate);
router.use(restrictTo('SUPER_ADMIN'));

// Subscription statistics
router.get('/stats', getSubscriptionStats);

// Subscription management
router.get('/', getSubscriptions);
router.get('/:id', getSubscriptionById);
router.post('/', createSubscription);
router.put('/:id', updateSubscription);
router.delete('/:id/cancel', cancelSubscription);

// Payment history
router.get('/payments/history', getPaymentHistory);

// Subscription plans management
router.get('/plans', getSubscriptionPlans);
router.post('/plans', createSubscriptionPlan);
router.put('/plans/:id', updateSubscriptionPlan);
router.delete('/plans/:id', deleteSubscriptionPlan);

module.exports = router;
