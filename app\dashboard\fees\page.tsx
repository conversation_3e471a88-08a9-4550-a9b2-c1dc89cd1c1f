"use client"

import { useState } from "react"
import { format } from "date-fns"
import { CalendarIcon, Plus, Download, CreditCard, DollarSign, FileText, Users } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"

// Mock data for fee types
const feeTypes = [
  { id: 1, name: "Tuition Fee", description: "Monthly tuition fee", amount: 5000, frequency: "Monthly" },
  { id: 2, name: "Admission Fee", description: "One-time admission fee", amount: 10000, frequency: "One-time" },
  { id: 3, name: "Examination Fee", description: "Per semester examination fee", amount: 2000, frequency: "Semester" },
  { id: 4, name: "Library Fee", description: "Annual library fee", amount: 1500, frequency: "Annual" },
  { id: 5, name: "Sports Fee", description: "Annual sports fee", amount: 1000, frequency: "Annual" },
  { id: 6, name: "Computer Lab Fee", description: "Monthly computer lab fee", amount: 800, frequency: "Monthly" },
]

// Mock data for fee payments
const feePayments = [
  {
    id: 1,
    studentId: 101,
    studentName: "Alice Johnson",
    class: "Class 10A",
    feeType: "Tuition Fee",
    amount: 5000,
    paymentDate: new Date(2023, 8, 5),
    paymentMethod: "Online",
    status: "Paid",
    receiptNo: "REC-2023-001",
  },
  {
    id: 2,
    studentId: 102,
    studentName: "Bob Smith",
    class: "Class 9B",
    feeType: "Tuition Fee",
    amount: 5000,
    paymentDate: new Date(2023, 8, 7),
    paymentMethod: "Cash",
    status: "Paid",
    receiptNo: "REC-2023-002",
  },
  {
    id: 3,
    studentId: 103,
    studentName: "Charlie Brown",
    class: "Class 8C",
    feeType: "Tuition Fee",
    amount: 5000,
    paymentDate: null,
    paymentMethod: "",
    status: "Pending",
    receiptNo: "",
  },
  {
    id: 4,
    studentId: 104,
    studentName: "Diana Prince",
    class: "Class 10A",
    feeType: "Examination Fee",
    amount: 2000,
    paymentDate: new Date(2023, 8, 10),
    paymentMethod: "Online",
    status: "Paid",
    receiptNo: "REC-2023-003",
  },
  {
    id: 5,
    studentId: 105,
    studentName: "Edward Norton",
    class: "Class 9B",
    feeType: "Library Fee",
    amount: 1500,
    paymentDate: new Date(2023, 8, 12),
    paymentMethod: "Cheque",
    status: "Paid",
    receiptNo: "REC-2023-004",
  },
  {
    id: 6,
    studentId: 106,
    studentName: "Fiona Gallagher",
    class: "Class 8C",
    feeType: "Sports Fee",
    amount: 1000,
    paymentDate: null,
    paymentMethod: "",
    status: "Pending",
    receiptNo: "",
  },
]

// Mock data for students
const students = [
  { id: 101, name: "Alice Johnson", class: "Class 10A" },
  { id: 102, name: "Bob Smith", class: "Class 9B" },
  { id: 103, name: "Charlie Brown", class: "Class 8C" },
  { id: 104, name: "Diana Prince", class: "Class 10A" },
  { id: 105, name: "Edward Norton", class: "Class 9B" },
  { id: 106, name: "Fiona Gallagher", class: "Class 8C" },
]

export default function FeesPage() {
  const [isAddFeeTypeOpen, setIsAddFeeTypeOpen] = useState(false)
  const [isAddPaymentOpen, setIsAddPaymentOpen] = useState(false)
  const [newFeeType, setNewFeeType] = useState({
    name: "",
    description: "",
    amount: 0,
    frequency: "",
  })
  const [newPayment, setNewPayment] = useState({
    studentId: 0,
    feeType: "",
    amount: 0,
    paymentDate: new Date(),
    paymentMethod: "",
  })
  const [statusFilter, setStatusFilter] = useState("all")

  const filteredPayments =
    statusFilter === "all"
      ? feePayments
      : feePayments.filter((payment) => payment.status.toLowerCase() === statusFilter)

  const totalCollected = feePayments
    .filter((payment) => payment.status === "Paid")
    .reduce((sum, payment) => sum + payment.amount, 0)

  const totalPending = feePayments
    .filter((payment) => payment.status === "Pending")
    .reduce((sum, payment) => sum + payment.amount, 0)

  const handleAddFeeType = () => {
    // In a real app, this would add to the database
    setIsAddFeeTypeOpen(false)
    setNewFeeType({
      name: "",
      description: "",
      amount: 0,
      frequency: "",
    })
  }

  const handleAddPayment = () => {
    // In a real app, this would add to the database
    setIsAddPaymentOpen(false)
    setNewPayment({
      studentId: 0,
      feeType: "",
      amount: 0,
      paymentDate: new Date(),
      paymentMethod: "",
    })
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Fees Management</h2>
          <p className="text-muted-foreground">Manage fee structures and track payments</p>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Collected</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totalCollected.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">+20.1% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totalPending.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {feePayments.filter((payment) => payment.status === "Pending").length} students with pending fees
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fee Types</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{feeTypes.length}</div>
            <p className="text-xs text-muted-foreground">Active fee categories</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{students.length}</div>
            <p className="text-xs text-muted-foreground">Total students with fee records</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="payments">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="payments">Fee Payments</TabsTrigger>
          <TabsTrigger value="structure">Fee Structure</TabsTrigger>
        </TabsList>
        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Fee Payments</CardTitle>
                <CardDescription>View and manage student fee payments</CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Payments</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
                <Dialog open={isAddPaymentOpen} onOpenChange={setIsAddPaymentOpen}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Add Payment
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                      <DialogTitle>Record Fee Payment</DialogTitle>
                      <DialogDescription>Enter the details to record a new fee payment.</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="student" className="text-right">
                          Student
                        </Label>
                        <Select
                          value={newPayment.studentId.toString()}
                          onValueChange={(value) => setNewPayment({ ...newPayment, studentId: Number.parseInt(value) })}
                        >
                          <SelectTrigger id="student" className="col-span-3">
                            <SelectValue placeholder="Select student" />
                          </SelectTrigger>
                          <SelectContent>
                            {students.map((student) => (
                              <SelectItem key={student.id} value={student.id.toString()}>
                                {student.name} ({student.class})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="fee-type" className="text-right">
                          Fee Type
                        </Label>
                        <Select
                          value={newPayment.feeType}
                          onValueChange={(value) => setNewPayment({ ...newPayment, feeType: value })}
                        >
                          <SelectTrigger id="fee-type" className="col-span-3">
                            <SelectValue placeholder="Select fee type" />
                          </SelectTrigger>
                          <SelectContent>
                            {feeTypes.map((feeType) => (
                              <SelectItem key={feeType.id} value={feeType.name}>
                                {feeType.name} (₹{feeType.amount})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="amount" className="text-right">
                          Amount
                        </Label>
                        <Input
                          id="amount"
                          type="number"
                          value={newPayment.amount}
                          onChange={(e) => setNewPayment({ ...newPayment, amount: Number.parseFloat(e.target.value) })}
                          className="col-span-3"
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label className="text-right">Payment Date</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "col-span-3 justify-start text-left font-normal",
                                !newPayment.paymentDate && "text-muted-foreground",
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {newPayment.paymentDate ? (
                                format(newPayment.paymentDate, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={newPayment.paymentDate}
                              onSelect={(date) => date && setNewPayment({ ...newPayment, paymentDate: date })}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="payment-method" className="text-right">
                          Payment Method
                        </Label>
                        <Select
                          value={newPayment.paymentMethod}
                          onValueChange={(value) => setNewPayment({ ...newPayment, paymentMethod: value })}
                        >
                          <SelectTrigger id="payment-method" className="col-span-3">
                            <SelectValue placeholder="Select payment method" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Cash">Cash</SelectItem>
                            <SelectItem value="Online">Online</SelectItem>
                            <SelectItem value="Cheque">Cheque</SelectItem>
                            <SelectItem value="Bank Transfer">Bank Transfer</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsAddPaymentOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleAddPayment}>Record Payment</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Student</TableHead>
                    <TableHead>Class</TableHead>
                    <TableHead>Fee Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Payment Date</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPayments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">{payment.studentName}</TableCell>
                      <TableCell>{payment.class}</TableCell>
                      <TableCell>{payment.feeType}</TableCell>
                      <TableCell>₹{payment.amount.toLocaleString()}</TableCell>
                      <TableCell>{payment.paymentDate ? format(payment.paymentDate, "MMM dd, yyyy") : "-"}</TableCell>
                      <TableCell>{payment.paymentMethod || "-"}</TableCell>
                      <TableCell>
                        <span
                          className={cn(
                            "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
                            payment.status === "Paid" && "bg-green-100 text-green-800",
                            payment.status === "Pending" && "bg-yellow-100 text-yellow-800",
                          )}
                        >
                          {payment.status}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        {payment.status === "Paid" ? (
                          <Button variant="outline" size="sm">
                            <Download className="mr-2 h-4 w-4" />
                            Receipt
                          </Button>
                        ) : (
                          <Button size="sm">
                            <CreditCard className="mr-2 h-4 w-4" />
                            Pay Now
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <div className="text-sm text-muted-foreground">
                Showing {filteredPayments.length} of {feePayments.length} payments
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="structure" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Fee Structure</CardTitle>
                <CardDescription>Manage fee types and amounts</CardDescription>
              </div>
              <Dialog open={isAddFeeTypeOpen} onOpenChange={setIsAddFeeTypeOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Fee Type
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[600px]">
                  <DialogHeader>
                    <DialogTitle>Add New Fee Type</DialogTitle>
                    <DialogDescription>Create a new fee type for the institution.</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="fee-name" className="text-right">
                        Fee Name
                      </Label>
                      <Input
                        id="fee-name"
                        value={newFeeType.name}
                        onChange={(e) => setNewFeeType({ ...newFeeType, name: e.target.value })}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="description" className="text-right">
                        Description
                      </Label>
                      <Input
                        id="description"
                        value={newFeeType.description}
                        onChange={(e) => setNewFeeType({ ...newFeeType, description: e.target.value })}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="amount" className="text-right">
                        Amount
                      </Label>
                      <Input
                        id="amount"
                        type="number"
                        value={newFeeType.amount}
                        onChange={(e) => setNewFeeType({ ...newFeeType, amount: Number.parseFloat(e.target.value) })}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="frequency" className="text-right">
                        Frequency
                      </Label>
                      <Select
                        value={newFeeType.frequency}
                        onValueChange={(value) => setNewFeeType({ ...newFeeType, frequency: value })}
                      >
                        <SelectTrigger id="frequency" className="col-span-3">
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="One-time">One-time</SelectItem>
                          <SelectItem value="Monthly">Monthly</SelectItem>
                          <SelectItem value="Quarterly">Quarterly</SelectItem>
                          <SelectItem value="Semester">Semester</SelectItem>
                          <SelectItem value="Annual">Annual</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddFeeTypeOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddFeeType}>Add Fee Type</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Fee Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Frequency</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {feeTypes.map((feeType) => (
                    <TableRow key={feeType.id}>
                      <TableCell className="font-medium">{feeType.name}</TableCell>
                      <TableCell>{feeType.description}</TableCell>
                      <TableCell>₹{feeType.amount.toLocaleString()}</TableCell>
                      <TableCell>{feeType.frequency}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
