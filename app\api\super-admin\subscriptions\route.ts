import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get("accessToken")?.value

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const endpoint = searchParams.get('endpoint') || 'subscriptions'
    
    // Build query string from search params, excluding endpoint
    const queryParams = new URLSearchParams()
    searchParams.forEach((value, key) => {
      if (key !== 'endpoint') {
        queryParams.append(key, value)
      }
    })

    let url = ''
    switch (endpoint) {
      case 'stats':
        url = `${BACKEND_URL}/api/v1/super-admin/subscriptions/stats`
        break
      case 'subscriptions':
        url = `${BACKEND_URL}/api/v1/super-admin/subscriptions`
        if (queryParams.toString()) {
          url += `?${queryParams.toString()}`
        }
        break
      case 'payments':
        url = `${BACKEND_URL}/api/v1/super-admin/subscriptions/payments/history`
        if (queryParams.toString()) {
          url += `?${queryParams.toString()}`
        }
        break
      case 'plans':
        url = `${BACKEND_URL}/api/v1/super-admin/subscriptions/plans`
        break
      default:
        return NextResponse.json({ error: 'Invalid endpoint' }, { status: 400 })
    }

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Subscriptions API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get("accessToken")?.value

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()

    const response = await fetch(`${BACKEND_URL}/api/v1/super-admin/subscriptions`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status })
    }

    return NextResponse.json(data, { status: 201 })
  } catch (error) {
    console.error("Subscription creation error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get("accessToken")?.value

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const endpoint = searchParams.get('endpoint')
    const id = searchParams.get('id')

    if (!endpoint || !id) {
      return NextResponse.json({ error: 'Missing endpoint or ID' }, { status: 400 })
    }

    const body = await request.json()

    let url = ''
    switch (endpoint) {
      case 'subscription':
        url = `${BACKEND_URL}/api/v1/super-admin/subscriptions/${id}`
        break
      case 'plan':
        url = `${BACKEND_URL}/api/v1/super-admin/subscriptions/plans/${id}`
        break
      default:
        return NextResponse.json({ error: 'Invalid endpoint' }, { status: 400 })
    }

    const response = await fetch(url, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Subscription update error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get("accessToken")?.value

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const endpoint = searchParams.get('endpoint')
    const id = searchParams.get('id')

    if (!endpoint || !id) {
      return NextResponse.json({ error: 'Missing endpoint or ID' }, { status: 400 })
    }

    let url = ''
    switch (endpoint) {
      case 'cancel':
        url = `${BACKEND_URL}/api/v1/super-admin/subscriptions/${id}/cancel`
        break
      case 'plan':
        url = `${BACKEND_URL}/api/v1/super-admin/subscriptions/plans/${id}`
        break
      default:
        return NextResponse.json({ error: 'Invalid endpoint' }, { status: 400 })
    }

    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Subscription delete error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
