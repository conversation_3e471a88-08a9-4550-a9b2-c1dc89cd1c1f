"use client"

import { useState, useRef } from "react"
import { motion, useInView } from "framer-motion"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Quote, ChevronLeft, ChevronRight, Star } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export function TestimonialsSection() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const [activeIndex, setActiveIndex] = useState(0)

  const testimonials = [
    {
      quote:
        "This system has transformed how we manage our school. The administrative burden has been reduced significantly, allowing us to focus more on education. The support team is always responsive and helpful.",
      author: "<PERSON>",
      role: "Principal, Lusaka Primary School",
      avatar: "/placeholder.svg?height=100&width=100",
      rating: 5,
    },
    {
      quote:
        "The parent portal has improved communication between our school and parents. They can now track their children's progress in real-time. We've seen a 40% increase in parent engagement since implementing this system.",
      author: "<PERSON>",
      role: "Head Teacher, Kitwe Secondary School",
      avatar: "/placeholder.svg?height=100&width=100",
      rating: 5,
    },
    {
      quote:
        "As a teacher, the attendance and grading systems have made my work much easier. I can now spend more time teaching and less time on paperwork. The analytics help me identify students who need additional support.",
      author: "Mary Tembo",
      role: "Teacher, Ndola Academy",
      avatar: "/placeholder.svg?height=100&width=100",
      rating: 4,
    },
    {
      quote:
        "The fee management module has streamlined our financial processes. We can now generate invoices, track payments, and send reminders automatically. This has improved our cash flow significantly.",
      author: "David Phiri",
      role: "Finance Manager, Copperbelt International School",
      avatar: "/placeholder.svg?height=100&width=100",
      rating: 5,
    },
    {
      quote:
        "As a parent, I appreciate being able to track my child's progress, attendance, and fee payments all in one place. The system sends me timely notifications about important events and deadlines.",
      author: "Elizabeth Mutale",
      role: "Parent, Lusaka",
      avatar: "/placeholder.svg?height=100&width=100",
      rating: 5,
    },
  ]

  const nextTestimonial = () => {
    setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setActiveIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length)
  }

  return (
    <section id="testimonials" ref={ref} className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="text-3xl sm:text-4xl font-bold text-gray-900"
          >
            What Our Customers Say
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto"
          >
            Hear from schools across Zambia that have transformed their management with our system
          </motion.p>
        </div>

        <div className="max-w-6xl mx-auto relative">
          <div className="overflow-hidden">
            <motion.div
              initial={{ opacity: 0 }}
              animate={isInView ? { opacity: 1 } : { opacity: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="flex items-center"
            >
              <Card className="bg-white shadow-xl border-0 overflow-hidden">
                <CardContent className="p-0">
                  <div className="flex flex-col md:flex-row">
                    <div className="md:w-1/3 bg-emerald-600 text-white p-8 flex flex-col justify-between">
                      <Quote className="h-12 w-12 text-emerald-300 mb-4" />
                      <div>
                        <div className="flex mb-2">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-5 w-5 ${
                                i < testimonials[activeIndex].rating
                                  ? "text-yellow-400 fill-yellow-400"
                                  : "text-gray-300"
                              }`}
                            />
                          ))}
                        </div>
                        <div className="flex items-center mt-6">
                          <div className="relative h-16 w-16 rounded-full overflow-hidden mr-4 border-2 border-white">
                            <Image
                              src={testimonials[activeIndex].avatar || "/placeholder.svg"}
                              alt={testimonials[activeIndex].author}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div>
                            <h4 className="font-semibold text-white text-lg">{testimonials[activeIndex].author}</h4>
                            <p className="text-emerald-200">{testimonials[activeIndex].role}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="md:w-2/3 p-8 md:p-12 flex items-center">
                      <blockquote>
                        <p className="text-xl md:text-2xl text-gray-700 italic leading-relaxed">
                          "{testimonials[activeIndex].quote}"
                        </p>
                      </blockquote>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          <div className="flex justify-center mt-8 gap-4">
            <Button
              variant="outline"
              size="icon"
              onClick={prevTestimonial}
              className="rounded-full"
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <div className="flex gap-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveIndex(index)}
                  className={`h-2.5 w-2.5 rounded-full transition-colors ${
                    index === activeIndex ? "bg-emerald-600" : "bg-gray-300"
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={nextTestimonial}
              className="rounded-full"
              aria-label="Next testimonial"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
