import { NextRequest, NextResponse } from "next/server"
import { authenticateUser, createAuthHeaders } from "@/lib/auth"

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request)
    
    if (!auth.user) {
      return NextResponse.json({ error: auth.error }, { status: auth.status })
    }

    // Forward the request to our backend API
    const response = await fetch(`${BACKEND_URL}/api/v1/dashboard/stats`, {
      headers: await createAuthHeaders(request),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      return NextResponse.json({ 
        error: errorData.message || "Failed to fetch dashboard stats" 
      }, { status: response.status })
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Dashboard stats API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
