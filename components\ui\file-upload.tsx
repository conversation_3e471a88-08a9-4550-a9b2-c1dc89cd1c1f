"use client"

import type React from "react"
import { useState, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Card, CardContent } from "@/components/ui/card"
import { Upload, X, File, ImageIcon, FileText, AlertCircle, CheckCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface FileUploadProps {
  onFileUpload?: (file: any) => void
  onFileRemove?: (fileId: string) => void
  acceptedTypes?: string[]
  maxFileSize?: number // in MB
  maxFiles?: number
  category?: string
  className?: string
  disabled?: boolean
  showPreview?: boolean
}

interface UploadedFile {
  id: string
  name: string
  size: number
  type: string
  url?: string
  thumbnailUrl?: string
  uploadProgress?: number
  error?: string
  status: "uploading" | "completed" | "error"
}

export function FileUpload({
  onFileUpload,
  onFileRemove,
  acceptedTypes = ["image/*", "application/pdf", ".doc", ".docx"],
  maxFileSize = 5, // 5MB default
  maxFiles = 1,
  category = "document",
  className,
  disabled = false,
  showPreview = true,
}: FileUploadProps) {
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [isDragOver, setIsDragOver] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string>("")
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size must be less than ${maxFileSize}MB`
    }

    // Check file type
    const fileType = file.type
    const fileName = file.name.toLowerCase()

    const isValidType = acceptedTypes.some((type) => {
      if (type.startsWith(".")) {
        return fileName.endsWith(type)
      }
      if (type.includes("*")) {
        const baseType = type.split("/")[0]
        return fileType.startsWith(baseType)
      }
      return fileType === type
    })

    if (!isValidType) {
      return `File type not supported. Accepted types: ${acceptedTypes.join(", ")}`
    }

    return null
  }

  const uploadFile = async (file: File): Promise<any> => {
    const formData = new FormData()
    formData.append("file", file)
    formData.append("category", category)
    formData.append("is_public", "false")

    const response = await fetch("/api/files/upload", {
      method: "POST",
      body: formData,
      headers: {
        Authorization: `Bearer ${localStorage.getItem("token")}`, // Adjust based on your auth implementation
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || "Upload failed")
    }

    return response.json()
  }

  const handleFileSelect = useCallback(
    async (selectedFiles: FileList) => {
      setError("")

      // Check max files limit
      if (files.length + selectedFiles.length > maxFiles) {
        setError(`Maximum ${maxFiles} file(s) allowed`)
        return
      }

      const newFiles: UploadedFile[] = []

      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i]
        const validationError = validateFile(file)

        if (validationError) {
          setError(validationError)
          continue
        }

        const fileId = `${Date.now()}-${i}`
        const uploadedFile: UploadedFile = {
          id: fileId,
          name: file.name,
          size: file.size,
          type: file.type,
          uploadProgress: 0,
          status: "uploading",
        }

        newFiles.push(uploadedFile)
      }

      if (newFiles.length === 0) return

      setFiles((prev) => [...prev, ...newFiles])
      setIsUploading(true)

      // Upload files
      for (let i = 0; i < newFiles.length; i++) {
        const file = selectedFiles[i]
        const uploadedFile = newFiles[i]

        try {
          // Simulate progress (you can implement real progress tracking)
          const progressInterval = setInterval(() => {
            setFiles((prev) =>
              prev.map((f) =>
                f.id === uploadedFile.id ? { ...f, uploadProgress: Math.min((f.uploadProgress || 0) + 10, 90) } : f,
              ),
            )
          }, 100)

          const result = await uploadFile(file)

          clearInterval(progressInterval)

          setFiles((prev) =>
            prev.map((f) =>
              f.id === uploadedFile.id
                ? {
                    ...f,
                    uploadProgress: 100,
                    status: "completed",
                    url: result.file.url,
                    thumbnailUrl: result.file.thumbnail_urls?.small,
                  }
                : f,
            ),
          )

          onFileUpload?.(result.file)
        } catch (error) {
          setFiles((prev) =>
            prev.map((f) =>
              f.id === uploadedFile.id
                ? {
                    ...f,
                    status: "error",
                    error: error instanceof Error ? error.message : "Upload failed",
                  }
                : f,
            ),
          )
        }
      }

      setIsUploading(false)
    },
    [files.length, maxFiles, category, onFileUpload],
  )

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      setIsDragOver(false)

      if (disabled) return

      const droppedFiles = e.dataTransfer.files
      if (droppedFiles.length > 0) {
        handleFileSelect(droppedFiles)
      }
    },
    [disabled, handleFileSelect],
  )

  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      if (!disabled) {
        setIsDragOver(true)
      }
    },
    [disabled],
  )

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const selectedFiles = e.target.files
      if (selectedFiles && selectedFiles.length > 0) {
        handleFileSelect(selectedFiles)
      }
      // Reset input value to allow selecting the same file again
      e.target.value = ""
    },
    [handleFileSelect],
  )

  const removeFile = useCallback(
    (fileId: string) => {
      const fileToRemove = files.find((f) => f.id === fileId)
      setFiles((prev) => prev.filter((f) => f.id !== fileId))

      if (fileToRemove && onFileRemove) {
        onFileRemove(fileId)
      }
    },
    [files, onFileRemove],
  )

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith("image/")) {
      return <ImageIcon className="h-8 w-8 text-blue-500" />
    }
    if (fileType.includes("pdf") || fileType.includes("document")) {
      return <FileText className="h-8 w-8 text-red-500" />
    }
    return <File className="h-8 w-8 text-gray-500" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
          isDragOver ? "border-primary bg-primary/5" : "border-gray-300",
          disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-primary hover:bg-primary/5",
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <Upload className={cn("mx-auto h-12 w-12 mb-4", isDragOver ? "text-primary" : "text-gray-400")} />
        <p className="text-lg font-medium mb-2">
          {isDragOver ? "Drop files here" : "Click to upload or drag and drop"}
        </p>
        <p className="text-sm text-gray-500">
          {acceptedTypes.join(", ")} up to {maxFileSize}MB
        </p>
        {maxFiles > 1 && <p className="text-xs text-gray-400 mt-1">Maximum {maxFiles} files</p>}
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple={maxFiles > 1}
        accept={acceptedTypes.join(",")}
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* File List */}
      {files.length > 0 && showPreview && (
        <div className="space-y-2">
          {files.map((file) => (
            <Card key={file.id} className="p-3">
              <CardContent className="p-0">
                <div className="flex items-center space-x-3">
                  {/* File Icon/Thumbnail */}
                  <div className="flex-shrink-0">
                    {file.thumbnailUrl ? (
                      <img
                        src={file.thumbnailUrl || "/placeholder.svg"}
                        alt={file.name}
                        className="h-12 w-12 object-cover rounded"
                      />
                    ) : (
                      getFileIcon(file.type)
                    )}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{file.name}</p>
                    <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>

                    {/* Progress Bar */}
                    {file.status === "uploading" && <Progress value={file.uploadProgress || 0} className="mt-1" />}

                    {/* Error Message */}
                    {file.status === "error" && <p className="text-xs text-red-500 mt-1">{file.error}</p>}
                  </div>

                  {/* Status Icon */}
                  <div className="flex-shrink-0">
                    {file.status === "completed" && <CheckCircle className="h-5 w-5 text-green-500" />}
                    {file.status === "error" && <AlertCircle className="h-5 w-5 text-red-500" />}
                  </div>

                  {/* Remove Button */}
                  <Button variant="ghost" size="sm" onClick={() => removeFile(file.id)} className="flex-shrink-0">
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
