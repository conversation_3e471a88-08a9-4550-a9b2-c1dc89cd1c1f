"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>hart, LineChart } from "@/components/dashboard/charts"
import { Progress } from "@/components/ui/progress"
import {
  Download,
  TrendingUp,
  TrendingDown,
  Users,
  Building,
  CreditCard,
  BarChartIcon,
  PieChart,
  ArrowRight,
} from "lucide-react"
import { superAdminService } from "@/lib/backend-api"

export default function AnalyticsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState("30days")
  const [stats, setStats] = useState({
    totalInstitutions: 0,
    activeInstitutions: 0,
    totalUsers: 0,
    totalStudents: 0,
    totalRevenue: 0,
    growthRate: 0,
  })
  const [institutionAnalytics, setInstitutionAnalytics] = useState<any>(null)
  const [userAnalytics, setUserAnalytics] = useState<any>(null)
  const [revenueAnalytics, setRevenueAnalytics] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchAnalyticsData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Fetch main stats using backend API service
        const statsResult = await superAdminService.getAnalytics('stats', { timeRange })

        if (statsResult.success) {
          const data = statsResult.data
          setStats({
            totalInstitutions: data.institutions.total,
            activeInstitutions: data.institutions.active,
            totalUsers: data.users.total,
            totalStudents: data.students.total,
            totalRevenue: 0, // Will be fetched from revenue analytics
            growthRate: data.institutions.growthRate,
          })
        }

        // Fetch institution analytics
        const institutionResult = await superAdminService.getAnalytics('institutions', { timeRange })
        if (institutionResult.success) {
          setInstitutionAnalytics(institutionResult.data)
        }

        // Fetch user analytics
        const userResult = await superAdminService.getAnalytics('users', { timeRange })
        if (userResult.success) {
          setUserAnalytics(userResult.data)
        }

        // Fetch revenue analytics
        const revenueResponse = await fetch(`/api/super-admin/analytics?type=revenue&timeRange=${timeRange}`)
        if (revenueResponse.ok) {
          const revenueResult = await revenueResponse.json()
          if (revenueResult.success) {
            setRevenueAnalytics(revenueResult.data)
            setStats(prev => ({ ...prev, totalRevenue: revenueResult.data.totalRevenue }))
          }
        }

      } catch (err: any) {
        console.error('Analytics API error:', err)
        setError(err?.message || 'An error occurred while fetching analytics data')
      } finally {
        setIsLoading(false)
      }
    }

    fetchAnalyticsData()
  }, [timeRange])

  const handleExport = async () => {
    try {
      const response = await fetch('/api/super-admin/analytics?action=export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'all',
          format: 'csv',
        }),
      })

      const result = await response.json()
      if (result.success) {
        // Handle download URL
        console.log('Export successful:', result.downloadUrl)
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
          <p className="text-gray-500">Comprehensive system-wide analytics and insights</p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="year">Last 12 months</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Institutions</CardTitle>
            <Building className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "Loading..." : stats.totalInstitutions}</div>
            <div className="flex items-center pt-1">
              <TrendingUp className="mr-1 h-4 w-4 text-emerald-500" />
              <span className="text-xs text-emerald-500 font-medium">+5 institutions</span>
              <span className="text-xs text-gray-500 ml-1">since last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "Loading..." : stats.totalUsers.toLocaleString()}</div>
            <div className="flex items-center pt-1">
              <TrendingUp className="mr-1 h-4 w-4 text-emerald-500" />
              <span className="text-xs text-emerald-500 font-medium">+12.5%</span>
              <span className="text-xs text-gray-500 ml-1">since last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <CreditCard className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? "Loading..." : `K${stats.totalRevenue.toLocaleString()}`}
            </div>
            <div className="flex items-center pt-1">
              <TrendingUp className="mr-1 h-4 w-4 text-emerald-500" />
              <span className="text-xs text-emerald-500 font-medium">+{stats.growthRate}%</span>
              <span className="text-xs text-gray-500 ml-1">since last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "Loading..." : stats.totalStudents.toLocaleString()}</div>
            <div className="flex items-center pt-1">
              <TrendingUp className="mr-1 h-4 w-4 text-emerald-500" />
              <span className="text-xs text-emerald-500 font-medium">+6.8%</span>
              <span className="text-xs text-gray-500 ml-1">since last month</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="institutions">Institutions</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="lg:col-span-4">
              <CardHeader>
                <CardTitle>Growth Trends</CardTitle>
                <CardDescription>System-wide growth metrics over time</CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <LineChart />
              </CardContent>
            </Card>

            <Card className="lg:col-span-3">
              <CardHeader>
                <CardTitle>Subscription Distribution</CardTitle>
                <CardDescription>Breakdown by subscription plan</CardDescription>
              </CardHeader>
              <CardContent>
                <BarChart />
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Top Institutions</CardTitle>
                <CardDescription>By number of students</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { name: "Kitwe Secondary School", students: 1200, growth: 5.2 },
                    { name: "Lusaka Primary School", students: 850, growth: 3.8 },
                    { name: "Ndola Academy", students: 650, growth: 7.5 },
                    { name: "Kabwe University", students: 3200, growth: 2.1 },
                    { name: "Livingstone College", students: 1500, growth: -1.2 },
                  ].map((institution, i) => (
                    <div key={i} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{institution.name}</p>
                        <p className="text-sm text-gray-500">{institution.students.toLocaleString()} students</p>
                      </div>
                      <div className="flex items-center">
                        {institution.growth > 0 ? (
                          <TrendingUp className="h-4 w-4 text-emerald-500 mr-1" />
                        ) : (
                          <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                        )}
                        <span
                          className={
                            institution.growth > 0 ? "text-emerald-500 font-medium" : "text-red-500 font-medium"
                          }
                        >
                          {institution.growth > 0 ? "+" : ""}
                          {institution.growth}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Growth</CardTitle>
                <CardDescription>New users over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { period: "This Week", count: 245, percentage: 12 },
                    { period: "Last Week", count: 218, percentage: 8 },
                    { period: "This Month", count: 1250, percentage: 15 },
                    { period: "Last Month", count: 1085, percentage: 10 },
                    { period: "This Year", count: 8500, percentage: 35 },
                  ].map((period, i) => (
                    <div key={i} className="space-y-1">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{period.period}</span>
                        <span className="text-sm font-medium">{period.count} users</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Progress value={period.percentage} className="h-2" />
                        <span className="text-xs text-emerald-500 font-medium">+{period.percentage}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue Breakdown</CardTitle>
                <CardDescription>By subscription plan</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { plan: "Premium", amount: 45000, percentage: 53 },
                    { plan: "Standard", amount: 28800, percentage: 34 },
                    { plan: "Basic", amount: 6000, percentage: 7 },
                    { plan: "Enterprise", amount: 5200, percentage: 6 },
                  ].map((plan, i) => (
                    <div key={i} className="space-y-1">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{plan.plan}</span>
                        <span className="text-sm font-medium">K{plan.amount.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Progress value={plan.percentage} className="h-2" />
                        <span className="text-xs text-gray-500">{plan.percentage}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="institutions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Institution Analytics</CardTitle>
              <CardDescription>Detailed metrics about institutions</CardDescription>
            </CardHeader>
            <CardContent className="h-[500px] flex items-center justify-center">
              <div className="text-center text-gray-500 flex flex-col items-center">
                <BarChartIcon className="h-16 w-16 mb-2 text-gray-400" />
                <p>Institution analytics will be displayed here</p>
                <Button variant="outline" className="mt-4">
                  View Detailed Report <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Analytics</CardTitle>
              <CardDescription>Detailed metrics about system users</CardDescription>
            </CardHeader>
            <CardContent className="h-[500px] flex items-center justify-center">
              <div className="text-center text-gray-500 flex flex-col items-center">
                <PieChart className="h-16 w-16 mb-2 text-gray-400" />
                <p>User analytics will be displayed here</p>
                <Button variant="outline" className="mt-4">
                  View Detailed Report <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Analytics</CardTitle>
              <CardDescription>Detailed financial metrics</CardDescription>
            </CardHeader>
            <CardContent className="h-[500px] flex items-center justify-center">
              <div className="text-center text-gray-500 flex flex-col items-center">
                <BarChartIcon className="h-16 w-16 mb-2 text-gray-400" />
                <p>Revenue analytics will be displayed here</p>
                <Button variant="outline" className="mt-4">
                  View Detailed Report <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
