"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import {
  UserPlus,
  Search,
  Download,
  Filter,
  ChevronLeft,
  ArrowUpDown,
  MoreHorizontal,
  UserMinus,
  Mail,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { InstitutionProtectedPage } from "@/components/auth/protected-page"

// Mock data for the class
const mockClass = {
  id: "1",
  name: "Class 10-A",
  section: "A",
  grade: "10",
  academicYear: "2023-2024",
  roomNumber: "301",
  classTeacher: "Ms. Sarah Johnson",
  totalStudents: 32,
  capacity: 35,
}

// Mock data for students
const mockStudents = [
  {
    id: "1",
    name: "Emma Thompson",
    rollNumber: "10A01",
    gender: "Female",
    attendance: "95%",
    performance: "Excellent",
    contact: "<EMAIL>",
    parentName: "David & Sarah Thompson",
    parentContact: "+****************",
    status: "active",
    joinDate: "2022-08-15",
  },
  {
    id: "2",
    name: "James Wilson",
    rollNumber: "10A02",
    gender: "Male",
    attendance: "92%",
    performance: "Good",
    contact: "<EMAIL>",
    parentName: "Robert & Mary Wilson",
    parentContact: "+****************",
    status: "active",
    joinDate: "2022-08-15",
  },
  {
    id: "3",
    name: "Sophia Martinez",
    rollNumber: "10A03",
    gender: "Female",
    attendance: "98%",
    performance: "Excellent",
    contact: "<EMAIL>",
    parentName: "Carlos & Elena Martinez",
    parentContact: "+****************",
    status: "active",
    joinDate: "2022-08-15",
  },
  {
    id: "4",
    name: "Ethan Johnson",
    rollNumber: "10A04",
    gender: "Male",
    attendance: "88%",
    performance: "Average",
    contact: "<EMAIL>",
    parentName: "Michael & Jennifer Johnson",
    parentContact: "+****************",
    status: "active",
    joinDate: "2022-08-15",
  },
  {
    id: "5",
    name: "Olivia Brown",
    rollNumber: "10A05",
    gender: "Female",
    attendance: "94%",
    performance: "Good",
    contact: "<EMAIL>",
    parentName: "William & Emily Brown",
    parentContact: "+****************",
    status: "active",
    joinDate: "2022-08-15",
  },
  {
    id: "6",
    name: "Noah Davis",
    rollNumber: "10A06",
    gender: "Male",
    attendance: "90%",
    performance: "Good",
    contact: "<EMAIL>",
    parentName: "Daniel & Sophia Davis",
    parentContact: "+****************",
    status: "active",
    joinDate: "2022-08-15",
  },
  {
    id: "7",
    name: "Ava Miller",
    rollNumber: "10A07",
    gender: "Female",
    attendance: "96%",
    performance: "Excellent",
    contact: "<EMAIL>",
    parentName: "Joseph & Olivia Miller",
    parentContact: "+****************",
    status: "active",
    joinDate: "2022-08-15",
  },
  {
    id: "8",
    name: "Liam Garcia",
    rollNumber: "10A08",
    gender: "Male",
    attendance: "85%",
    performance: "Average",
    contact: "<EMAIL>",
    parentName: "Anthony & Isabella Garcia",
    parentContact: "+****************",
    status: "active",
    joinDate: "2022-08-15",
  },
  {
    id: "9",
    name: "Isabella Rodriguez",
    rollNumber: "10A09",
    gender: "Female",
    attendance: "93%",
    performance: "Good",
    contact: "<EMAIL>",
    parentName: "Manuel & Sofia Rodriguez",
    parentContact: "+****************",
    status: "active",
    joinDate: "2022-08-15",
  },
  {
    id: "10",
    name: "Mason Lee",
    rollNumber: "10A10",
    gender: "Male",
    attendance: "91%",
    performance: "Good",
    contact: "<EMAIL>",
    parentName: "David & Grace Lee",
    parentContact: "+1 (555) 012-3456",
    status: "active",
    joinDate: "2022-08-15",
  },
]

// Mock data for available students to add
const availableStudents = [
  { id: "101", name: "Charlotte White", rollNumber: "10B01", gender: "Female", currentClass: "10-B" },
  { id: "102", name: "Henry Clark", rollNumber: "10B02", gender: "Male", currentClass: "10-B" },
  { id: "103", name: "Amelia Lewis", rollNumber: "10B03", gender: "Female", currentClass: "10-B" },
  { id: "104", name: "Benjamin Walker", rollNumber: "10B04", gender: "Male", currentClass: "10-B" },
  { id: "105", name: "Mia Hall", rollNumber: "10B05", gender: "Female", currentClass: "10-B" },
]

function ClassStudentsContent() {
  const params = useParams()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStudents, setSelectedStudents] = useState<string[]>([])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false)

  // Filter students based on search term
  const filteredStudents = mockStudents.filter(
    (student) =>
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.rollNumber.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  // Handle student selection
  const toggleStudentSelection = (studentId: string) => {
    setSelectedStudents((prev) =>
      prev.includes(studentId) ? prev.filter((id) => id !== studentId) : [...prev, studentId],
    )
  }

  // Handle select all students
  const toggleSelectAll = () => {
    if (selectedStudents.length === filteredStudents.length) {
      setSelectedStudents([])
    } else {
      setSelectedStudents(filteredStudents.map((student) => student.id))
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Manage Students</h1>
          <p className="text-muted-foreground">
            {mockClass.name} | Total Students: {mockClass.totalStudents}/{mockClass.capacity}
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Class
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <UserPlus className="mr-2 h-4 w-4" />
                Add Students
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Add Students to {mockClass.name}</DialogTitle>
                <DialogDescription>
                  Select students to add to this class. The class currently has {mockClass.totalStudents} students out
                  of a capacity of {mockClass.capacity}.
                </DialogDescription>
              </DialogHeader>
              <div className="py-4">
                <div className="flex items-center mb-4">
                  <Input
                    placeholder="Search students..."
                    className="max-w-sm"
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[180px] ml-2">
                      <SelectValue placeholder="Filter by class" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Classes</SelectItem>
                      <SelectItem value="10b">Class 10-B</SelectItem>
                      <SelectItem value="10c">Class 10-C</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="border rounded-md">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="py-3 px-4 text-left font-medium">
                          <Checkbox />
                        </th>
                        <th className="py-3 px-4 text-left font-medium">Name</th>
                        <th className="py-3 px-4 text-left font-medium">Roll Number</th>
                        <th className="py-3 px-4 text-left font-medium">Current Class</th>
                      </tr>
                    </thead>
                    <tbody>
                      {availableStudents.map((student) => (
                        <tr key={student.id} className="border-b last:border-0">
                          <td className="py-3 px-4">
                            <Checkbox />
                          </td>
                          <td className="py-3 px-4">
                            <div className="font-medium">{student.name}</div>
                          </td>
                          <td className="py-3 px-4">{student.rollNumber}</td>
                          <td className="py-3 px-4">{student.currentClass}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsAddDialogOpen(false)}>Add Selected Students</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="all">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All Students</TabsTrigger>
          <TabsTrigger value="male">Male</TabsTrigger>
          <TabsTrigger value="female">Female</TabsTrigger>
          <TabsTrigger value="performance">By Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                <CardTitle>Students in {mockClass.name}</CardTitle>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search students..."
                      className="pl-8 w-[200px] sm:w-[300px]"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="py-3 px-4 text-left font-medium">
                          <Checkbox
                            checked={selectedStudents.length === filteredStudents.length && filteredStudents.length > 0}
                            onCheckedChange={toggleSelectAll}
                          />
                        </th>
                        <th className="py-3 px-4 text-left font-medium">
                          <div className="flex items-center">
                            Name
                            <ArrowUpDown className="ml-2 h-4 w-4" />
                          </div>
                        </th>
                        <th className="py-3 px-4 text-left font-medium">Roll Number</th>
                        <th className="py-3 px-4 text-left font-medium">Gender</th>
                        <th className="py-3 px-4 text-left font-medium">Attendance</th>
                        <th className="py-3 px-4 text-left font-medium">Performance</th>
                        <th className="py-3 px-4 text-right font-medium">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredStudents.map((student) => (
                        <tr key={student.id} className="border-b last:border-0">
                          <td className="py-3 px-4">
                            <Checkbox
                              checked={selectedStudents.includes(student.id)}
                              onCheckedChange={() => toggleStudentSelection(student.id)}
                            />
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              <Avatar className="h-8 w-8 mr-2">
                                <AvatarImage src={`/placeholder.svg?height=32&width=32`} alt={student.name} />
                                <AvatarFallback>
                                  {student.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <div className="font-medium">{student.name}</div>
                            </div>
                          </td>
                          <td className="py-3 px-4">{student.rollNumber}</td>
                          <td className="py-3 px-4">{student.gender}</td>
                          <td className="py-3 px-4">{student.attendance}</td>
                          <td className="py-3 px-4">
                            <Badge
                              variant={
                                student.performance === "Excellent"
                                  ? "default"
                                  : student.performance === "Good"
                                    ? "outline"
                                    : "secondary"
                              }
                            >
                              {student.performance}
                            </Badge>
                          </td>
                          <td className="py-3 px-4 text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => router.push(`/dashboard/students/${student.id}`)}>
                                  View Profile
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Mail className="mr-2 h-4 w-4" />
                                  Contact Parent
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-destructive">
                                  <UserMinus className="mr-2 h-4 w-4" />
                                  Remove from Class
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {selectedStudents.length > 0 && (
                <div className="flex items-center justify-between mt-4 p-2 bg-muted rounded-md">
                  <div className="text-sm">
                    {selectedStudents.length} student{selectedStudents.length > 1 ? "s" : ""} selected
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <Mail className="mr-2 h-4 w-4" />
                      Contact Parents
                    </Button>
                    <Dialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
                      <DialogTrigger asChild>
                        <Button variant="destructive" size="sm">
                          <UserMinus className="mr-2 h-4 w-4" />
                          Remove from Class
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Remove Students</DialogTitle>
                          <DialogDescription>
                            Are you sure you want to remove {selectedStudents.length} student
                            {selectedStudents.length > 1 ? "s" : ""} from {mockClass.name}?
                          </DialogDescription>
                        </DialogHeader>
                        <DialogFooter className="mt-4">
                          <Button variant="outline" onClick={() => setIsRemoveDialogOpen(false)}>
                            Cancel
                          </Button>
                          <Button
                            variant="destructive"
                            onClick={() => {
                              setIsRemoveDialogOpen(false)
                              setSelectedStudents([])
                            }}
                          >
                            Remove
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="male" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Male Students</CardTitle>
              <CardDescription>Showing all male students in {mockClass.name}</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Similar table structure as "all" tab but filtered for male students */}
              <div className="text-center py-4 text-muted-foreground">
                This tab would display only male students with the same structure as the All Students tab.
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="female" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Female Students</CardTitle>
              <CardDescription>Showing all female students in {mockClass.name}</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Similar table structure as "all" tab but filtered for female students */}
              <div className="text-center py-4 text-muted-foreground">
                This tab would display only female students with the same structure as the All Students tab.
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Students by Performance</CardTitle>
              <CardDescription>Students grouped by academic performance</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Performance-based grouping */}
              <div className="text-center py-4 text-muted-foreground">
                This tab would group students by their performance levels (Excellent, Good, Average, etc.)
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default function ClassStudentsPage() {
  return (
    <InstitutionProtectedPage>
      <ClassStudentsContent />
    </InstitutionProtectedPage>
  )
}
