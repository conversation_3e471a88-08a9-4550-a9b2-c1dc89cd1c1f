#!/usr/bin/env node

/**
 * Test script to verify institution registration functionality
 */
const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:4000';

async function testInstitutionRegistration() {
  try {
    console.log(`Testing institution registration at ${BACKEND_URL}...`);
    
    // Create a FormData instance
    const formData = new FormData();
    
    // Add institution details
    formData.append('institutionName', 'Test School');
    formData.append('institutionType', 'primary');
    formData.append('address', '123 Test Street');
    formData.append('city', 'Lusaka');
    formData.append('country', 'Zambia');
    formData.append('phone', '+260123456789');
    formData.append('institutionEmail', '<EMAIL>');
    formData.append('website', 'https://testschool.edu');
    
    // Add admin details
    formData.append('adminFirstName', 'Test');
    formData.append('adminLastName', 'Admin');
    formData.append('adminEmail', '<EMAIL>');
    formData.append('adminPassword', 'Test@123456');
    formData.append('confirmPassword', 'Test@123456');
    
    // Add additional info
    formData.append('studentCount', '200');
    formData.append('teacherCount', '20');
    formData.append('subscriptionPlan', 'standard');
    formData.append('referralSource', 'Google');
    formData.append('specialRequirements', 'None');
    
    // Add a test logo if available
    const logoPath = path.join(__dirname, '../public/placeholder-logo.png');
    if (fs.existsSync(logoPath)) {
      formData.append('logo', fs.createReadStream(logoPath));
    }
    
    // Send the registration request
    const response = await fetch(`${BACKEND_URL}/api/v1/institutions/register`, {
      method: 'POST',
      body: formData,
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('Institution registration successful!');
      console.log('Response:', JSON.stringify(data, null, 2));
      return true;
    } else {
      console.error('Institution registration failed:');
      console.error(data);
      return false;
    }
  } catch (error) {
    console.error('Error testing institution registration:', error.message);
    return false;
  }
}

async function main() {
  const registrationSuccess = await testInstitutionRegistration();
  
  if (!registrationSuccess) {
    console.log('\nTroubleshooting tips:');
    console.log('1. Make sure the backend server is running');
    console.log('2. Check the BACKEND_URL environment variable');
    console.log('3. Verify the database connection');
    console.log('4. Check the backend logs for any errors');
    console.log('5. Ensure the uploads directory exists and is writable');
    process.exit(1);
  }
  
  process.exit(0);
}

// Run the test
main();
