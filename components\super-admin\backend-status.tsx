"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, RefreshCw } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { But<PERSON> } from "@/components/ui/button"

interface BackendStatusProps {
  onStatusChange?: (isOnline: boolean) => void
}

export function BackendStatus({ onStatusChange }: BackendStatusProps) {
  const [isOnline, setIsOnline] = useState<boolean | null>(null)
  const [isChecking, setIsChecking] = useState(false)
  const [lastChecked, setLastChecked] = useState<Date | null>(null)

  const checkBackendStatus = async () => {
    setIsChecking(true)
    try {
      // Try to reach a simple backend endpoint
      const response = await fetch('/api/auth/session', {
        credentials: 'include',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      })
      
      const online = response.ok
      setIsOnline(online)
      setLastChecked(new Date())
      onStatusChange?.(online)
    } catch (error) {
      setIsOnline(false)
      setLastChecked(new Date())
      onStatusChange?.(false)
    } finally {
      setIsChecking(false)
    }
  }

  useEffect(() => {
    checkBackendStatus()
    
    // Check every 30 seconds
    const interval = setInterval(checkBackendStatus, 30000)
    
    return () => clearInterval(interval)
  }, [])

  if (isOnline === null) {
    return (
      <Alert>
        <RefreshCw className="h-4 w-4 animate-spin" />
        <AlertDescription>
          Checking backend connection...
        </AlertDescription>
      </Alert>
    )
  }

  if (isOnline) {
    return (
      <Alert className="border-green-200 bg-green-50 text-green-800">
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          Backend is online and responding normally.
          {lastChecked && (
            <span className="text-xs block mt-1">
              Last checked: {lastChecked.toLocaleTimeString()}
            </span>
          )}
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <Alert variant="destructive">
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <div>
          <div className="font-medium">Backend server is not responding</div>
          <div className="text-sm mt-1">
            The backend API is not available. Some features may not work properly.
            {lastChecked && (
              <span className="block mt-1">
                Last checked: {lastChecked.toLocaleTimeString()}
              </span>
            )}
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={checkBackendStatus}
          disabled={isChecking}
          className="ml-4"
        >
          {isChecking ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            "Retry"
          )}
        </Button>
      </AlertDescription>
    </Alert>
  )
}

// Hook to use backend status in components
export function useBackendStatus() {
  const [isOnline, setIsOnline] = useState<boolean | null>(null)

  useEffect(() => {
    const checkStatus = async () => {
      try {
        const response = await fetch('/api/auth/session', {
          credentials: 'include',
          signal: AbortSignal.timeout(5000)
        })
        setIsOnline(response.ok)
      } catch (error) {
        setIsOnline(false)
      }
    }

    checkStatus()
    const interval = setInterval(checkStatus, 30000)
    
    return () => clearInterval(interval)
  }, [])

  return isOnline
}
