const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const catchAsync = require('../../../utils/catchAsync');
const AppError = require('../../../utils/appError');
const { logAudit } = require('../../../services/auditService');

/**
 * Get all schools with pagination and filtering
 */
exports.getSchools = catchAsync(async (req, res, next) => {
  // Pagination parameters
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;
  
  // Filtering options
  const filter = {};
  if (req.query.isActive) {
    filter.isActive = req.query.isActive === 'true';
  }
  if (req.query.institutionId) {
    filter.institutionId = req.query.institutionId;
  }
  if (req.query.search) {
    filter.OR = [
      { name: { contains: req.query.search, mode: 'insensitive' } },
      { city: { contains: req.query.search, mode: 'insensitive' } },
    ];
  }

  // Get schools with pagination
  const schools = await prisma.school.findMany({
    where: filter,
    skip,
    take: limit,
    include: {
      institution: {
        select: {
          id: true,
          name: true,
        },
      },
      _count: {
        select: {
          users: true,
          classes: true,
          studentEnrollments: true,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  // Get total count for pagination
  const totalSchools = await prisma.school.count({
    where: filter,
  });

  const totalPages = Math.ceil(totalSchools / limit);

  res.status(200).json({
    status: 'success',
    results: schools.length,
    data: {
      schools,
      pagination: {
        currentPage: page,
        totalPages,
        totalSchools,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    },
  });
});

/**
 * Get school by ID
 */
exports.getSchoolById = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const school = await prisma.school.findUnique({
    where: { id },
    include: {
      institution: {
        select: {
          id: true,
          name: true,
        },
      },
      _count: {
        select: {
          students: true,
          teachers: true,
          classes: true,
        },
      },
    },
  });

  if (!school) {
    return next(new AppError('School not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      school,
    },
  });
});

/**
 * Create new school
 */
exports.createSchool = catchAsync(async (req, res, next) => {
  const {
    name,
    type,
    institutionId,
    address,
    city,
    state,
    country,
    postalCode,
    phoneNumber,
    email,
    website,
  } = req.body;

  // Verify institution exists
  const institution = await prisma.institution.findUnique({
    where: { id: institutionId },
  });

  if (!institution) {
    return next(new AppError('Institution not found', 404));
  }

  // Check if school with same name already exists in the institution
  const existingSchool = await prisma.school.findFirst({
    where: {
      name,
      institutionId,
    },
  });

  if (existingSchool) {
    return next(new AppError('A school with this name already exists in the institution', 400));
  }

  // Create the school
  const school = await prisma.school.create({
    data: {
      name,
      type: type || 'TERTIARY', // Default to TERTIARY if not provided
      institutionId,
      address,
      city,
      state,
      country,
      postalCode,
      phoneNumber,
      email,
      website,
    },
    include: {
      institution: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  // Log audit
  await logAudit({
    userId: req.user.id,
    action: 'CREATE_SCHOOL',
    resourceType: 'SCHOOL',
    resourceId: school.id,
    details: { schoolName: name, institutionId },
  });

  res.status(201).json({
    status: 'success',
    data: {
      school,
    },
  });
});

/**
 * Update school
 */
exports.updateSchool = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const updates = req.body;

  // Check if school exists
  const existingSchool = await prisma.school.findUnique({
    where: { id },
  });

  if (!existingSchool) {
    return next(new AppError('School not found', 404));
  }

  // If updating name, check for duplicates within the same institution
  if (updates.name && updates.name !== existingSchool.name) {
    const duplicateSchool = await prisma.school.findFirst({
      where: {
        name: updates.name,
        institutionId: existingSchool.institutionId,
        id: { not: id },
      },
    });

    if (duplicateSchool) {
      return next(new AppError('A school with this name already exists in the institution', 400));
    }
  }

  // Update the school
  const school = await prisma.school.update({
    where: { id },
    data: updates,
    include: {
      institution: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  // Log audit
  await logAudit({
    userId: req.user.id,
    action: 'UPDATE_SCHOOL',
    resourceType: 'SCHOOL',
    resourceId: school.id,
    details: { updates },
  });

  res.status(200).json({
    status: 'success',
    data: {
      school,
    },
  });
});

/**
 * Delete school
 */
exports.deleteSchool = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // Check if school exists
  const school = await prisma.school.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          students: true,
          teachers: true,
          classes: true,
        },
      },
    },
  });

  if (!school) {
    return next(new AppError('School not found', 404));
  }

  // Check if school has any associated data
  if (school._count.students > 0 || school._count.teachers > 0 || school._count.classes > 0) {
    return next(new AppError('Cannot delete school with existing students, teachers, or classes', 400));
  }

  // Delete the school
  await prisma.school.delete({
    where: { id },
  });

  // Log audit
  await logAudit({
    userId: req.user.id,
    action: 'DELETE_SCHOOL',
    resourceType: 'SCHOOL',
    resourceId: id,
    details: { schoolName: school.name },
  });

  res.status(204).json({
    status: 'success',
    data: null,
  });
});

/**
 * Get schools by institution
 */
exports.getSchoolsByInstitution = catchAsync(async (req, res, next) => {
  const { institutionId } = req.params;

  // Verify institution exists
  const institution = await prisma.institution.findUnique({
    where: { id: institutionId },
  });

  if (!institution) {
    return next(new AppError('Institution not found', 404));
  }

  // Get schools for the institution
  const schools = await prisma.school.findMany({
    where: {
      institutionId,
      isActive: true,
    },
    include: {
      _count: {
        select: {
          students: true,
          teachers: true,
          classes: true,
        },
      },
    },
    orderBy: {
      name: 'asc',
    },
  });

  res.status(200).json({
    status: 'success',
    results: schools.length,
    data: {
      schools,
    },
  });
});
