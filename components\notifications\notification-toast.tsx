"use client"

import { useEffect, useState } from "react"
import { X } from "lucide-react"
import { cva } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { useNotifications, type Notification } from "@/contexts/notification-context"

const toastVariants = cva(
  "pointer-events-auto relative w-full max-w-sm overflow-hidden rounded-lg p-4 shadow-lg transition-all",
  {
    variants: {
      type: {
        info: "bg-blue-50 text-blue-800 dark:bg-blue-950 dark:text-blue-200",
        success: "bg-green-50 text-green-800 dark:bg-green-950 dark:text-green-200",
        warning: "bg-yellow-50 text-yellow-800 dark:bg-yellow-950 dark:text-yellow-200",
        error: "bg-red-50 text-red-800 dark:bg-red-950 dark:text-red-200",
      },
    },
    defaultVariants: {
      type: "info",
    },
  },
)

const iconVariants = cva("h-5 w-5", {
  variants: {
    type: {
      info: "text-blue-500 dark:text-blue-400",
      success: "text-green-500 dark:text-green-400",
      warning: "text-yellow-500 dark:text-yellow-400",
      error: "text-red-500 dark:text-red-400",
    },
  },
  defaultVariants: {
    type: "info",
  },
})

interface NotificationToastProps {
  notification: Notification
  onClose: () => void
}

export function NotificationToast({ notification, onClose }: NotificationToastProps) {
  const [isVisible, setIsVisible] = useState(true)
  const { markAsRead } = useNotifications()

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false)
      setTimeout(onClose, 300) // Wait for the fade-out animation
    }, 5000)

    return () => clearTimeout(timer)
  }, [onClose])

  const handleClick = () => {
    markAsRead(notification.id)
    if (notification.link) {
      window.location.href = notification.link
    }
  }

  return (
    <div
      className={cn(
        toastVariants({ type: notification.type }),
        "flex items-start gap-3 transition-opacity duration-300",
        isVisible ? "opacity-100" : "opacity-0",
      )}
      role="alert"
    >
      <div className="flex-1" onClick={handleClick}>
        <p className="font-medium">{notification.title}</p>
        <p className="mt-1 text-sm opacity-90">{notification.message}</p>
        <p className="mt-1 text-xs opacity-70">
          {new Date(notification.createdAt).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
        </p>
      </div>
      <Button
        variant="ghost"
        size="icon"
        className="h-6 w-6 rounded-full opacity-70 hover:opacity-100"
        onClick={(e) => {
          e.stopPropagation()
          onClose()
        }}
      >
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </Button>
    </div>
  )
}
