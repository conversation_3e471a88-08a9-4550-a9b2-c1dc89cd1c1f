import type { <PERSON><PERSON><PERSON> } from "next"
import Link from "next/link"
import { Check } from "lucide-react"

import { LandingHeader } from "@/components/landing/landing-header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"

export const metadata: Metadata = {
  title: "Pricing | School Management System",
  description:
    "Explore our flexible pricing plans designed to meet the needs of educational institutions of all sizes.",
}

export default function PricingPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <LandingHeader />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 py-20 md:py-28">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  Simple, Transparent Pricing
                </h1>
                <p className="mx-auto max-w-[700px] text-slate-500 dark:text-slate-400 md:text-xl">
                  Choose the plan that's right for your institution. All plans include a 14-day free trial.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-20">
          <div className="container px-4 md:px-6">
            <Tabs defaultValue="monthly" className="w-full">
              <div className="flex justify-center mb-8">
                <TabsList>
                  <TabsTrigger value="monthly">Monthly</TabsTrigger>
                  <TabsTrigger value="annual">Annual (Save 20%)</TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="monthly">
                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                  {monthlyPlans.map((plan, index) => (
                    <PricingCard key={index} plan={plan} />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="annual">
                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                  {annualPlans.map((plan, index) => (
                    <PricingCard key={index} plan={plan} />
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="bg-slate-50 dark:bg-slate-900 py-20">
          <div className="container px-4 md:px-6">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Frequently Asked Questions</h2>
              <p className="mx-auto max-w-[700px] text-slate-500 dark:text-slate-400 md:text-xl mt-4">
                Find answers to common questions about our pricing and plans
              </p>
            </div>
            <div className="grid gap-6 md:grid-cols-2 lg:gap-10">
              {faqs.map((faq, index) => (
                <div key={index} className="space-y-2">
                  <h3 className="text-xl font-bold">{faq.question}</h3>
                  <p className="text-slate-500 dark:text-slate-400">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Enterprise Section */}
        <section className="py-20">
          <div className="container px-4 md:px-6">
            <div className="rounded-xl bg-primary/5 p-8 md:p-10">
              <div className="grid gap-10 lg:grid-cols-2 items-center">
                <div className="space-y-4">
                  <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Need a Custom Solution?</h2>
                  <p className="text-slate-500 dark:text-slate-400 md:text-xl">
                    We offer tailored solutions for large educational institutions, school districts, and educational
                    organizations with specific requirements.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-center">
                      <Check className="mr-2 h-5 w-5 text-primary" />
                      <span>Custom integrations with existing systems</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="mr-2 h-5 w-5 text-primary" />
                      <span>Dedicated account manager</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="mr-2 h-5 w-5 text-primary" />
                      <span>Priority support and SLA</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="mr-2 h-5 w-5 text-primary" />
                      <span>Custom feature development</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="mr-2 h-5 w-5 text-primary" />
                      <span>On-premise deployment options</span>
                    </li>
                  </ul>
                  <Button size="lg" asChild>
                    <Link href="/contact">Contact Sales</Link>
                  </Button>
                </div>
                <div className="bg-white dark:bg-slate-800 p-8 rounded-xl shadow-lg">
                  <h3 className="text-2xl font-bold mb-4">Request a Custom Quote</h3>
                  <p className="text-slate-500 dark:text-slate-400 mb-6">
                    Fill out the form below and our team will get back to you within 24 hours.
                  </p>
                  <form className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <label htmlFor="name" className="text-sm font-medium">
                          Name
                        </label>
                        <input id="name" placeholder="Your name" className="w-full p-2 border rounded-md" />
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="email" className="text-sm font-medium">
                          Email
                        </label>
                        <input
                          id="email"
                          type="email"
                          placeholder="Your email"
                          className="w-full p-2 border rounded-md"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="institution" className="text-sm font-medium">
                        Institution
                      </label>
                      <input id="institution" placeholder="Institution name" className="w-full p-2 border rounded-md" />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="message" className="text-sm font-medium">
                        Message
                      </label>
                      <textarea
                        id="message"
                        placeholder="Tell us about your requirements"
                        rows={4}
                        className="w-full p-2 border rounded-md"
                      ></textarea>
                    </div>
                    <Button type="submit" className="w-full">
                      Submit Request
                    </Button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}

function PricingCard({ plan }) {
  return (
    <Card className={`flex flex-col ${plan.popular ? "border-primary shadow-lg" : ""}`}>
      {plan.popular && (
        <div className="bg-primary text-primary-foreground text-center py-2 font-medium">Most Popular</div>
      )}
      <CardHeader>
        <CardTitle>{plan.name}</CardTitle>
        <CardDescription>{plan.description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-1">
        <div className="mb-6">
          <span className="text-4xl font-bold">${plan.price}</span>
          <span className="text-slate-500 dark:text-slate-400">/month</span>
          {plan.perStudent && <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">per student</p>}
        </div>
        <ul className="space-y-2">
          {plan.features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <Check className="mr-2 h-5 w-5 text-primary shrink-0" />
              <span>{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter>
        <Button
          className={`w-full ${plan.popular ? "" : "bg-slate-900 dark:bg-slate-50 dark:text-slate-900"}`}
          variant={plan.popular ? "default" : "outline"}
          asChild
        >
          <Link href="/auth/register">Start Free Trial</Link>
        </Button>
      </CardFooter>
    </Card>
  )
}

const monthlyPlans = [
  {
    name: "Starter",
    description: "For small schools just getting started",
    price: "2.99",
    perStudent: true,
    features: [
      "Up to 200 students",
      "Student & teacher management",
      "Attendance tracking",
      "Basic grade management",
      "Parent portal",
      "Email support",
    ],
  },
  {
    name: "Professional",
    description: "For growing educational institutions",
    price: "4.99",
    perStudent: true,
    popular: true,
    features: [
      "Up to 1,000 students",
      "All Starter features",
      "Advanced grade management",
      "Financial management",
      "Timetable scheduling",
      "SMS notifications",
      "Priority email support",
      "API access",
    ],
  },
  {
    name: "Enterprise",
    description: "For large schools and districts",
    price: "7.99",
    perStudent: true,
    features: [
      "Unlimited students",
      "All Professional features",
      "Advanced reporting & analytics",
      "Custom integrations",
      "White-labeling",
      "Dedicated account manager",
      "24/7 phone & email support",
      "On-site training",
    ],
  },
]

const annualPlans = [
  {
    name: "Starter",
    description: "For small schools just getting started",
    price: "2.39",
    perStudent: true,
    features: [
      "Up to 200 students",
      "Student & teacher management",
      "Attendance tracking",
      "Basic grade management",
      "Parent portal",
      "Email support",
    ],
  },
  {
    name: "Professional",
    description: "For growing educational institutions",
    price: "3.99",
    perStudent: true,
    popular: true,
    features: [
      "Up to 1,000 students",
      "All Starter features",
      "Advanced grade management",
      "Financial management",
      "Timetable scheduling",
      "SMS notifications",
      "Priority email support",
      "API access",
    ],
  },
  {
    name: "Enterprise",
    description: "For large schools and districts",
    price: "6.39",
    perStudent: true,
    features: [
      "Unlimited students",
      "All Professional features",
      "Advanced reporting & analytics",
      "Custom integrations",
      "White-labeling",
      "Dedicated account manager",
      "24/7 phone & email support",
      "On-site training",
    ],
  },
]

const faqs = [
  {
    question: "How does the per-student pricing work?",
    answer:
      "Our pricing is based on the number of active students in your system. You only pay for the students you have enrolled, making it scalable for institutions of all sizes.",
  },
  {
    question: "Can I switch plans later?",
    answer:
      "Yes, you can upgrade or downgrade your plan at any time. When upgrading, you'll get immediate access to new features. When downgrading, changes will take effect at the start of your next billing cycle.",
  },
  {
    question: "Is there a setup fee?",
    answer:
      "No, there are no setup fees for any of our plans. You only pay the advertised monthly or annual subscription fee.",
  },
  {
    question: "Do you offer discounts for educational institutions?",
    answer:
      "Yes, we offer special discounts for public schools, non-profits, and educational institutions in developing countries. Contact our sales team for more information.",
  },
  {
    question: "What payment methods do you accept?",
    answer:
      "We accept all major credit cards, PayPal, and bank transfers for annual plans. For some regions, we also support local payment methods.",
  },
  {
    question: "Is my data secure?",
    answer:
      "Yes, we take data security very seriously. Our platform is FERPA compliant, uses encryption for all data, and undergoes regular security audits.",
  },
]
