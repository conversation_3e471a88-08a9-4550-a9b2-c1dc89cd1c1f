"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, Clock, Download, BookOpen, MapPin, User } from "lucide-react"
import { useTenant } from "@/contexts/tenant-context"

// Mock data for timetable
const teacherTimetable = {
  days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
  periods: [
    { time: "08:00 - 09:30", name: "Period 1" },
    { time: "09:45 - 11:15", name: "Period 2" },
    { time: "11:30 - 13:00", name: "Period 3" },
    { time: "14:00 - 15:30", name: "Period 4" },
    { time: "15:45 - 17:15", name: "Period 5" },
  ],
  schedule: [
    // Monday
    [
      { subject: "Mathematics", class: "Grade 5A", room: "Room 101" },
      { subject: "Mathematics", class: "Grade 6B", room: "Room 105" },
      { subject: "Free Period", class: "", room: "" },
      { subject: "Mathematics", class: "Grade 5C", room: "Room 102" },
      { subject: "Department Meeting", class: "", room: "Staff Room" },
    ],
    // Tuesday
    [
      { subject: "Free Period", class: "", room: "" },
      { subject: "Mathematics", class: "Grade 6A", room: "Room 104" },
      { subject: "Mathematics", class: "Grade 5B", room: "Room 103" },
      { subject: "Mathematics", class: "Grade 6B", room: "Room 105" },
      { subject: "Free Period", class: "", room: "" },
    ],
    // Wednesday
    [
      { subject: "Mathematics", class: "Grade 5A", room: "Room 101" },
      { subject: "Free Period", class: "", room: "" },
      { subject: "Mathematics", class: "Grade 5C", room: "Room 102" },
      { subject: "Staff Meeting", class: "", room: "Conference Room" },
      { subject: "Free Period", class: "", room: "" },
    ],
    // Thursday
    [
      { subject: "Free Period", class: "", room: "" },
      { subject: "Mathematics", class: "Grade 6A", room: "Room 104" },
      { subject: "Mathematics", class: "Grade 5B", room: "Room 103" },
      { subject: "Mathematics", class: "Grade 6B", room: "Room 105" },
      { subject: "Parent Consultation", class: "", room: "Meeting Room" },
    ],
    // Friday
    [
      { subject: "Mathematics", class: "Grade 5A", room: "Room 101" },
      { subject: "Mathematics", class: "Grade 6A", room: "Room 104" },
      { subject: "Mathematics", class: "Grade 5C", room: "Room 102" },
      { subject: "Free Period", class: "", room: "" },
      { subject: "Free Period", class: "", room: "" },
    ],
  ],
}

const studentTimetable = {
  days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
  periods: [
    { time: "08:00 - 09:30", name: "Period 1" },
    { time: "09:45 - 11:15", name: "Period 2" },
    { time: "11:30 - 13:00", name: "Period 3" },
    { time: "14:00 - 15:30", name: "Period 4" },
    { time: "15:45 - 17:15", name: "Period 5" },
  ],
  schedule: [
    // Monday
    [
      { subject: "Mathematics", teacher: "Mr. John Smith", room: "Room 101" },
      { subject: "English", teacher: "Ms. Sarah Williams", room: "Room 203" },
      { subject: "Lunch Break", teacher: "", room: "Cafeteria" },
      { subject: "Science", teacher: "Mrs. Emily Johnson", room: "Lab 2" },
      { subject: "History", teacher: "Mr. Robert Brown", room: "Room 105" },
    ],
    // Tuesday
    [
      { subject: "Physical Education", teacher: "Mr. Michael Davis", room: "Sports Field" },
      { subject: "Mathematics", teacher: "Mr. John Smith", room: "Room 101" },
      { subject: "Lunch Break", teacher: "", room: "Cafeteria" },
      { subject: "Art", teacher: "Ms. Jennifer Lee", room: "Art Studio" },
      { subject: "Science", teacher: "Mrs. Emily Johnson", room: "Lab 2" },
    ],
    // Wednesday
    [
      { subject: "Mathematics", teacher: "Mr. John Smith", room: "Room 101" },
      { subject: "English", teacher: "Ms. Sarah Williams", room: "Room 203" },
      { subject: "Lunch Break", teacher: "", room: "Cafeteria" },
      { subject: "Computer Science", teacher: "Mr. David Wilson", room: "Computer Lab" },
      { subject: "Music", teacher: "Mrs. Lisa Garcia", room: "Music Room" },
    ],
    // Thursday
    [
      { subject: "History", teacher: "Mr. Robert Brown", room: "Room 105" },
      { subject: "Mathematics", teacher: "Mr. John Smith", room: "Room 101" },
      { subject: "Lunch Break", teacher: "", room: "Cafeteria" },
      { subject: "Science", teacher: "Mrs. Emily Johnson", room: "Lab 2" },
      { subject: "English", teacher: "Ms. Sarah Williams", room: "Room 203" },
    ],
    // Friday
    [
      { subject: "Mathematics", teacher: "Mr. John Smith", room: "Room 101" },
      { subject: "Physical Education", teacher: "Mr. Michael Davis", room: "Sports Field" },
      { subject: "Lunch Break", teacher: "", room: "Cafeteria" },
      { subject: "Science", teacher: "Mrs. Emily Johnson", room: "Lab 2" },
      { subject: "Library", teacher: "Mrs. Karen Martinez", room: "Library" },
    ],
  ],
}

// Default empty timetable structure to prevent null errors
const defaultTimetable = {
  days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
  periods: [],
  schedule: []
}

export default function TimetablePage() {
  const { currentUser } = useTenant()
  const [isLoading, setIsLoading] = useState(true)
  const [timetable, setTimetable] = useState<any>(defaultTimetable)
  const [selectedDay, setSelectedDay] = useState("Monday")
  const [selectedClass, setSelectedClass] = useState("")

  useEffect(() => {
    // Simulate API call to fetch timetable data based on user role
    const fetchData = async () => {
      try {
        await new Promise((resolve) => setTimeout(resolve, 1000))

        if (currentUser?.role === "TEACHER") {
          setTimetable(teacherTimetable)
        } else if (currentUser?.role === "STUDENT" || currentUser?.role === "PARENT") {
          setTimetable(studentTimetable)
        } else {
          // Fallback for unknown roles or when currentUser is null
          setTimetable(defaultTimetable)
        }
      } catch (error) {
        console.error("Error loading timetable:", error)
        // Set default timetable on error
        setTimetable(defaultTimetable)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [currentUser])

  const isTeacher = currentUser?.role === "TEACHER"
  const isStudent = currentUser?.role === "STUDENT"
  const isParent = currentUser?.role === "PARENT"

  // Get the day index for the selected day
  const dayIndex = timetable?.days?.findIndex((day: string) => day === selectedDay) ?? 0

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Timetable</h1>
          <p className="text-gray-500">
            {isTeacher
              ? "View and manage your teaching schedule"
              : isParent
                ? "View your child's class schedule"
                : "View your class schedule and room assignments"}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Download Schedule
          </Button>
          <Button>
            <Calendar className="mr-2 h-4 w-4" />
            Academic Calendar
          </Button>
        </div>
      </div>

      {isTeacher && (
        <div className="flex flex-col md:flex-row gap-4">
          <div className="md:w-1/3">
            <Select value={selectedClass} onValueChange={setSelectedClass}>
              <SelectTrigger>
                <SelectValue placeholder="All Classes" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Classes</SelectItem>
                <SelectItem value="Grade 5A">Grade 5A</SelectItem>
                <SelectItem value="Grade 5B">Grade 5B</SelectItem>
                <SelectItem value="Grade 5C">Grade 5C</SelectItem>
                <SelectItem value="Grade 6A">Grade 6A</SelectItem>
                <SelectItem value="Grade 6B">Grade 6B</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <p>Loading timetable...</p>
        </div>
      ) : !timetable || !timetable.days || timetable.days.length === 0 ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <p className="text-gray-500">No timetable data available</p>
            <p className="text-sm text-gray-400 mt-2">Please contact your administrator if this issue persists.</p>
          </div>
        </div>
      ) : (
        <>
          <Tabs defaultValue={selectedDay} onValueChange={setSelectedDay} className="space-y-4">
            <TabsList className="grid grid-cols-5 w-full">
              {(timetable?.days || []).map((day: string) => (
                <TabsTrigger key={day} value={day}>
                  {day}
                </TabsTrigger>
              ))}
            </TabsList>

            {(timetable?.days || []).map((day: string, dayIdx: number) => (
              <TabsContent key={day} value={day} className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>{day}'s Schedule</CardTitle>
                    <CardDescription>
                      {isTeacher
                        ? "Your teaching schedule for " + day
                        : isParent
                          ? "Your child's class schedule for " + day
                          : "Your class schedule for " + day}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {(timetable?.periods || []).map((period: any, periodIdx: number) => {
                        const session = timetable?.schedule?.[dayIdx]?.[periodIdx] || { subject: "No Data", class: "", room: "" }

                        // Skip if filtering by class and this period doesn't match
                        if (isTeacher && selectedClass && session.class !== selectedClass && session.class !== "") {
                          return null
                        }

                        return (
                          <div key={periodIdx} className="flex flex-col md:flex-row border rounded-lg overflow-hidden">
                            <div className="bg-gray-100 p-4 md:w-1/4 flex flex-col justify-center">
                              <p className="font-medium">{period.name}</p>
                              <div className="flex items-center text-gray-500 mt-1">
                                <Clock className="h-4 w-4 mr-1" />
                                <span className="text-sm">{period.time}</span>
                              </div>
                            </div>
                            <div className="p-4 md:w-3/4 flex flex-col justify-center">
                              {session.subject === "Free Period" || session.subject === "Lunch Break" ? (
                                <div className="flex items-center">
                                  <Badge variant="outline" className="mr-2">
                                    {session.subject}
                                  </Badge>
                                  {session.room && (
                                    <div className="flex items-center text-gray-500 text-sm">
                                      <MapPin className="h-4 w-4 mr-1" />
                                      {session.room}
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <div className="space-y-2">
                                  <div className="flex items-center">
                                    <BookOpen className="h-5 w-5 mr-2 text-emerald-600" />
                                    <span className="font-medium">{session.subject}</span>
                                    {session.subject !== "Staff Meeting" &&
                                      session.subject !== "Department Meeting" &&
                                      session.subject !== "Parent Consultation" && (
                                        <Badge variant="outline" className="ml-2">
                                          {isTeacher ? session.class : ""}
                                        </Badge>
                                      )}
                                  </div>
                                  <div className="flex flex-wrap gap-4">
                                    {isTeacher && session.class && (
                                      <div className="flex items-center text-gray-500 text-sm">
                                        <User className="h-4 w-4 mr-1" />
                                        {session.class}
                                      </div>
                                    )}
                                    {isStudent && session.teacher && (
                                      <div className="flex items-center text-gray-500 text-sm">
                                        <User className="h-4 w-4 mr-1" />
                                        {session.teacher}
                                      </div>
                                    )}
                                    {session.room && (
                                      <div className="flex items-center text-gray-500 text-sm">
                                        <MapPin className="h-4 w-4 mr-1" />
                                        {session.room}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>

          <Card>
            <CardHeader>
              <CardTitle>Weekly Overview</CardTitle>
              <CardDescription>
                {isTeacher
                  ? "Your complete teaching schedule for the week"
                  : isParent
                    ? "Your child's complete class schedule for the week"
                    : "Your complete class schedule for the week"}
              </CardDescription>
            </CardHeader>
            <CardContent className="overflow-x-auto">
              <div className="min-w-[800px]">
                <div className="grid grid-cols-6 gap-2">
                  <div className="bg-gray-100 p-3 rounded-lg font-medium">Time</div>
                  {(timetable?.days || []).map((day: string) => (
                    <div key={day} className="bg-gray-100 p-3 rounded-lg font-medium">
                      {day}
                    </div>
                  ))}
                </div>

                {(timetable?.periods || []).map((period: any, periodIdx: number) => (
                  <div key={periodIdx} className="grid grid-cols-6 gap-2 mt-2">
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <p className="font-medium">{period.name}</p>
                      <p className="text-sm text-gray-500">{period.time}</p>
                    </div>

                    {(timetable?.days || []).map((day: string, dayIdx: number) => {
                      const session = timetable?.schedule?.[dayIdx]?.[periodIdx] || { subject: "No Data", class: "", room: "" }
                      const isFree = session.subject === "Free Period" || session.subject === "Lunch Break"

                      return (
                        <div
                          key={dayIdx}
                          className={`p-3 rounded-lg ${
                            isFree ? "bg-gray-50 text-gray-500" : "bg-emerald-50 text-emerald-700"
                          }`}
                        >
                          <p className="font-medium">{session.subject}</p>
                          <p className="text-sm">
                            {isTeacher ? session.class : isStudent || isParent ? session.teacher : ""}
                          </p>
                          {session.room && (
                            <p className="text-xs mt-1">
                              <MapPin className="h-3 w-3 inline mr-1" />
                              {session.room}
                            </p>
                          )}
                        </div>
                      )
                    })}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
