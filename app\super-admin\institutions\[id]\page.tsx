"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Building,
  Users,
  CreditCard,
  Download,
  Mail,
  Phone,
  MapPin,
  Globe,
  Clock,
  CheckCircle,
  AlertCircle,
  Edit,
  Trash2,
} from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription } from "@/components/ui/alert"

// Mock data for institution details
const mockInstitutionData = {
  "1": {
    id: "1",
    name: "Lusaka Primary School",
    logo: "/placeholder.svg?height=100&width=100",
    type: "Primary",
    location: "Lusaka",
    address: "Plot 123, Great East Road, Lusaka",
    email: "<EMAIL>",
    phone: "+260 97 1234567",
    website: "www.lusakaprimary.edu.zm",
    status: "Active",
    subscription: "Premium",
    subscriptionExpiry: "December 31, 2025",
    registrationDate: "January 15, 2023",
    students: 850,
    teachers: 45,
    staff: 12,
    classes: 25,
    storageUsed: 75,
    storageLimit: 100,
    features: [
      "Student Management",
      "Teacher Management",
      "Fee Management",
      "Attendance Tracking",
      "Grade Management",
      "Timetable",
      "Reports",
      "Parent Portal",
      "SMS Notifications",
      "API Access",
    ],
    admins: [
      {
        id: "1",
        name: "Michael Banda",
        email: "<EMAIL>",
        role: "Principal",
        status: "Active",
        lastLogin: "May 20, 2025",
      },
      {
        id: "2",
        name: "Sarah Mwanza",
        email: "<EMAIL>",
        role: "Vice Principal",
        status: "Active",
        lastLogin: "May 19, 2025",
      },
      {
        id: "3",
        name: "James Tembo",
        email: "<EMAIL>",
        role: "Admin Staff",
        status: "Active",
        lastLogin: "May 18, 2025",
      },
    ],
    recentActivities: [
      {
        id: "1",
        action: "User Login",
        user: "Michael Banda",
        timestamp: "May 20, 2025 09:15 AM",
      },
      {
        id: "2",
        action: "Student Added",
        user: "Sarah Mwanza",
        timestamp: "May 19, 2025 02:30 PM",
      },
      {
        id: "3",
        action: "Fee Payment Recorded",
        user: "James Tembo",
        timestamp: "May 18, 2025 11:45 AM",
      },
      {
        id: "4",
        action: "Report Generated",
        user: "Michael Banda",
        timestamp: "May 17, 2025 04:20 PM",
      },
      {
        id: "5",
        action: "Attendance Marked",
        user: "Sarah Mwanza",
        timestamp: "May 16, 2025 08:30 AM",
      },
    ],
    billingHistory: [
      {
        id: "1",
        date: "January 1, 2025",
        amount: "K2,500",
        status: "Paid",
        method: "Credit Card",
        invoice: "INV-2025-001",
      },
      {
        id: "2",
        date: "October 1, 2024",
        amount: "K2,500",
        status: "Paid",
        method: "Bank Transfer",
        invoice: "INV-2024-004",
      },
      {
        id: "3",
        date: "July 1, 2024",
        amount: "K2,500",
        status: "Paid",
        method: "Credit Card",
        invoice: "INV-2024-003",
      },
      {
        id: "4",
        date: "April 1, 2024",
        amount: "K2,500",
        status: "Paid",
        method: "Bank Transfer",
        invoice: "INV-2024-002",
      },
      {
        id: "5",
        date: "January 1, 2024",
        amount: "K2,500",
        status: "Paid",
        method: "Credit Card",
        invoice: "INV-2024-001",
      },
    ],
  },
  "2": {
    id: "2",
    name: "Kitwe Secondary School",
    logo: "/placeholder.svg?height=100&width=100",
    type: "Secondary",
    location: "Kitwe",
    address: "Plot 456, Freedom Avenue, Kitwe",
    email: "<EMAIL>",
    phone: "+260 96 7654321",
    website: "www.kitwesecondary.edu.zm",
    status: "Active",
    subscription: "Standard",
    subscriptionExpiry: "October 31, 2025",
    registrationDate: "August 22, 2023",
    students: 1200,
    teachers: 78,
    staff: 25,
    classes: 35,
    storageUsed: 60,
    storageLimit: 75,
    features: [
      "Student Management",
      "Teacher Management",
      "Fee Management",
      "Attendance Tracking",
      "Grade Management",
      "Timetable",
      "Reports",
      "Parent Portal",
    ],
    admins: [
      {
        id: "1",
        name: "John Banda",
        email: "<EMAIL>",
        role: "Principal",
        status: "Active",
        lastLogin: "May 20, 2025",
      },
      {
        id: "2",
        name: "Mary Zulu",
        email: "<EMAIL>",
        role: "Vice Principal",
        status: "Active",
        lastLogin: "May 19, 2025",
      },
    ],
    recentActivities: [
      {
        id: "1",
        action: "User Login",
        user: "John Banda",
        timestamp: "May 20, 2025 08:45 AM",
      },
      {
        id: "2",
        action: "Timetable Updated",
        user: "Mary Zulu",
        timestamp: "May 19, 2025 01:15 PM",
      },
      {
        id: "3",
        action: "Exam Results Uploaded",
        user: "John Banda",
        timestamp: "May 18, 2025 03:30 PM",
      },
    ],
    billingHistory: [
      {
        id: "1",
        date: "May 1, 2025",
        amount: "K1,200",
        status: "Paid",
        method: "Credit Card",
        invoice: "INV-2025-002",
      },
      {
        id: "2",
        date: "February 1, 2025",
        amount: "K1,200",
        status: "Paid",
        method: "Bank Transfer",
        invoice: "INV-2025-001",
      },
      {
        id: "3",
        date: "November 1, 2024",
        amount: "K1,200",
        status: "Paid",
        method: "Credit Card",
        invoice: "INV-2024-004",
      },
    ],
  },
  "4": {
    id: "4",
    name: "Livingstone College",
    logo: "/placeholder.svg?height=100&width=100",
    type: "College",
    location: "Livingstone",
    address: "Plot 789, Victoria Road, Livingstone",
    email: "<EMAIL>",
    phone: "+260 95 1122334",
    website: "www.livingstonecollege.edu.zm",
    status: "Pending",
    subscription: "Enterprise",
    subscriptionExpiry: "Not Started",
    registrationDate: "May 18, 2025",
    students: 0,
    teachers: 0,
    staff: 0,
    classes: 0,
    storageUsed: 0,
    storageLimit: 200,
    features: [
      "Student Management",
      "Teacher Management",
      "Fee Management",
      "Attendance Tracking",
      "Grade Management",
      "Timetable",
      "Reports",
      "Parent Portal",
      "SMS Notifications",
      "API Access",
      "Advanced Analytics",
      "Custom Branding",
      "Multi-campus Support",
    ],
    admins: [
      {
        id: "1",
        name: "David Mutale",
        email: "<EMAIL>",
        role: "Director",
        status: "Pending",
        lastLogin: "Never",
      },
    ],
    recentActivities: [
      {
        id: "1",
        action: "Institution Registered",
        user: "David Mutale",
        timestamp: "May 18, 2025 10:30 AM",
      },
    ],
    billingHistory: [],
  },
}

export default function InstitutionDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [institution, setInstitution] = useState<any>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showApproveDialog, setShowApproveDialog] = useState(false)
  const [showSuspendDialog, setShowSuspendDialog] = useState(false)
  const [showAddAdminDialog, setShowAddAdminDialog] = useState(false)
  const [newAdmin, setNewAdmin] = useState({
    name: "",
    email: "",
    role: "",
  })

  useEffect(() => {
    // Simulate API call to fetch institution details
    const fetchInstitution = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      const institutionId = params.id as string

      if (mockInstitutionData[institutionId]) {
        setInstitution(mockInstitutionData[institutionId])
      } else {
        // Handle institution not found
        router.push("/super-admin/institutions")
      }

      setIsLoading(false)
    }

    fetchInstitution()
  }, [params.id, router])

  const handleApproveInstitution = () => {
    // In a real app, this would call your API
    setInstitution({
      ...institution,
      status: "Active",
    })
    setShowApproveDialog(false)
  }

  const handleSuspendInstitution = () => {
    // In a real app, this would call your API
    setInstitution({
      ...institution,
      status: "Suspended",
    })
    setShowSuspendDialog(false)
  }

  const handleDeleteInstitution = () => {
    // In a real app, this would call your API
    setShowDeleteDialog(false)
    router.push("/super-admin/institutions")
  }

  const handleAddAdmin = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, this would call your API
    const newAdminWithId = {
      id: `${institution.admins.length + 1}`,
      ...newAdmin,
      status: "Active",
      lastLogin: "Never",
    }

    setInstitution({
      ...institution,
      admins: [...institution.admins, newAdminWithId],
    })

    setNewAdmin({
      name: "",
      email: "",
      role: "",
    })

    setShowAddAdminDialog(false)
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>Loading institution details...</p>
      </div>
    )
  }

  if (!institution) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>Institution not found</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex items-center gap-4">
          <Avatar className="h-16 w-16 rounded-md">
            <AvatarImage src={institution.logo || "/placeholder.svg"} alt={institution.name} />
            <AvatarFallback className="rounded-md bg-emerald-100 text-emerald-700 text-lg">
              {institution.name.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-3xl font-bold tracking-tight">{institution.name}</h1>
              <Badge
                variant={
                  institution.status === "Active"
                    ? "default"
                    : institution.status === "Pending"
                      ? "outline"
                      : "destructive"
                }
              >
                {institution.status}
              </Badge>
            </div>
            <p className="text-gray-500">
              {institution.type} School • {institution.location}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Link href={`/super-admin/institutions/${institution.id}/edit`}>
            <Button variant="outline">
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          </Link>

          {institution.status === "Pending" ? (
            <Button onClick={() => setShowApproveDialog(true)}>
              <CheckCircle className="mr-2 h-4 w-4" />
              Approve
            </Button>
          ) : institution.status === "Active" ? (
            <Button variant="outline" onClick={() => setShowSuspendDialog(true)}>
              <AlertCircle className="mr-2 h-4 w-4" />
              Suspend
            </Button>
          ) : (
            <Button onClick={() => setShowApproveDialog(true)}>
              <CheckCircle className="mr-2 h-4 w-4" />
              Reactivate
            </Button>
          )}

          <Button variant="destructive" onClick={() => setShowDeleteDialog(true)}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Students</CardTitle>
            <Users className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{institution.students.toLocaleString()}</div>
            <p className="text-xs text-gray-500">Total enrolled students</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Teachers</CardTitle>
            <Users className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{institution.teachers}</div>
            <p className="text-xs text-gray-500">Total teaching staff</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Subscription</CardTitle>
            <CreditCard className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{institution.subscription}</div>
            <p className="text-xs text-gray-500">Expires: {institution.subscriptionExpiry}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Storage</CardTitle>
            <Building className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {institution.storageUsed} GB / {institution.storageLimit} GB
            </div>
            <Progress value={(institution.storageUsed / institution.storageLimit) * 100} className="h-2 mt-2" />
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="admins">Administrators</TabsTrigger>
          <TabsTrigger value="subscription">Subscription</TabsTrigger>
          <TabsTrigger value="activity">Activity Log</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Institution Details</CardTitle>
                <CardDescription>Basic information about the institution</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Institution Type</p>
                      <p>{institution.type}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Registration Date</p>
                      <p>{institution.registrationDate}</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Address</p>
                    <div className="flex items-start mt-1">
                      <MapPin className="h-4 w-4 text-gray-500 mr-2 mt-0.5" />
                      <p>{institution.address}</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Contact Information</p>
                    <div className="space-y-2 mt-1">
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 text-gray-500 mr-2" />
                        <p>{institution.email}</p>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 text-gray-500 mr-2" />
                        <p>{institution.phone}</p>
                      </div>
                      <div className="flex items-center">
                        <Globe className="h-4 w-4 text-gray-500 mr-2" />
                        <p>{institution.website}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Statistics</p>
                    <div className="grid grid-cols-2 gap-4 mt-1">
                      <div>
                        <p className="text-sm text-gray-500">Students</p>
                        <p className="font-medium">{institution.students.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Teachers</p>
                        <p className="font-medium">{institution.teachers}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Staff</p>
                        <p className="font-medium">{institution.staff}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Classes</p>
                        <p className="font-medium">{institution.classes}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Subscription Details</CardTitle>
                <CardDescription>Current plan and features</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">{institution.subscription} Plan</p>
                        <p className="text-sm text-gray-500">
                          {institution.status === "Pending"
                            ? "Pending activation"
                            : `Expires on ${institution.subscriptionExpiry}`}
                        </p>
                      </div>
                      <Badge variant="outline">{institution.subscription}</Badge>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-2">Storage Usage</p>
                    <div className="space-y-1">
                      <div className="flex justify-between items-center">
                        <p className="text-sm">
                          {institution.storageUsed} GB of {institution.storageLimit} GB used
                        </p>
                        <p className="text-sm font-medium">
                          {Math.round((institution.storageUsed / institution.storageLimit) * 100)}%
                        </p>
                      </div>
                      <Progress value={(institution.storageUsed / institution.storageLimit) * 100} className="h-2" />
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-2">Included Features</p>
                    <div className="grid grid-cols-2 gap-2">
                      {institution.features.map((feature: string, index: number) => (
                        <div key={index} className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-emerald-500 mr-2" />
                          <p className="text-sm">{feature}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="pt-2">
                    <Link href={`/super-admin/institutions/${institution.id}/subscription`}>
                      <Button className="w-full">Manage Subscription</Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest actions performed in this institution</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {institution.recentActivities.length === 0 ? (
                  <p className="text-center text-gray-500 py-4">No recent activities</p>
                ) : (
                  institution.recentActivities.map((activity: any) => (
                    <div key={activity.id} className="flex items-start">
                      <div className="mr-3 bg-emerald-100 text-emerald-700 p-2 rounded">
                        <Clock className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium">{activity.action}</p>
                        <p className="text-sm text-gray-500">By {activity.user}</p>
                        <p className="text-xs text-gray-500">{activity.timestamp}</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                View All Activity
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="admins" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Institution Administrators</CardTitle>
                <CardDescription>Users with administrative access to this institution</CardDescription>
              </div>
              <Button onClick={() => setShowAddAdminDialog(true)}>
                <Users className="mr-2 h-4 w-4" />
                Add Admin
              </Button>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Login</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {institution.admins.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center h-24">
                          No administrators found
                        </TableCell>
                      </TableRow>
                    ) : (
                      institution.admins.map((admin: any) => (
                        <TableRow key={admin.id}>
                          <TableCell className="font-medium">{admin.name}</TableCell>
                          <TableCell>{admin.email}</TableCell>
                          <TableCell>{admin.role}</TableCell>
                          <TableCell>
                            <Badge variant={admin.status === "Active" ? "default" : "outline"}>{admin.status}</Badge>
                          </TableCell>
                          <TableCell>{admin.lastLogin}</TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Edit</span>
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscription" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Subscription Management</CardTitle>
              <CardDescription>Manage the institution's subscription plan and billing</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium text-lg">{institution.subscription} Plan</p>
                      <p className="text-gray-500">
                        {institution.status === "Pending"
                          ? "Pending activation"
                          : `Expires on ${institution.subscriptionExpiry}`}
                      </p>
                    </div>
                    <Badge variant="outline" className="text-base px-3 py-1">
                      {institution.subscription}
                    </Badge>
                  </div>

                  <div className="mt-4 grid grid-cols-3 gap-4">
                    <div className="bg-white p-3 rounded border">
                      <p className="text-sm text-gray-500">Monthly Price</p>
                      <p className="text-lg font-bold">
                        {institution.subscription === "Basic"
                          ? "K500"
                          : institution.subscription === "Standard"
                            ? "K1,200"
                            : "K2,500"}
                      </p>
                    </div>
                    <div className="bg-white p-3 rounded border">
                      <p className="text-sm text-gray-500">Billing Cycle</p>
                      <p className="text-lg font-bold">Monthly</p>
                    </div>
                    <div className="bg-white p-3 rounded border">
                      <p className="text-sm text-gray-500">Next Billing</p>
                      <p className="text-lg font-bold">
                        {institution.status === "Pending"
                          ? "N/A"
                          : institution.subscriptionExpiry.split(" ")[0] +
                            " 1, " +
                            institution.subscriptionExpiry.split(" ")[1]}
                      </p>
                    </div>
                  </div>

                  <div className="mt-4 flex gap-2">
                    <Button>Change Plan</Button>
                    <Button variant="outline">Cancel Subscription</Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Billing History</h3>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Payment Method</TableHead>
                          <TableHead>Invoice</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {institution.billingHistory.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center h-24">
                              No billing history available
                            </TableCell>
                          </TableRow>
                        ) : (
                          institution.billingHistory.map((bill: any) => (
                            <TableRow key={bill.id}>
                              <TableCell>{bill.date}</TableCell>
                              <TableCell>{bill.amount}</TableCell>
                              <TableCell>
                                <Badge variant={bill.status === "Paid" ? "default" : "destructive"}>
                                  {bill.status}
                                </Badge>
                              </TableCell>
                              <TableCell>{bill.method}</TableCell>
                              <TableCell>
                                <Button variant="ghost" size="sm">
                                  <Download className="h-4 w-4 mr-1" />
                                  {bill.invoice}
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Activity Log</CardTitle>
              <CardDescription>Complete history of actions performed in this institution</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {institution.recentActivities.length === 0 ? (
                  <p className="text-center text-gray-500 py-4">No activities recorded</p>
                ) : (
                  institution.recentActivities.map((activity: any) => (
                    <div key={activity.id} className="flex items-start border-b pb-4 last:border-0">
                      <div className="mr-3 bg-emerald-100 text-emerald-700 p-2 rounded">
                        <Clock className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium">{activity.action}</p>
                        <p className="text-sm text-gray-500">By {activity.user}</p>
                        <p className="text-xs text-gray-500">{activity.timestamp}</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
            <CardFooter>
              <div className="flex justify-between items-center w-full">
                <Button variant="outline" disabled>
                  Previous
                </Button>
                <p className="text-sm text-gray-500">Page 1 of 1</p>
                <Button variant="outline" disabled>
                  Next
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Approve Institution Dialog */}
      <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Institution</DialogTitle>
            <DialogDescription>
              This will activate the institution and grant access to all users. Are you sure you want to continue?
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                The institution will be able to access all features included in their subscription plan.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowApproveDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleApproveInstitution}>Approve Institution</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Suspend Institution Dialog */}
      <Dialog open={showSuspendDialog} onOpenChange={setShowSuspendDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Suspend Institution</DialogTitle>
            <DialogDescription>
              This will temporarily disable access to the system for all users in this institution. Are you sure you
              want to continue?
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                The institution will not be able to access the system until reactivated, but all data will be preserved.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSuspendDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleSuspendInstitution}>
              Suspend Institution
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Institution Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Institution</DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the institution and all associated data.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                All data including students, teachers, classes, and records will be permanently deleted.
              </AlertDescription>
            </Alert>
            <div className="bg-gray-100 p-3 rounded-md">
              <p className="font-medium">Please type the institution name to confirm:</p>
              <p className="text-sm text-gray-500 mt-1">{institution.name}</p>
              <Input className="mt-2" placeholder="Type institution name here" />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteInstitution}>
              Delete Institution
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Admin Dialog */}
      <Dialog open={showAddAdminDialog} onOpenChange={setShowAddAdminDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Administrator</DialogTitle>
            <DialogDescription>
              Add a new administrator to this institution. They will receive an email invitation.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddAdmin}>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={newAdmin.name}
                  onChange={(e) => setNewAdmin({ ...newAdmin, name: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={newAdmin.email}
                  onChange={(e) => setNewAdmin({ ...newAdmin, email: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select
                  value={newAdmin.role}
                  onValueChange={(value) => setNewAdmin({ ...newAdmin, role: value })}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Principal">Principal</SelectItem>
                    <SelectItem value="Vice Principal">Vice Principal</SelectItem>
                    <SelectItem value="Admin Staff">Admin Staff</SelectItem>
                    <SelectItem value="IT Administrator">IT Administrator</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="send-invitation" defaultChecked />
                <Label htmlFor="send-invitation">Send email invitation</Label>
              </div>
            </div>
            <DialogFooter className="mt-4">
              <Button variant="outline" type="button" onClick={() => setShowAddAdminDialog(false)}>
                Cancel
              </Button>
              <Button type="submit">Add Administrator</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
