"use client"

import type React from "react"

import { useState } from "react"
import { Users, Plus, Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useOnboarding } from "@/contexts/onboarding-context"

interface InviteUser {
  email: string
  role: string
  name: string
}

export function InviteUsersStep() {
  const { onboardingData, updateOnboardingData, nextStep, prevStep } = useOnboarding()
  const [newUser, setNewUser] = useState<InviteUser>({ email: "", role: "", name: "" })
  const [invitedUsers, setInvitedUsers] = useState<InviteUser[]>(onboardingData.invitedUsers || [])

  const handleAddUser = () => {
    if (newUser.email && newUser.role && newUser.name) {
      const updatedUsers = [...invitedUsers, newUser]
      setInvitedUsers(updatedUsers)
      updateOnboardingData({ invitedUsers: updatedUsers })
      setNewUser({ email: "", role: "", name: "" })
    }
  }

  const handleRemoveUser = (index: number) => {
    const updatedUsers = invitedUsers.filter((_, i) => i !== index)
    setInvitedUsers(updatedUsers)
    updateOnboardingData({ invitedUsers: updatedUsers })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    nextStep()
  }

  return (
    <Card className="border-0 shadow-none">
      <CardHeader>
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100">
          <Users className="h-6 w-6 text-emerald-600" />
        </div>
        <CardTitle className="text-center text-2xl">Invite Users</CardTitle>
        <CardDescription className="text-center">
          Invite teachers and staff to join your institution (optional)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form id="invite-form" onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  placeholder="e.g., Jane Smith"
                  value={newUser.name}
                  onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="e.g., <EMAIL>"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select value={newUser.role} onValueChange={(value) => setNewUser({ ...newUser, role: value })}>
                  <SelectTrigger id="role">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ADMIN">Administrator</SelectItem>
                    <SelectItem value="TEACHER">Teacher</SelectItem>
                    <SelectItem value="STAFF">Staff</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={handleAddUser}
              disabled={!newUser.email || !newUser.role || !newUser.name}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </div>

          {invitedUsers.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Users to Invite</h3>
              <div className="rounded-md border">
                <div className="grid grid-cols-[1fr_1fr_auto] gap-4 p-3 font-medium text-sm text-muted-foreground">
                  <div>Name</div>
                  <div>Email</div>
                  <div className="text-right">Actions</div>
                </div>
                {invitedUsers.map((user, index) => (
                  <div key={index} className="grid grid-cols-[1fr_1fr_auto] gap-4 border-t p-3 text-sm items-center">
                    <div>{user.name}</div>
                    <div>{user.email}</div>
                    <div className="text-right">
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-red-500"
                        onClick={() => handleRemoveUser(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Remove</span>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="rounded-lg bg-muted p-4">
            <p className="text-sm font-medium">Note</p>
            <p className="text-xs text-muted-foreground">
              You can skip this step and invite users later from the dashboard. Invitations will be sent via email when
              you complete the setup.
            </p>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={prevStep}>
          Back
        </Button>
        <Button type="submit" form="invite-form">
          {invitedUsers.length > 0 ? "Continue & Send Invites" : "Skip & Continue"}
        </Button>
      </CardFooter>
    </Card>
  )
}
