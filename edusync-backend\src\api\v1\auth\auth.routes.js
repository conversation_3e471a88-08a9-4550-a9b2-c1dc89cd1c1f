const express = require('express');
const { body } = require('express-validator');
const authController = require('./auth.controller');
const { validateRequest } = require('../../../middleware/validateRequest');
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const router = express.Router();

/**
 * @route POST /api/v1/auth/login
 * @desc Login user
 * @access Public
 */
router.post(
  '/login',
  [ 
    body('email').isEmail().withMessage('Please provide a valid email'),
    body('password').notEmpty().withMessage('Password is required'),
    validateRequest,
  ],
  authController.login
);

/**
 * @route POST /api/v1/auth/refresh-token
 * @desc Refresh access token
 * @access Public (with refresh token)
 */
router.post('/refresh-token', authController.refreshToken);

/**
 * @route POST /api/v1/auth/logout
 * @desc Logout user
 * @access Private
 */
router.post('/logout', authenticate, authController.logout);

/**
 * @route POST /api/v1/auth/forgot-password
 * @desc Request password reset
 * @access Public
 */
router.post(
  '/forgot-password',
  [
    body('email').isEmail().withMessage('Please provide a valid email'),
    validateRequest,
  ],
  authController.forgotPassword
);

/**
 * @route POST /api/v1/auth/reset-password/:token
 * @desc Reset password with token
 * @access Public (with token)
 */
router.post(
  '/reset-password/:token',
  [
    body('password')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*])/)
      .withMessage('Password must include one lowercase, one uppercase, one number, and one special character'),
    body('confirmPassword').custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Passwords do not match');
      }
      return true;
    }),
    validateRequest,
  ],
  authController.resetPassword
);

/**
 * @route GET /api/v1/auth/verify-email/:token
 * @desc Verify email address
 * @access Public (with token)
 */
router.get('/verify-email/:token', authController.verifyEmail);

/**
 * @route POST /api/v1/auth/resend-verification
 * @desc Resend email verification
 * @access Private
 */
router.post('/resend-verification', authenticate, authController.resendVerification);

/**
 * @route GET /api/v1/auth/me
 * @desc Get current user information
 * @access Private
 */
router.get('/me', authenticate, authController.getCurrentUser);

/**
 * @route POST /api/v1/auth/change-password
 * @desc Change user password
 * @access Private
 */
router.post(
  '/change-password',
  authenticate,
  [
    body('currentPassword').notEmpty().withMessage('Current password is required'),
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*])/)
      .withMessage('Password must include one lowercase, one uppercase, one number, and one special character'),
    body('confirmPassword').custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Passwords do not match');
      }
      return true;
    }),
    validateRequest,
  ],
  authController.changePassword
);

module.exports = router;
