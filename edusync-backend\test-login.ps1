Write-Host "Testing login endpoint..."

$loginBody = @{
    email = "<EMAIL>"
    password = "TestPassword123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:4000/api/v1/auth/login" -Method Post -Body $loginBody -ContentType "application/json"
    Write-Host "✅ Login successful!" -ForegroundColor Green
    Write-Host "Access Token: $($loginResponse.data.accessToken)"
    
    $token = $loginResponse.data.accessToken
    
    # Test institution endpoint
    Write-Host "`nTesting institution endpoint..."
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    $institutionResponse = Invoke-RestMethod -Uri "http://localhost:4000/api/v1/institutions/360ccd63-cb03-4b5c-81c6-28d429c95b88" -Method Get -Headers $headers
    Write-Host "✅ Institution fetch successful!" -ForegroundColor Green
    Write-Host "Institution: $($institutionResponse | ConvertTo-Json -Depth 3)"
    
} catch {
    Write-Host "❌ Error:" -ForegroundColor Red
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody"
    }
}
