const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
const catchAsync = require('../../../utils/catchAsync');
const AppError = require('../../../utils/appError');
const { sendWelcomeEmail } = require('../../../services/emailService');
const { createAuditLog } = require('../../../services/auditService');

const prisma = new PrismaClient();

/**
 * Get all users with pagination and filtering
 */
exports.getUsers = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    role,
    isActive,
    email,
    name,
    sortBy = 'createdAt',
    order = 'desc',
  } = req.query;

  // Prepare filters
  const filters = {};
  if (role) filters.role = role;
  if (isActive !== undefined) filters.isActive = isActive === 'true';
  if (email) filters.email = { contains: email };
  if (name) {
    filters.OR = [
      { firstName: { contains: name } },
      { lastName: { contains: name } },
    ];
  }

  // Prepare sorting
  const orderBy = {};
  orderBy[sortBy] = order.toLowerCase();

  // Get total count for pagination
  const total = await prisma.user.count({ where: filters });

  // Get users
  const users = await prisma.user.findMany({
    where: filters,
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      phoneNumber: true,
      profileImageUrl: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
      lastLoginAt: true,
    },
    orderBy,
    skip: (parseInt(page) - 1) * parseInt(limit),
    take: parseInt(limit),
  });

  // Return response
  res.status(200).json({
    status: 'success',
    results: users.length,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / parseInt(limit)),
    },
    data: { users },
  });
});

/**
 * Get user by ID
 */
exports.getUserById = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // Check access permissions
  if (req.user.role !== 'SUPER_ADMIN' && req.user.id !== id) {
    // For non-super admins, check if they belong to the same institution or school
    const userInstitutions = await prisma.institutionUser.findMany({
      where: { userId: req.user.id },
      select: { institutionId: true },
    });

    const userSchools = await prisma.schoolUser.findMany({
      where: { userId: req.user.id },
      select: { schoolId: true },
    });

    const targetUserInstitutions = await prisma.institutionUser.findMany({
      where: { userId: id },
      select: { institutionId: true },
    });

    const targetUserSchools = await prisma.schoolUser.findMany({
      where: { userId: id },
      select: { schoolId: true },
    });

    const sharedInstitution = userInstitutions.some(ui => 
      targetUserInstitutions.some(tui => tui.institutionId === ui.institutionId)
    );

    const sharedSchool = userSchools.some(us => 
      targetUserSchools.some(tus => tus.schoolId === us.schoolId)
    );

    if (!sharedInstitution && !sharedSchool) {
      return next(new AppError('You do not have permission to view this user', 403));
    }
  }

  // Get user with related data
  const user = await prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      phoneNumber: true,
      profileImageUrl: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
      lastLoginAt: true,
      institutions: {
        select: {
          id: true,
          institutionId: true,
          role: true,
          isActive: true,
          institution: {
            select: {
              id: true,
              name: true,
              logo: true,
            },
          },
        },
      },
      schools: {
        select: {
          id: true,
          schoolId: true,
          role: true,
          isActive: true,
          school: {
            select: {
              id: true,
              name: true,
              type: true,
              institutionId: true,
              address: true,
              city: true,
              state: true,
              country: true,
              postalCode: true,
              phoneNumber: true,
              email: true,
              website: true,
              isActive: true,
            },
          },
        },
      },
    },
  });

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Return response
  res.status(200).json({
    status: 'success',
    data: { user },
  });
});

/**
 * Create a new user
 */
exports.createUser = catchAsync(async (req, res, next) => {
  const { email, password, firstName, lastName, role, phoneNumber, institutionId, schoolId } = req.body;

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email },
  });

  if (existingUser) {
    return next(new AppError('User with this email already exists', 400));
  }

  // Hash password
  const passwordHash = await bcrypt.hash(password, 12);

  // Create user transaction
  const user = await prisma.$transaction(async (tx) => {
    // Create new user
    const newUser = await tx.user.create({
      data: {
        email,
        passwordHash,
        firstName,
        lastName,
        role,
        phoneNumber,
        isEmailVerified: true, // Auto-verify when admin creates
      },
    });

    // Add to institution if provided
    if (institutionId) {
      await tx.institutionUser.create({
        data: {
          userId: newUser.id,
          institutionId,
          role: newUser.role,
        },
      });
    }

    // Add to school if provided
    if (schoolId) {
      // Validate that school belongs to institution
      if (institutionId) {
        const school = await tx.school.findUnique({
          where: { id: schoolId },
        });

        if (!school || school.institutionId !== institutionId) {
          throw new AppError('School does not belong to the provided institution', 400);
        }
      }

      await tx.schoolUser.create({
        data: {
          userId: newUser.id,
          schoolId,
          role: newUser.role,
        },
      });
    }

    return newUser;
  });

  // Create audit log
  await createAuditLog({
    userId: req.user.id,
    action: 'USER_CREATED',
    details: `User ${user.email} created by admin`,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'User',
    resourceId: user.id,
  });

  // Send welcome email
  try {
    await sendWelcomeEmail(user.email, user.firstName);
  } catch (error) {
    console.error('Failed to send welcome email:', error);
  }

  // Return response
  res.status(201).json({
    status: 'success',
    data: {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      },
    },
  });
});

/**
 * Update user
 */
exports.updateUser = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { email, firstName, lastName, role, phoneNumber, profileImageUrl, isActive } = req.body;

  // Check access permissions
  if (req.user.role !== 'SUPER_ADMIN' && req.user.id !== id) {
    // For non-super admins, check if they have permission to update this user
    const canUpdate = await checkUserUpdatePermission(req.user.id, id);
    if (!canUpdate) {
      return next(new AppError('You do not have permission to update this user', 403));
    }
  }

  // If changing email, check if it's already in use
  if (email) {
    const existingUser = await prisma.user.findFirst({
      where: {
        email,
        id: { not: id },
      },
    });

    if (existingUser) {
      return next(new AppError('Email is already in use by another user', 400));
    }
  }

  // Update user
  const user = await prisma.user.update({
    where: { id },
    data: {
      ...(email && { email }),
      ...(firstName && { firstName }),
      ...(lastName && { lastName }),
      ...(role && { role }),
      ...(phoneNumber !== undefined && { phoneNumber }),
      ...(profileImageUrl !== undefined && { profileImageUrl }),
      ...(isActive !== undefined && { isActive }),
    },
  });

  // Create audit log
  await createAuditLog({
    userId: req.user.id,
    action: 'USER_UPDATED',
    details: `User ${user.email} updated`,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'User',
    resourceId: user.id,
  });

  // Return response
  res.status(200).json({
    status: 'success',
    data: {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        phoneNumber: user.phoneNumber,
        profileImageUrl: user.profileImageUrl,
        isActive: user.isActive,
      },
    },
  });
});

/**
 * Delete user
 */
exports.deleteUser = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // Check if user exists
  const user = await prisma.user.findUnique({
    where: { id },
  });

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Check access permissions
  if (req.user.role !== 'SUPER_ADMIN') {
    // For non-super admins, check if they have permission to delete this user
    const canDelete = await checkUserDeletePermission(req.user.id, id);
    if (!canDelete) {
      return next(new AppError('You do not have permission to delete this user', 403));
    }
  }

  // Delete user
  await prisma.user.delete({
    where: { id },
  });

  // Create audit log
  await createAuditLog({
    userId: req.user.id,
    action: 'USER_DELETED',
    details: `User ${user.email} deleted`,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'User',
    resourceId: id,
  });

  // Return response
  res.status(200).json({
    status: 'success',
    message: 'User deleted successfully',
  });
});

/**
 * Add user to institution
 */
exports.addUserToInstitution = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { institutionId, role } = req.body;

  // Check if user exists
  const user = await prisma.user.findUnique({
    where: { id },
  });

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Check if institution exists
  const institution = await prisma.institution.findUnique({
    where: { id: institutionId },
  });

  if (!institution) {
    return next(new AppError('Institution not found', 404));
  }

  // Check if user is already in institution
  const existingInstitutionUser = await prisma.institutionUser.findUnique({
    where: {
      userId_institutionId: {
        userId: id,
        institutionId,
      },
    },
  });

  if (existingInstitutionUser) {
    // Update role if exists
    await prisma.institutionUser.update({
      where: {
        id: existingInstitutionUser.id,
      },
      data: {
        role,
        isActive: true,
      },
    });
  } else {
    // Add user to institution
    await prisma.institutionUser.create({
      data: {
        userId: id,
        institutionId,
        role,
      },
    });
  }

  // Create audit log
  await createAuditLog({
    userId: req.user.id,
    action: 'USER_ADDED_TO_INSTITUTION',
    details: `User ${user.email} added to institution ${institution.name}`,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'InstitutionUser',
    resourceId: `${id}:${institutionId}`,
  });

  // Return response
  res.status(200).json({
    status: 'success',
    message: 'User added to institution successfully',
  });
});

/**
 * Add user to school
 */
exports.addUserToSchool = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { schoolId, role } = req.body;

  // Check if user exists
  const user = await prisma.user.findUnique({
    where: { id },
  });

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Check if school exists
  const school = await prisma.school.findUnique({
    where: { id: schoolId },
    include: { institution: true },
  });

  if (!school) {
    return next(new AppError('School not found', 404));
  }

  // Check if user has access to the institution
  const institutionAccess = await prisma.institutionUser.findFirst({
    where: {
      userId: id,
      institutionId: school.institutionId,
    },
  });

  if (!institutionAccess && req.user.role !== 'SUPER_ADMIN') {
    // Add user to institution first
    await prisma.institutionUser.create({
      data: {
        userId: id,
        institutionId: school.institutionId,
        role,
      },
    });
  }

  // Check if user is already in school
  const existingSchoolUser = await prisma.schoolUser.findUnique({
    where: {
      userId_schoolId: {
        userId: id,
        schoolId,
      },
    },
  });

  if (existingSchoolUser) {
    // Update role if exists
    await prisma.schoolUser.update({
      where: {
        id: existingSchoolUser.id,
      },
      data: {
        role,
        isActive: true,
      },
    });
  } else {
    // Add user to school
    await prisma.schoolUser.create({
      data: {
        userId: id,
        schoolId,
        role,
      },
    });
  }

  // Create audit log
  await createAuditLog({
    userId: req.user.id,
    action: 'USER_ADDED_TO_SCHOOL',
    details: `User ${user.email} added to school ${school.name}`,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'SchoolUser',
    resourceId: `${id}:${schoolId}`,
  });

  // Return response
  res.status(200).json({
    status: 'success',
    message: 'User added to school successfully',
  });
});

/**
 * Get users by institution
 */
exports.getUsersByInstitution = catchAsync(async (req, res, next) => {
  const { institutionId } = req.params;
  const { page = 1, limit = 10, role, isActive } = req.query;

  // Check if institution exists
  const institution = await prisma.institution.findUnique({
    where: { id: institutionId },
  });

  if (!institution) {
    return next(new AppError('Institution not found', 404));
  }

  // Prepare filters
  const filters = { institutionId };
  if (role) filters.role = role;
  if (isActive !== undefined) filters.isActive = isActive === 'true';

  // Get total count for pagination
  const total = await prisma.institutionUser.count({ where: filters });

  // Get users
  const institutionUsers = await prisma.institutionUser.findMany({
    where: filters,
    include: {
      user: {
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          phoneNumber: true,
          profileImageUrl: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          isActive: true,
          createdAt: true,
        },
      },
    },
    orderBy: { createdAt: 'desc' },
    skip: (parseInt(page) - 1) * parseInt(limit),
    take: parseInt(limit),
  });

  // Map data for response
  const users = institutionUsers.map(iu => ({
    ...iu.user,
    institutionRole: iu.role,
    institutionIsActive: iu.isActive,
  }));

  // Return response
  res.status(200).json({
    status: 'success',
    results: users.length,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / parseInt(limit)),
    },
    data: { users },
  });
});

/**
 * Get users by school
 */
exports.getUsersBySchool = catchAsync(async (req, res, next) => {
  const { schoolId } = req.params;
  const { page = 1, limit = 10, role, isActive } = req.query;

  // Check if school exists
  const school = await prisma.school.findUnique({
    where: { id: schoolId },
  });

  if (!school) {
    return next(new AppError('School not found', 404));
  }

  // Prepare filters
  const filters = { schoolId };
  if (role) filters.role = role;
  if (isActive !== undefined) filters.isActive = isActive === 'true';

  // Get total count for pagination
  const total = await prisma.schoolUser.count({ where: filters });

  // Get users
  const schoolUsers = await prisma.schoolUser.findMany({
    where: filters,
    include: {
      user: {
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          phoneNumber: true,
          profileImageUrl: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          isActive: true,
          createdAt: true,
        },
      },
    },
    orderBy: { createdAt: 'desc' },
    skip: (parseInt(page) - 1) * parseInt(limit),
    take: parseInt(limit),
  });

  // Map data for response
  const users = schoolUsers.map(su => ({
    ...su.user,
    schoolRole: su.role,
    schoolIsActive: su.isActive,
  }));

  // Return response
  res.status(200).json({
    status: 'success',
    results: users.length,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / parseInt(limit)),
    },
    data: { users },
  });
});

/**
 * Remove user from institution
 */
exports.removeUserFromInstitution = catchAsync(async (req, res, next) => {
  const { id, institutionId } = req.params;

  // Check if user exists
  const user = await prisma.user.findUnique({
    where: { id },
  });

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Check if institution exists
  const institution = await prisma.institution.findUnique({
    where: { id: institutionId },
  });

  if (!institution) {
    return next(new AppError('Institution not found', 404));
  }

  // Check if user is in institution
  const institutionUser = await prisma.institutionUser.findUnique({
    where: {
      userId_institutionId: {
        userId: id,
        institutionId,
      },
    },
  });

  if (!institutionUser) {
    return next(new AppError('User is not part of this institution', 404));
  }

  // Remove user from all schools in the institution
  await prisma.schoolUser.deleteMany({
    where: {
      userId: id,
      school: {
        institutionId,
      },
    },
  });

  // Remove user from institution
  await prisma.institutionUser.delete({
    where: {
      userId_institutionId: {
        userId: id,
        institutionId,
      },
    },
  });

  // Create audit log
  await createAuditLog({
    userId: req.user.id,
    action: 'USER_REMOVED_FROM_INSTITUTION',
    details: `User ${user.email} removed from institution ${institution.name}`,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'InstitutionUser',
    resourceId: `${id}:${institutionId}`,
  });

  // Return response
  res.status(200).json({
    status: 'success',
    message: 'User removed from institution successfully',
  });
});

/**
 * Remove user from school
 */
exports.removeUserFromSchool = catchAsync(async (req, res, next) => {
  const { id, schoolId } = req.params;

  // Check if user exists
  const user = await prisma.user.findUnique({
    where: { id },
  });

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Check if school exists
  const school = await prisma.school.findUnique({
    where: { id: schoolId },
  });

  if (!school) {
    return next(new AppError('School not found', 404));
  }

  // Check if user is in school
  const schoolUser = await prisma.schoolUser.findUnique({
    where: {
      userId_schoolId: {
        userId: id,
        schoolId,
      },
    },
  });

  if (!schoolUser) {
    return next(new AppError('User is not part of this school', 404));
  }

  // Remove user from school
  await prisma.schoolUser.delete({
    where: {
      userId_schoolId: {
        userId: id,
        schoolId,
      },
    },
  });

  // Create audit log
  await createAuditLog({
    userId: req.user.id,
    action: 'USER_REMOVED_FROM_SCHOOL',
    details: `User ${user.email} removed from school ${school.name}`,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'SchoolUser',
    resourceId: `${id}:${schoolId}`,
  });

  // Return response
  res.status(200).json({
    status: 'success',
    message: 'User removed from school successfully',
  });
});

/**
 * Helper function to check if a user has permission to update another user
 */
async function checkUserUpdatePermission(currentUserId, targetUserId) {
  // Get current user's institutions and schools
  const userInstitutions = await prisma.institutionUser.findMany({
    where: { userId: currentUserId },
    select: { institutionId: true, role: true },
  });

  const userSchools = await prisma.schoolUser.findMany({
    where: { userId: currentUserId },
    select: { schoolId: true, role: true },
  });

  // Get target user's institutions and schools
  const targetUserInstitutions = await prisma.institutionUser.findMany({
    where: { userId: targetUserId },
    select: { institutionId: true },
  });

  const targetUserSchools = await prisma.schoolUser.findMany({
    where: { userId: targetUserId },
    select: { schoolId: true },
  });

  // Check for shared institutions where current user is an admin
  const sharedInstitution = userInstitutions.some(ui => 
    ui.role === 'INSTITUTION_ADMIN' && 
    targetUserInstitutions.some(tui => tui.institutionId === ui.institutionId)
  );

  if (sharedInstitution) return true;

  // Check for shared schools where current user is an admin
  const sharedSchool = userSchools.some(us => 
    us.role === 'SCHOOL_ADMIN' && 
    targetUserSchools.some(tus => tus.schoolId === us.schoolId)
  );

  return sharedSchool;
}

/**
 * Helper function to check if a user has permission to delete another user
 */
async function checkUserDeletePermission(currentUserId, targetUserId) {
  return await checkUserUpdatePermission(currentUserId, targetUserId);
}
