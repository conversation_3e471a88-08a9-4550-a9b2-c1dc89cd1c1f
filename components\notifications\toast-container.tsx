"use client"

import { useEffect, useState } from "react"
import { createPortal } from "react-dom"
import { NotificationToast } from "@/components/notifications/notification-toast"
import { useNotifications, type Notification } from "@/contexts/notification-context"

export function ToastContainer() {
  const { notifications, markAsRead, removeNotification } = useNotifications()
  const [activeToasts, setActiveToasts] = useState<Notification[]>([])
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    return () => setIsMounted(false)
  }, [])

  // Show new unread notifications as toasts
  useEffect(() => {
    const newNotifications = notifications
      .filter((notification) => !notification.read)
      .filter((notification) => !activeToasts.some((toast) => toast.id === notification.id))
      .slice(0, 3) // Limit to 3 new toasts at a time

    if (newNotifications.length > 0) {
      setActiveToasts((prev) => [...prev, ...newNotifications])
    }
  }, [notifications, activeToasts])

  const handleCloseToast = (id: string) => {
    markAsRead(id)
    setActiveToasts((prev) => prev.filter((toast) => toast.id !== id))
  }

  if (!isMounted) return null

  return createPortal(
    <div className="fixed bottom-0 right-0 z-50 m-4 flex flex-col gap-2">
      {activeToasts.map((notification) => (
        <NotificationToast
          key={notification.id}
          notification={notification}
          onClose={() => handleCloseToast(notification.id)}
        />
      ))}
    </div>,
    document.body,
  )
}
