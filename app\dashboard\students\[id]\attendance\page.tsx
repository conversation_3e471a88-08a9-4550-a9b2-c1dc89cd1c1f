import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { FileText, ArrowLeft, CalendarIcon, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import Link from "next/link"

export default function StudentAttendancePage({ params }: { params: { id: string } }) {
  const studentId = params.id

  // This would normally come from a database
  const student = {
    id: studentId,
    name: "<PERSON>",
    grade: "10th Grade",
    section: "Section A",
    rollNumber: "2023-10-042",
    attendanceRate: "94%",
    presentDays: 85,
    absentDays: 5,
    lateDays: 3,
    totalSchoolDays: 93,
    currentMonth: "October 2023",
  }

  // Sample attendance data
  const monthlyAttendance = [
    { date: "Oct 1, 2023", day: "Monday", status: "Present", arrivalTime: "7:45 AM", departureTime: "3:15 PM" },
    { date: "Oct 2, 2023", day: "Tuesday", status: "Present", arrivalTime: "7:50 AM", departureTime: "3:15 PM" },
    {
      date: "Oct 3, 2023",
      day: "Wednesday",
      status: "Late",
      arrivalTime: "8:20 AM",
      departureTime: "3:15 PM",
      notes: "Bus delay",
    },
    { date: "Oct 4, 2023", day: "Thursday", status: "Present", arrivalTime: "7:55 AM", departureTime: "3:15 PM" },
    { date: "Oct 5, 2023", day: "Friday", status: "Present", arrivalTime: "7:45 AM", departureTime: "3:15 PM" },
    { date: "Oct 8, 2023", day: "Monday", status: "Absent", notes: "Medical appointment" },
    { date: "Oct 9, 2023", day: "Tuesday", status: "Present", arrivalTime: "7:50 AM", departureTime: "3:15 PM" },
    { date: "Oct 10, 2023", day: "Wednesday", status: "Present", arrivalTime: "7:45 AM", departureTime: "3:15 PM" },
    { date: "Oct 11, 2023", day: "Thursday", status: "Present", arrivalTime: "7:55 AM", departureTime: "3:15 PM" },
    { date: "Oct 12, 2023", day: "Friday", status: "Present", arrivalTime: "7:45 AM", departureTime: "3:15 PM" },
  ]

  // Sample attendance by subject
  const subjectAttendance = [
    { subject: "Mathematics", present: 20, absent: 1, late: 1, attendanceRate: "95%" },
    { subject: "Science", present: 19, absent: 2, late: 1, attendanceRate: "90%" },
    { subject: "English", present: 22, absent: 0, late: 0, attendanceRate: "100%" },
    { subject: "History", present: 18, absent: 2, late: 2, attendanceRate: "86%" },
    { subject: "Physical Education", present: 20, absent: 0, late: 2, attendanceRate: "95%" },
    { subject: "Art", present: 10, absent: 0, late: 0, attendanceRate: "100%" },
  ]

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <Link href={`/dashboard/students/${studentId}`}>
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Back to student profile</span>
              </Link>
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">Attendance Record</h1>
          </div>
          <p className="text-muted-foreground ml-10">
            Attendance details for {student.name} ({student.grade}, {student.section})
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <CalendarIcon className="mr-2 h-4 w-4" />
            Select Month
          </Button>
          <Button variant="outline">
            <FileText className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Attendance Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{student.attendanceRate}</div>
            <p className="text-xs text-muted-foreground">Academic Year 2023-24</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Present Days</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-500">{student.presentDays}</div>
            <p className="text-xs text-muted-foreground">Out of {student.totalSchoolDays} school days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Absent Days</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-red-500">{student.absentDays}</div>
            <p className="text-xs text-muted-foreground">Out of {student.totalSchoolDays} school days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Late Arrivals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-amber-500">{student.lateDays}</div>
            <p className="text-xs text-muted-foreground">Out of {student.totalSchoolDays} school days</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="monthly" className="space-y-4">
        <TabsList>
          <TabsTrigger value="monthly">Monthly View</TabsTrigger>
          <TabsTrigger value="subjects">By Subject</TabsTrigger>
          <TabsTrigger value="trends">Attendance Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="monthly" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Attendance - {student.currentMonth}</CardTitle>
              <CardDescription>Daily attendance record for the current month</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-6 bg-muted/50 p-3 text-sm font-medium">
                  <div>Date</div>
                  <div>Day</div>
                  <div>Status</div>
                  <div>Arrival</div>
                  <div>Departure</div>
                  <div>Notes</div>
                </div>
                <div className="divide-y">
                  {monthlyAttendance.map((record, index) => (
                    <div key={index} className="grid grid-cols-6 p-3 text-sm">
                      <div>{record.date}</div>
                      <div>{record.day}</div>
                      <div>
                        <span
                          className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                            record.status === "Present"
                              ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                              : record.status === "Absent"
                                ? "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
                                : "bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400"
                          }`}
                        >
                          {record.status === "Present" ? (
                            <CheckCircle className="mr-1 h-3 w-3" />
                          ) : record.status === "Absent" ? (
                            <XCircle className="mr-1 h-3 w-3" />
                          ) : (
                            <AlertCircle className="mr-1 h-3 w-3" />
                          )}
                          {record.status}
                        </span>
                      </div>
                      <div>{record.arrivalTime || "-"}</div>
                      <div>{record.departureTime || "-"}</div>
                      <div>{record.notes || "-"}</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Previous Month</Button>
              <Button variant="outline">Next Month</Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Calendar View</CardTitle>
              <CardDescription>Monthly calendar showing attendance status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-7 gap-2 text-center">
                <div className="text-sm font-medium">Sun</div>
                <div className="text-sm font-medium">Mon</div>
                <div className="text-sm font-medium">Tue</div>
                <div className="text-sm font-medium">Wed</div>
                <div className="text-sm font-medium">Thu</div>
                <div className="text-sm font-medium">Fri</div>
                <div className="text-sm font-medium">Sat</div>

                {/* Empty cells for days before the month starts */}
                <div></div>

                {/* Calendar days */}
                {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => {
                  // Determine status based on the day
                  let status = "none"
                  if (day <= 12) {
                    if (day === 3) status = "late"
                    else if (day === 8) status = "absent"
                    else if (day <= 12) status = "present"
                  }

                  // Skip days after the current date
                  if (day > 12) return <div key={day}></div>

                  return (
                    <div
                      key={day}
                      className={`aspect-square flex items-center justify-center rounded-md border text-sm ${
                        status === "present"
                          ? "bg-green-100 border-green-200 dark:bg-green-900/30 dark:border-green-800"
                          : status === "absent"
                            ? "bg-red-100 border-red-200 dark:bg-red-900/30 dark:border-red-800"
                            : status === "late"
                              ? "bg-amber-100 border-amber-200 dark:bg-amber-900/30 dark:border-amber-800"
                              : ""
                      }`}
                    >
                      {day}
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subjects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Attendance by Subject</CardTitle>
              <CardDescription>Subject-wise attendance breakdown</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-5 bg-muted/50 p-3 text-sm font-medium">
                  <div>Subject</div>
                  <div>Present</div>
                  <div>Absent</div>
                  <div>Late</div>
                  <div>Attendance Rate</div>
                </div>
                <div className="divide-y">
                  {subjectAttendance.map((subject, index) => (
                    <div key={index} className="grid grid-cols-5 p-3 text-sm">
                      <div className="font-medium">{subject.subject}</div>
                      <div className="text-green-600 dark:text-green-400">{subject.present}</div>
                      <div className="text-red-600 dark:text-red-400">{subject.absent}</div>
                      <div className="text-amber-600 dark:text-amber-400">{subject.late}</div>
                      <div>
                        <div className="flex items-center gap-2">
                          <div className="w-full max-w-24 bg-muted rounded-full h-2">
                            <div
                              className="bg-primary h-2 rounded-full"
                              style={{ width: subject.attendanceRate }}
                            ></div>
                          </div>
                          <span>{subject.attendanceRate}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Attendance Trends</CardTitle>
              <CardDescription>Monthly attendance patterns for the academic year</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80 w-full">
                {/* This would be a chart in a real implementation */}
                <div className="h-full w-full flex flex-col">
                  <div className="flex-1 grid grid-cols-9 gap-2 items-end pb-4">
                    {[90, 92, 95, 88, 94, 96, 93, 94, 92].map((value, index) => (
                      <div key={index} className="relative w-full">
                        <div className="w-full bg-primary rounded-t-sm" style={{ height: `${value}%` }}></div>
                      </div>
                    ))}
                  </div>
                  <div className="grid grid-cols-9 gap-2 text-xs text-center text-muted-foreground">
                    <div>Sep</div>
                    <div>Oct</div>
                    <div>Nov</div>
                    <div>Dec</div>
                    <div>Jan</div>
                    <div>Feb</div>
                    <div>Mar</div>
                    <div>Apr</div>
                    <div>May</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Attendance by Day of Week</CardTitle>
                <CardDescription>Patterns based on weekdays</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { day: "Monday", rate: "96%" },
                    { day: "Tuesday", rate: "98%" },
                    { day: "Wednesday", rate: "92%" },
                    { day: "Thursday", rate: "95%" },
                    { day: "Friday", rate: "90%" },
                  ].map((item, index) => (
                    <div key={index}>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">{item.day}</span>
                        <span className="text-sm font-medium">{item.rate}</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div className="bg-primary h-2 rounded-full" style={{ width: item.rate }}></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Attendance Summary</CardTitle>
                <CardDescription>Year-to-date statistics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">School Days</p>
                      <p className="text-2xl font-bold">{student.totalSchoolDays}</p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Present Days</p>
                      <p className="text-2xl font-bold text-green-500">{student.presentDays}</p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Absent Days</p>
                      <p className="text-2xl font-bold text-red-500">{student.absentDays}</p>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Late Arrivals</p>
                      <p className="text-2xl font-bold text-amber-500">{student.lateDays}</p>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <p className="text-sm font-medium mb-2">Overall Attendance</p>
                    <div className="w-full bg-muted rounded-full h-4">
                      <div
                        className="bg-primary h-4 rounded-full text-xs text-primary-foreground flex items-center justify-center"
                        style={{ width: student.attendanceRate }}
                      >
                        {student.attendanceRate}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
