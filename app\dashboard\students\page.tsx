"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Plus, Search, MoreHorizontal, Download, X, Loader2 } from "lucide-react"
import { backendApi } from "@/lib/backend-api"
import { useToast } from "@/hooks/use-toast"

interface Student {
  id: string
  admissionNumber: string
  firstName: string
  lastName: string
  gender: string
  dateOfBirth: string
  email?: string
  phoneNumber?: string
  status: string
  currentClass?: {
    id: string
    name: string
  }
  class?: {
    id: string
    name: string
  }
  enrollments?: Array<{
    id: string
    class: {
      id: string
      name: string
    }
  }>
  parents?: Array<{
    relationship?: string
    isPrimary?: boolean
    parent?: {
      name: string
      email?: string
      phoneNumber?: string
    }
    // Legacy format support
    id?: string
    firstName?: string
    lastName?: string
    phoneNumber?: string
  }>
}

export default function StudentsPage() {
  const [students, setStudents] = useState<Student[]>([])
  const [classes, setClasses] = useState<Array<{id: string; name: string}>>([])
  const [academicYears, setAcademicYears] = useState<Array<{id: string; name: string; isActive: boolean}>>([])
  const [currentAcademicYear, setCurrentAcademicYear] = useState<string>("")
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [classFilter, setClassFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [newStudent, setNewStudent] = useState({
    firstName: "",
    lastName: "",
    admissionNumber: "",
    gender: "",
    dateOfBirth: "",
    classId: "",
    parentName: "",
    contactNumber: "",
    email: "",
  })
  const { toast } = useToast()

  useEffect(() => {
    fetchStudents()
    fetchClasses()
    fetchAcademicYears()
  }, [currentPage, searchQuery, classFilter, statusFilter])

  const fetchStudents = async () => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(searchQuery && { search: searchQuery }),
        ...(classFilter && classFilter !== "all" && { classId: classFilter }),
        ...(statusFilter && statusFilter !== "all" && { status: statusFilter }),
      })

      const response = await backendApi.student.getStudents(params.toString())

      // Add null checks for response data
      if (response && response.data) {
        // Backend returns data in response.data.students format
        const studentsData = response.data.students || response.data
        setStudents(Array.isArray(studentsData) ? studentsData : [])

        // Handle pagination data
        if (response.data && typeof response.data === 'object' && 'pagination' in response.data) {
          const paginationData = response.data.pagination as any
          if (paginationData) {
            setTotalPages(Math.ceil(paginationData.totalStudents / paginationData.limit))
          }
        }
      } else {
        setStudents([])
        setTotalPages(1)
      }
    } catch (error) {
      console.error("Error fetching students:", error)
      setStudents([])
      setTotalPages(1)
      toast({
        title: "Error",
        description: "Failed to fetch students. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const fetchClasses = async () => {
    try {
      const response = await backendApi.class.getClasses()

      // Add null checks for response data
      if (response && response.data && Array.isArray(response.data)) {
        setClasses(response.data)

        // Extract academic year from classes if available
        if (response.data.length > 0) {
          const firstClass = response.data[0]
          if (firstClass && firstClass.academicYear && firstClass.academicYear.id) {
            setCurrentAcademicYear(firstClass.academicYear.id)
          } else if (firstClass && firstClass.academicYearId) {
            setCurrentAcademicYear(firstClass.academicYearId)
          }
        }
      } else {
        setClasses([])
      }
    } catch (error) {
      console.error("Failed to fetch classes:", error)
      setClasses([])
    }
  }

  const fetchAcademicYears = async () => {
    // Academic year is now fetched in fetchClasses, so this is just a fallback
    if (!currentAcademicYear) {
      console.warn("No academic year found from classes, using fallback")
      const currentYear = new Date().getFullYear()
      setCurrentAcademicYear(`default-${currentYear}-${currentYear + 1}`)
    }
  }

  const filteredStudents = students || []

  const handleAddStudent = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate required fields
    if (!currentAcademicYear) {
      toast({
        title: "Error",
        description: "No academic year available. Please contact administrator.",
        variant: "destructive",
      })
      return
    }

    try {
      const studentData = {
        firstName: newStudent.firstName,
        lastName: newStudent.lastName,
        admissionNumber: newStudent.admissionNumber,
        gender: newStudent.gender,
        dateOfBirth: newStudent.dateOfBirth,
        email: newStudent.email || undefined, // Don't send empty string
        phoneNumber: newStudent.contactNumber || undefined, // Backend expects phoneNumber
        classId: newStudent.classId,
        academicYearId: currentAcademicYear, // Required field
        parents: newStudent.parentName ? [{
          firstName: newStudent.parentName.split(' ')[0] || '',
          lastName: newStudent.parentName.split(' ').slice(1).join(' ') || '',
          phoneNumber: newStudent.contactNumber,
        }] : [],
      }

      await backendApi.student.createStudent(studentData)
      
      toast({
        title: "Success",
        description: "Student added successfully.",
      })
      
      setShowAddDialog(false)
      resetForm()
      fetchStudents()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to add student. Please try again.",
        variant: "destructive",
      })
    }
  }

  const resetForm = () => {
    setNewStudent({
      firstName: "",
      lastName: "",
      admissionNumber: "",
      gender: "",
      dateOfBirth: "",
      classId: "",
      parentName: "",
      contactNumber: "",
      email: "",
    })
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setNewStudent((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setNewStudent((prev) => ({ ...prev, [name]: value }))
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Students</h1>
          <p className="text-gray-500">Manage student records and information</p>
        </div>
        <div className="flex gap-2">
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Student
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Add New Student</DialogTitle>
                <DialogDescription>Enter the student details below to register a new student.</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleAddStudent}>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First Name</Label>
                      <Input
                        id="firstName"
                        name="firstName"
                        value={newStudent.firstName}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input
                        id="lastName"
                        name="lastName"
                        value={newStudent.lastName}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="admissionNumber">Admission Number</Label>
                      <Input
                        id="admissionNumber"
                        name="admissionNumber"
                        value={newStudent.admissionNumber}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gender">Gender</Label>
                      <Select onValueChange={(value) => handleSelectChange("gender", value)} value={newStudent.gender}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select gender" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="MALE">Male</SelectItem>
                          <SelectItem value="FEMALE">Female</SelectItem>
                          <SelectItem value="OTHER">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="dateOfBirth">Date of Birth</Label>
                      <Input
                        id="dateOfBirth"
                        name="dateOfBirth"
                        type="date"
                        value={newStudent.dateOfBirth}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={newStudent.email}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="classId">Class</Label>
                      <Select onValueChange={(value) => handleSelectChange("classId", value)} value={newStudent.classId}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select class" />
                        </SelectTrigger>
                        <SelectContent>
                          {classes.map((classItem) => (
                            <SelectItem key={classItem.id} value={classItem.id}>
                              {classItem.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="parentName">Parent/Guardian Name</Label>
                      <Input
                        id="parentName"
                        name="parentName"
                        value={newStudent.parentName}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactNumber">Contact Number</Label>
                    <Input
                      id="contactNumber"
                      name="contactNumber"
                      value={newStudent.contactNumber}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setShowAddDialog(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Add Student</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>

          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Student Records</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search by name or admission number..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select onValueChange={setClassFilter} value={classFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by class" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Classes</SelectItem>
                  {classes.map((classItem) => (
                    <SelectItem key={classItem.id} value={classItem.id}>
                      {classItem.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select onValueChange={setStatusFilter} value={statusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="INACTIVE">Inactive</SelectItem>
                  <SelectItem value="GRADUATED">Graduated</SelectItem>
                  <SelectItem value="TRANSFERRED">Transferred</SelectItem>
                </SelectContent>
              </Select>

              {(classFilter && classFilter !== "all" || statusFilter && statusFilter !== "all") && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    setClassFilter("all")
                    setStatusFilter("all")
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading students...</span>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Admission #</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Gender</TableHead>
                      <TableHead>Class</TableHead>
                      <TableHead>Parent/Guardian</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredStudents.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center h-24">
                          No students found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredStudents.map((student) => (
                        <TableRow key={student.id}>
                          <TableCell>{student.admissionNumber}</TableCell>
                          <TableCell>
                            <Link
                              href={`/dashboard/students/${student.id}`}
                              className="font-medium text-emerald-600 hover:underline"
                            >
                              {student.firstName} {student.lastName}
                            </Link>
                          </TableCell>
                          <TableCell>
                            {student.gender === "MALE" ? "Male" : student.gender === "FEMALE" ? "Female" : "Other"}
                          </TableCell>
                          <TableCell>
                            {student.currentClass?.name || student.class?.name || student.enrollments?.[0]?.class?.name || "No Class"}
                          </TableCell>
                          <TableCell>
                            {student.parents && Array.isArray(student.parents) && student.parents.length > 0
                              ? (student.parents[0].parent?.name ||
                                 `${student.parents[0].firstName || ''} ${student.parents[0].lastName || ''}`.trim() ||
                                 "No Name")
                              : "No Parent"
                            }
                          </TableCell>
                          <TableCell>
                            {student.parents && Array.isArray(student.parents) && student.parents.length > 0
                              ? (student.parents[0].parent?.phoneNumber || student.parents[0].phoneNumber || "No Contact")
                              : student.phoneNumber || "No Contact"
                            }
                          </TableCell>
                          <TableCell>
                            <Badge variant={student.status === "ACTIVE" ? "default" : "secondary"}>
                              {student.status.toLowerCase().charAt(0).toUpperCase() + student.status.toLowerCase().slice(1)}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Actions</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Link href={`/dashboard/students/${student.id}`} className="flex w-full">
                                    View Details
                                  </Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem>Edit</DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Link href={`/dashboard/students/${student.id}/attendance`} className="flex w-full">
                                    Attendance
                                  </Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Link href={`/dashboard/students/${student.id}/grades`} className="flex w-full">
                                    Grades
                                  </Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Link href={`/dashboard/students/${student.id}/fees`} className="flex w-full">
                                    Fee Details
                                  </Link>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              <div className="mt-4 flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  Showing {(students && students.length > 0) ? ((currentPage - 1) * 10) + 1 : 0} to {Math.min(currentPage * 10, students?.length || 0)} of {students?.length || 0} students
                </div>
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious 
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                    {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                      const pageNum = i + 1
                      return (
                        <PaginationItem key={pageNum}>
                          <PaginationLink 
                            onClick={() => setCurrentPage(pageNum)}
                            isActive={currentPage === pageNum}
                            className="cursor-pointer"
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      )
                    })}
                    <PaginationItem>
                      <PaginationNext 
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
