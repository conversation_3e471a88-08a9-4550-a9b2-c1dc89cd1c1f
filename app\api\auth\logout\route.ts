import { NextResponse } from "next/server"
import { cookies } from "next/headers"

export async function POST(request: Request) {
  try {
    const cookieStore = await cookies()
    const accessToken = cookieStore.get("accessToken")?.value

    // Call backend API for logout if we have an access token
    if (accessToken) {
      try {
        const response = await fetch(`${process.env.BACKEND_URL}/api/v1/auth/logout`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
          // Add timeout to prevent hanging
          signal: AbortSignal.timeout(5000),
        })

        if (!response.ok) {
          console.warn("Backend logout failed, but continuing with local cleanup")
        }
      } catch (error) {
        console.warn("Backend logout request failed:", error)
        // Continue with local cleanup even if backend fails
      }
    }

    // Clear all authentication-related cookies
    const cookiesToClear = ["session", "accessToken", "refreshToken"]

    cookiesToClear.forEach(cookieName => {
      cookieStore.set(cookieName, "", {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 0, // Expire immediately
        path: "/",
      })
    })

    return NextResponse.json({
      success: true,
      message: "Logged out successfully"
    })
  } catch (error) {
    console.error("Logout error:", error)

    // Even if there's an error, try to clear cookies
    try {
      const cookieStore = await cookies()
      const cookiesToClear = ["session", "accessToken", "refreshToken"]

      cookiesToClear.forEach(cookieName => {
        cookieStore.set(cookieName, "", {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax",
          maxAge: 0,
          path: "/",
        })
      })
    } catch (cookieError) {
      console.error("Failed to clear cookies:", cookieError)
    }

    return NextResponse.json(
      { success: false, message: "An unexpected error occurred during logout" },
      { status: 500 }
    )
  }
}
