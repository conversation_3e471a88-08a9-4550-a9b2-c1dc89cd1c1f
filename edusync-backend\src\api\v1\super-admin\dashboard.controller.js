const { PrismaClient } = require('@prisma/client');
const asyncHandler = require('../../../utils/asyncHandler');

const prisma = new PrismaClient();

/**
 * Get super admin dashboard statistics
 */
const getDashboardStats = asyncHandler(async (req, res) => {
  // Get total institutions
  const totalInstitutions = await prisma.institution.count();
  
  // Get active institutions (verified)
  const activeInstitutions = await prisma.institution.count({
    where: { verificationStatus: true }
  });

  // Get pending institutions (not verified)
  const pendingApprovals = await prisma.institution.count({
    where: { verificationStatus: false }
  });

  // Get total users
  const totalUsers = await prisma.user.count();

  // Get total revenue (mock data for now - would come from subscription system)
  const totalRevenue = 125000; // This would come from actual billing system

  // Get system uptime (mock - would come from monitoring system)
  const systemUptime = 99.8;

  // Get recent activities (last 30 days)
  const recentActivities = await prisma.auditLog.findMany({
    take: 10,
    orderBy: { createdAt: 'desc' },
    include: {
      user: {
        select: { firstName: true, lastName: true, email: true }
      }
    }
  });

  // Get institution growth data (last 12 months)
  const institutionGrowth = await prisma.institution.groupBy({
    by: ['createdAt'],
    _count: true,
    orderBy: { createdAt: 'asc' },
    where: {
      createdAt: {
        gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) // Last year
      }
    }
  });

  // Get subscription distribution
  const subscriptionDistribution = [
    { name: 'Free', value: 45, color: '#8884d8' },
    { name: 'Basic', value: 30, color: '#82ca9d' },
    { name: 'Premium', value: 20, color: '#ffc658' },
    { name: 'Enterprise', value: 5, color: '#ff7300' }
  ];

  res.json({
    success: true,
    data: {
      stats: {
        totalInstitutions,
        activeInstitutions,
        pendingApprovals,
        totalUsers,
        totalRevenue,
        systemUptime
      },
      recentActivities: recentActivities.map(activity => ({
        id: activity.id,
        action: activity.action,
        details: activity.details,
        user: activity.user ? `${activity.user.firstName} ${activity.user.lastName}` : 'System',
        timestamp: activity.createdAt,
        ipAddress: activity.ipAddress
      })),
      institutionGrowth: institutionGrowth.map(item => ({
        month: item.createdAt.toISOString().slice(0, 7), // YYYY-MM format
        count: item._count
      })),
      subscriptionDistribution
    }
  });
});

/**
 * Get pending approvals
 */
const getPendingApprovals = asyncHandler(async (req, res) => {
  const pendingInstitutions = await prisma.institution.findMany({
    where: { verificationStatus: false },
    include: {
      users: {
        where: { role: 'INSTITUTION_ADMIN' },
        select: { firstName: true, lastName: true, email: true }
      }
    },
    orderBy: { createdAt: 'desc' }
  });

  res.json({
    success: true,
    data: pendingInstitutions.map(institution => ({
      id: institution.id,
      name: institution.name,
      type: institution.type || 'School',
      adminContact: institution.users[0] ? `${institution.users[0].firstName} ${institution.users[0].lastName}` : 'No admin assigned',
      adminEmail: institution.users[0]?.email || '',
      submittedDate: institution.createdAt,
      address: institution.address,
      city: institution.city,
      country: institution.country
    }))
  });
});

/**
 * Approve institution
 */
const approveInstitution = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const institution = await prisma.institution.findUnique({
    where: { id },
    include: {
      users: {
        where: { role: 'INSTITUTION_ADMIN' }
      }
    }
  });

  if (!institution) {
    return res.status(404).json({
      success: false,
      error: 'Institution not found'
    });
  }

  // Update institution status
  const updatedInstitution = await prisma.institution.update({
    where: { id },
    data: {
      verificationStatus: true
    }
  });

  // Log the approval action
  await prisma.auditLog.create({
    data: {
      userId: req.user.id,
      action: 'INSTITUTION_APPROVED',
      details: `Institution "${institution.name}" has been approved`,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    }
  });

  res.json({
    success: true,
    message: 'Institution approved successfully',
    data: updatedInstitution
  });
});

/**
 * Reject institution
 */
const rejectInstitution = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { reason } = req.body;

  const institution = await prisma.institution.findUnique({
    where: { id }
  });

  if (!institution) {
    return res.status(404).json({
      success: false,
      error: 'Institution not found'
    });
  }

  // Update institution with rejection reason
  await prisma.institution.update({
    where: { id },
    data: {
      verificationStatus: false
    }
  });

  // Log the rejection action
  await prisma.auditLog.create({
    data: {
      userId: req.user.id,
      action: 'INSTITUTION_REJECTED',
      details: `Institution "${institution.name}" has been rejected. Reason: ${reason}`,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    }
  });

  res.json({
    success: true,
    message: 'Institution rejected successfully'
  });
});

module.exports = {
  getDashboardStats,
  getPendingApprovals,
  approveInstitution,
  rejectInstitution
};
