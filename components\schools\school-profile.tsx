import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card } from "@/components/ui/card"
import { SchoolProfileHeader } from "./school-profile-header"
import { SchoolInformationForm } from "./school-information-form"
import { SchoolSettings } from "./school-settings"
import { SchoolStatistics } from "./school-statistics"
import { Separator } from "@/components/ui/separator"

export function SchoolProfile() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold tracking-tight">School Profile</h2>
          <p className="text-sm text-muted-foreground">
            Manage your school information, settings, and view statistics
          </p>
        </div>
      </div>
      <Separator />
      <Tabs defaultValue="information" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 lg:w-[400px]">
          <TabsTrigger value="information">Information</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="statistics">Statistics</TabsTrigger>
        </TabsList>
        <TabsContent value="information" className="space-y-6">
          <Card className="p-6 border-primary transition-all duration-200 hover:shadow-md">
            <SchoolProfileHeader />
          </Card>
          <Card className="p-6 border-primary transition-all duration-200 hover:shadow-md">
            <SchoolInformationForm />
          </Card>
        </TabsContent>
        <TabsContent value="settings">
          <Card className="p-6 border-primary transition-all duration-200 hover:shadow-md">
            <SchoolSettings />
          </Card>
        </TabsContent>
        <TabsContent value="statistics">
          <Card className="p-6 border-primary transition-all duration-200 hover:shadow-md">
            <SchoolStatistics />
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 