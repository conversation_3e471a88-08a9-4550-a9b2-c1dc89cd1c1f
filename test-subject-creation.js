const https = require('https');
const http = require('http');

// Simple fetch implementation using Node.js built-in modules
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

// Test subject creation with proper authentication
async function testSubjectCreation() {
  try {
    // First, login to get a token
    console.log('🔐 Logging in...');
    const loginResponse = await fetch('http://localhost:4000/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123'
      })
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status}`);
    }

    const loginData = await loginResponse.json();
    const token = loginData.data.accessToken;
    console.log('✅ Login successful');

    // Now test creating a subject
    console.log('📚 Creating test subject...');
    const subjectData = {
      name: 'Test Mathematics',
      code: 'MATH101',
      description: 'Basic mathematics course',
      credits: 3,
      isCore: true,
      departmentId: null // No department for now
    };

    const createResponse = await fetch('http://localhost:4000/api/v1/subjects', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(subjectData)
    });

    const createData = await createResponse.json();
    
    if (createResponse.ok) {
      console.log('✅ Subject created successfully:');
      console.log(JSON.stringify(createData, null, 2));
    } else {
      console.log('❌ Subject creation failed:');
      console.log(JSON.stringify(createData, null, 2));
    }

    // Test getting subjects
    console.log('📋 Fetching subjects...');
    const getResponse = await fetch('http://localhost:4000/api/v1/subjects', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    const getData = await getResponse.json();
    
    if (getResponse.ok) {
      console.log('✅ Subjects fetched successfully:');
      console.log(`Found ${getData.data.subjects.length} subjects`);
      getData.data.subjects.forEach(subject => {
        console.log(`- ${subject.name} (${subject.code}) - School: ${subject.schoolId}`);
      });
    } else {
      console.log('❌ Failed to fetch subjects:');
      console.log(JSON.stringify(getData, null, 2));
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSubjectCreation();
