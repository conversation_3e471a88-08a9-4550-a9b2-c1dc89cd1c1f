"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ProtectedPage } from "@/components/auth/protected-page"

function DebugAuthContent() {
  const [authState, setAuthState] = useState<any>({})
  const [loading, setLoading] = useState(false)

  const checkAuthState = async () => {
    setLoading(true)
    try {
      // Check cookies
      const cookies = document.cookie
      console.log("Document cookies:", cookies)

      // Test auth endpoint
      const authResponse = await fetch('/api/auth', {
        credentials: 'include'
      })
      const authData = authResponse.ok ? await authResponse.json() : { error: 'Failed to fetch' }

      // Test session endpoint
      const sessionResponse = await fetch('/api/auth/session', {
        credentials: 'include'
      })
      const sessionData = sessionResponse.ok ? await sessionResponse.json() : { error: 'Failed to fetch' }

      // Test subjects endpoint
      const subjectsResponse = await fetch('/api/subjects', {
        credentials: 'include'
      })
      const subjectsData = subjectsResponse.ok ? await subjectsResponse.json() : { error: 'Failed to fetch' }

      // Test classes endpoint
      const classesResponse = await fetch('/api/classes', {
        credentials: 'include'
      })
      const classesData = classesResponse.ok ? await classesResponse.json() : { error: 'Failed to fetch' }

      setAuthState({
        cookies,
        auth: {
          status: authResponse.status,
          data: authData
        },
        session: {
          status: sessionResponse.status,
          data: sessionData
        },
        subjects: {
          status: subjectsResponse.status,
          data: subjectsData
        },
        classes: {
          status: classesResponse.status,
          data: classesData
        }
      })
    } catch (error) {
      console.error("Debug error:", error)
      setAuthState({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testLogin = async () => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'Admin@123'
        })
      })

      const data = await response.json()
      console.log("Login test result:", { status: response.status, data })

      // Refresh auth state after login attempt
      setTimeout(checkAuthState, 1000)
    } catch (error) {
      console.error("Login test error:", error)
    }
  }

  const clearCookies = () => {
    // Clear all auth-related cookies
    document.cookie = "accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"
    document.cookie = "refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"
    document.cookie = "session=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"
    
    setTimeout(checkAuthState, 500)
  }

  useEffect(() => {
    checkAuthState()
  }, [])

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Authentication Debug</h1>
        <div className="space-x-2">
          <Button onClick={checkAuthState} disabled={loading}>
            {loading ? "Checking..." : "Refresh State"}
          </Button>
          <Button onClick={testLogin} variant="outline">
            Test Login
          </Button>
          <Button onClick={clearCookies} variant="destructive">
            Clear Cookies
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Cookies</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-sm bg-gray-100 p-3 rounded overflow-auto">
              {authState.cookies || "No cookies found"}
            </pre>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Auth Endpoint (/api/auth)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Status:</strong> {authState.auth?.status}</p>
              <pre className="text-sm bg-gray-100 p-3 rounded overflow-auto max-h-40">
                {JSON.stringify(authState.auth?.data, null, 2)}
              </pre>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Session Endpoint (/api/auth/session)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Status:</strong> {authState.session?.status}</p>
              <pre className="text-sm bg-gray-100 p-3 rounded overflow-auto max-h-40">
                {JSON.stringify(authState.session?.data, null, 2)}
              </pre>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Subjects Endpoint (/api/subjects)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Status:</strong> {authState.subjects?.status}</p>
              <pre className="text-sm bg-gray-100 p-3 rounded overflow-auto max-h-40">
                {JSON.stringify(authState.subjects?.data, null, 2)}
              </pre>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Classes Endpoint (/api/classes)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Status:</strong> {authState.classes?.status}</p>
              <pre className="text-sm bg-gray-100 p-3 rounded overflow-auto max-h-40">
                {JSON.stringify(authState.classes?.data, null, 2)}
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>

      {authState.error && (
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">{authState.error}</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default function DebugAuthPage() {
  return (
    <ProtectedPage requiredRoles={["SUPER_ADMIN"]}>
      <DebugAuthContent />
    </ProtectedPage>
  )
}
