"use client"

import { useRef } from "react"
import { motion, useInView } from "framer-motion"
import Image from "next/image"

export function PartnersSection() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const partners = [
    { name: "Ministry of Education", logo: "/placeholder.svg?height=60&width=180" },
    { name: "Zambia ICT Authority", logo: "/placeholder.svg?height=60&width=180" },
    { name: "University of Zambia", logo: "/placeholder.svg?height=60&width=180" },
    { name: "Zambia National Commercial Bank", logo: "/placeholder.svg?height=60&width=180" },
    { name: "Zamtel", logo: "/placeholder.svg?height=60&width=180" },
    { name: "MTN Zambia", logo: "/placeholder.svg?height=60&width=180" },
  ]

  return (
    <section ref={ref} className="py-12 border-b border-gray-200">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <p className="text-sm font-medium text-gray-500 uppercase tracking-wider">Trusted by leading organizations</p>
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center justify-items-center"
        >
          {partners.map((partner, index) => (
            <div key={index} className="grayscale hover:grayscale-0 transition-all duration-300">
              <Image
                src={partner.logo || "/placeholder.svg"}
                alt={partner.name}
                width={180}
                height={60}
                className="h-12 w-auto object-contain"
              />
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
