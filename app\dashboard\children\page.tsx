"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  GraduationCap,
  Calendar,
  FileText,
  CreditCard,
  ChevronRight,
  BookOpen,
  Bell,
  MessageSquare,
  School,
  Download,
} from "lucide-react"
import { useTenant } from "@/contexts/tenant-context"
import { Progress } from "@/components/ui/progress"

// Mock data for children
const childrenData = [
  {
    id: "1",
    name: "<PERSON>",
    age: 10,
    grade: "Grade 5",
    class: "5A",
    teacher: "Mr. <PERSON>",
    attendance: 95,
    overallGrade: "A-",
    subjects: [
      { name: "Mathematics", grade: "A-", score: 90 },
      { name: "Science", grade: "B+", score: 85 },
      { name: "English", grade: "A", score: 95 },
      { name: "History", grade: "B", score: 82 },
      { name: "Physical Education", grade: "A", score: 94 },
    ],
    upcomingAssignments: [
      { subject: "Mathematics", title: "Algebra Quiz", due: "May 26, 2025" },
      { subject: "Science", title: "Ecosystem Project", due: "May 30, 2025" },
      { subject: "English", title: "Essay Submission", due: "May 28, 2025" },
    ],
    feeStatus: "Paid",
    nextFeeDate: "September 1, 2025",
    recentAttendance: [
      { date: "May 20, 2025", status: "Present" },
      { date: "May 19, 2025", status: "Present" },
      { date: "May 18, 2025", status: "Present" },
      { date: "May 17, 2025", status: "Absent" },
      { date: "May 16, 2025", status: "Present" },
    ],
  },
  {
    id: "2",
    name: "Noah Wilson",
    age: 8,
    grade: "Grade 3",
    class: "3C",
    teacher: "Mrs. Emily Johnson",
    attendance: 92,
    overallGrade: "B+",
    subjects: [
      { name: "Mathematics", grade: "B+", score: 85 },
      { name: "Science", grade: "A-", score: 90 },
      { name: "English", grade: "B", score: 82 },
      { name: "History", grade: "B-", score: 80 },
      { name: "Physical Education", grade: "A", score: 95 },
    ],
    upcomingAssignments: [
      { subject: "Mathematics", title: "Multiplication Test", due: "May 25, 2025" },
      { subject: "Science", title: "Plant Growth Project", due: "May 29, 2025" },
      { subject: "English", title: "Book Report", due: "June 2, 2025" },
    ],
    feeStatus: "Paid",
    nextFeeDate: "September 1, 2025",
    recentAttendance: [
      { date: "May 20, 2025", status: "Present" },
      { date: "May 19, 2025", status: "Present" },
      { date: "May 18, 2025", status: "Absent" },
      { date: "May 17, 2025", status: "Present" },
      { date: "May 16, 2025", status: "Present" },
    ],
  },
]

export default function ChildrenPage() {
  const { currentUser } = useTenant()
  const [isLoading, setIsLoading] = useState(true)
  const [children, setChildren] = useState([])
  const [selectedChild, setSelectedChild] = useState("")

  useEffect(() => {
    // Simulate API call to fetch children data
    const fetchData = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      setChildren(childrenData)
      setSelectedChild(childrenData[0].id)
      setIsLoading(false)
    }

    fetchData()
  }, [])

  // Get the selected child's data
  const childData = children.find((child: any) => child.id === selectedChild)

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Children</h1>
          <p className="text-gray-500">Monitor your children's academic progress and school activities</p>
        </div>
        <div className="flex gap-2">
          <Button>
            <MessageSquare className="mr-2 h-4 w-4" />
            Contact Teacher
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <p>Loading children data...</p>
        </div>
      ) : (
        <>
          <div className="flex flex-wrap gap-4">
            {children.map((child: any) => (
              <Button
                key={child.id}
                variant={selectedChild === child.id ? "default" : "outline"}
                className="h-auto py-2 px-4"
                onClick={() => setSelectedChild(child.id)}
              >
                <div className="flex items-center gap-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={`/placeholder.svg?height=32&width=32`} alt={child.name} />
                    <AvatarFallback>{child.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div className="text-left">
                    <p className="font-medium">{child.name}</p>
                    <p className="text-xs">
                      {child.grade} {child.class}
                    </p>
                  </div>
                </div>
              </Button>
            ))}
          </div>

          {childData && (
            <>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">Overall Grade</CardTitle>
                    <GraduationCap className="h-4 w-4 text-gray-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{childData.overallGrade}</div>
                    <p className="text-xs text-gray-500">Average across all subjects</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">Attendance</CardTitle>
                    <Calendar className="h-4 w-4 text-gray-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{childData.attendance}%</div>
                    <Progress value={childData.attendance} className="h-2 mt-2" />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">Fee Status</CardTitle>
                    <CreditCard className="h-4 w-4 text-gray-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      <Badge variant={childData.feeStatus === "Paid" ? "default" : "destructive"}>
                        {childData.feeStatus}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">Next payment: {childData.nextFeeDate}</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">Class Teacher</CardTitle>
                    <School className="h-4 w-4 text-gray-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-lg font-bold">{childData.teacher}</div>
                    <p className="text-xs text-gray-500">
                      {childData.grade} {childData.class}
                    </p>
                  </CardContent>
                </Card>
              </div>

              <Tabs defaultValue="overview" className="space-y-4">
                <TabsList>
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="academics">Academics</TabsTrigger>
                  <TabsTrigger value="attendance">Attendance</TabsTrigger>
                  <TabsTrigger value="fees">Fees & Payments</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <Card>
                      <CardHeader>
                        <CardTitle>Subject Performance</CardTitle>
                        <CardDescription>Current grades by subject</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {childData.subjects.map((subject: any, i: number) => (
                            <div key={i} className="space-y-1">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">{subject.name}</span>
                                <Badge
                                  variant={
                                    subject.grade.startsWith("A")
                                      ? "default"
                                      : subject.grade.startsWith("B")
                                        ? "secondary"
                                        : "outline"
                                  }
                                >
                                  {subject.grade}
                                </Badge>
                              </div>
                              <Progress value={subject.score} className="h-2" />
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Upcoming Assignments</CardTitle>
                        <CardDescription>Tasks due in the next 7 days</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {childData.upcomingAssignments.map((assignment: any, i: number) => (
                            <div key={i} className="flex items-start gap-3">
                              <div className="bg-amber-100 text-amber-700 px-2 py-1 rounded text-xs font-medium mt-1">
                                {assignment.subject}
                              </div>
                              <div>
                                <p className="font-medium">{assignment.title}</p>
                                <p className="text-sm text-gray-500">Due: {assignment.due}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Button variant="ghost" size="sm" className="w-full" asChild>
                          <Link href={`/dashboard/grades?child=${childData.id}`}>
                            View All Assignments <ChevronRight className="ml-1 h-4 w-4" />
                          </Link>
                        </Button>
                      </CardFooter>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Recent Attendance</CardTitle>
                        <CardDescription>Last 5 school days</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {childData.recentAttendance.map((day: any, i: number) => (
                            <div key={i} className="flex items-center justify-between border-b pb-2 last:border-0">
                              <p className="text-sm">{day.date}</p>
                              <Badge variant={day.status === "Present" ? "default" : "destructive"}>{day.status}</Badge>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Button variant="ghost" size="sm" className="w-full" asChild>
                          <Link href={`/dashboard/attendance?child=${childData.id}`}>
                            View Full Attendance <ChevronRight className="ml-1 h-4 w-4" />
                          </Link>
                        </Button>
                      </CardFooter>
                    </Card>
                  </div>

                  <Card>
                    <CardHeader>
                      <CardTitle>School Notifications</CardTitle>
                      <CardDescription>Recent updates from {childData.name}'s school</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {[
                          {
                            title: "Parent-Teacher Meeting",
                            description: "Scheduled for June 5, 2025 at 4:00 PM",
                            time: "2 days ago",
                            icon: <MessageSquare className="h-5 w-5 text-blue-500" />,
                          },
                          {
                            title: "End of Term Exams",
                            description: "Exams will be held from June 10-15, 2025",
                            time: "3 days ago",
                            icon: <FileText className="h-5 w-5 text-purple-500" />,
                          },
                          {
                            title: "School Holiday",
                            description: "School will be closed on May 30, 2025 for Staff Development Day",
                            time: "1 week ago",
                            icon: <Calendar className="h-5 w-5 text-emerald-500" />,
                          },
                        ].map((notification, i) => (
                          <div key={i} className="flex items-start">
                            <div className="mr-3 mt-1">{notification.icon}</div>
                            <div>
                              <p className="font-medium">{notification.title}</p>
                              <p className="text-sm text-gray-500">{notification.description}</p>
                              <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="academics" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Academic Performance</CardTitle>
                      <CardDescription>Detailed view of {childData.name}'s academic progress</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-medium mb-2">Subject Grades</h3>
                          <div className="rounded-md border">
                            <table className="min-w-full divide-y divide-gray-200">
                              <thead>
                                <tr>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Subject
                                  </th>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Teacher
                                  </th>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Grade
                                  </th>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Score
                                  </th>
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {childData.subjects.map((subject: any, i: number) => (
                                  <tr key={i}>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <div className="flex items-center">
                                        <BookOpen className="h-4 w-4 text-gray-500 mr-2" />
                                        <span>{subject.name}</span>
                                      </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      {i === 0
                                        ? "Mr. John Smith"
                                        : i === 1
                                          ? "Mrs. Emily Johnson"
                                          : i === 2
                                            ? "Ms. Sarah Williams"
                                            : i === 3
                                              ? "Mr. Robert Brown"
                                              : "Mr. Michael Davis"}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <Badge
                                        variant={
                                          subject.grade.startsWith("A")
                                            ? "default"
                                            : subject.grade.startsWith("B")
                                              ? "secondary"
                                              : "outline"
                                        }
                                      >
                                        {subject.grade}
                                      </Badge>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">{subject.score}%</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-medium mb-2">Recent Assessments</h3>
                          <div className="space-y-4">
                            {[
                              {
                                subject: "Mathematics",
                                title: "Algebra Quiz",
                                score: 88,
                                maxScore: 100,
                                date: "May 15, 2025",
                              },
                              {
                                subject: "Science",
                                title: "Ecosystem Project",
                                score: 92,
                                maxScore: 100,
                                date: "May 10, 2025",
                              },
                              { subject: "English", title: "Essay", score: 95, maxScore: 100, date: "May 8, 2025" },
                              {
                                subject: "History",
                                title: "Research Paper",
                                score: 85,
                                maxScore: 100,
                                date: "May 5, 2025",
                              },
                            ].map((assessment, i) => (
                              <div key={i} className="flex items-center justify-between border-b pb-3 last:border-0">
                                <div className="flex items-start gap-3">
                                  <div className="bg-amber-100 text-amber-700 px-2 py-1 rounded text-xs font-medium mt-1">
                                    {assessment.subject}
                                  </div>
                                  <div>
                                    <p className="font-medium">{assessment.title}</p>
                                    <p className="text-sm text-gray-500">{assessment.date}</p>
                                  </div>
                                </div>
                                <Badge
                                  variant={
                                    assessment.score >= 90
                                      ? "default"
                                      : assessment.score >= 80
                                        ? "secondary"
                                        : "outline"
                                  }
                                >
                                  {assessment.score}/{assessment.maxScore}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-medium mb-2">Teacher Comments</h3>
                          <div className="space-y-4 border rounded-md p-4 bg-gray-50">
                            <p className="italic text-gray-700">
                              "{childData.name} has shown consistent improvement in mathematics this term.
                              {childData.name.split(" ")[0]} participates actively in class discussions and completes
                              assignments on time.
                              {childData.name.split(" ")[0]} should continue to practice problem-solving skills to
                              further improve."
                            </p>
                            <p className="text-sm text-gray-500">- {childData.teacher}, Class Teacher</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button asChild>
                        <Link href={`/dashboard/grades?child=${childData.id}`}>View Complete Academic Record</Link>
                      </Button>
                    </CardFooter>
                  </Card>
                </TabsContent>

                <TabsContent value="attendance" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Attendance Record</CardTitle>
                      <CardDescription>Detailed attendance history for {childData.name}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="text-lg font-medium">Current Term Attendance</h3>
                            <p className="text-sm text-gray-500">January - June 2025</p>
                          </div>
                          <div className="text-right">
                            <p className="text-2xl font-bold">{childData.attendance}%</p>
                            <p className="text-sm text-gray-500">Present Days</p>
                          </div>
                        </div>

                        <Progress value={childData.attendance} className="h-2" />

                        <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
                          <div className="rounded-lg border p-3">
                            <p className="text-sm font-medium text-gray-500">Present</p>
                            <p className="text-2xl font-bold">85</p>
                          </div>
                          <div className="rounded-lg border p-3">
                            <p className="text-sm font-medium text-gray-500">Absent</p>
                            <p className="text-2xl font-bold">4</p>
                          </div>
                          <div className="rounded-lg border p-3">
                            <p className="text-sm font-medium text-gray-500">Late</p>
                            <p className="text-2xl font-bold">2</p>
                          </div>
                          <div className="rounded-lg border p-3">
                            <p className="text-sm font-medium text-gray-500">Total Days</p>
                            <p className="text-2xl font-bold">91</p>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-medium mb-2">Monthly Attendance</h3>
                          <div className="rounded-md border">
                            <table className="min-w-full divide-y divide-gray-200">
                              <thead>
                                <tr>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Month
                                  </th>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Present
                                  </th>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Absent
                                  </th>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Late
                                  </th>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Percentage
                                  </th>
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {["January", "February", "March", "April", "May"].map((month, i) => (
                                  <tr key={i}>
                                    <td className="px-6 py-4 whitespace-nowrap">{month}</td>
                                    <td className="px-6 py-4 whitespace-nowrap">{[18, 16, 19, 17, 15][i]}</td>
                                    <td className="px-6 py-4 whitespace-nowrap">{[1, 0, 1, 1, 1][i]}</td>
                                    <td className="px-6 py-4 whitespace-nowrap">{[0, 1, 0, 1, 0][i]}</td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <Badge variant="outline">{[95, 100, 95, 94, 94][i]}%</Badge>
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Button variant="outline">
                        <Download className="mr-2 h-4 w-4" />
                        Download Report
                      </Button>
                      <Button variant="default">
                        <Bell className="mr-2 h-4 w-4" />
                        Set Attendance Alert
                      </Button>
                    </CardFooter>
                  </Card>
                </TabsContent>

                <TabsContent value="fees" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Fees & Payments</CardTitle>
                      <CardDescription>Payment history and upcoming fees for {childData.name}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="text-lg font-medium">Current Status</h3>
                            <p className="text-sm text-gray-500">2025 Academic Year</p>
                          </div>
                          <Badge
                            variant={childData.feeStatus === "Paid" ? "default" : "destructive"}
                            className="text-base px-3 py-1"
                          >
                            {childData.feeStatus}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                          <div className="rounded-lg border p-4">
                            <p className="text-sm font-medium text-gray-500">Term 1 Fees</p>
                            <p className="text-2xl font-bold">K5,000</p>
                            <Badge variant="outline" className="mt-2">
                              Paid
                            </Badge>
                          </div>
                          <div className="rounded-lg border p-4">
                            <p className="text-sm font-medium text-gray-500">Term 2 Fees</p>
                            <p className="text-2xl font-bold">K5,000</p>
                            <Badge variant="outline" className="mt-2">
                              Paid
                            </Badge>
                          </div>
                          <div className="rounded-lg border p-4">
                            <p className="text-sm font-medium text-gray-500">Term 3 Fees</p>
                            <p className="text-2xl font-bold">K5,000</p>
                            <Badge variant="outline" className="mt-2">
                              Due: {childData.nextFeeDate}
                            </Badge>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-medium mb-2">Payment History</h3>
                          <div className="rounded-md border">
                            <table className="min-w-full divide-y divide-gray-200">
                              <thead>
                                <tr>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Description
                                  </th>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Date
                                  </th>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Amount
                                  </th>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                  </th>
                                  <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Receipt
                                  </th>
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {[
                                  { desc: "Term 2 Tuition Fee", date: "January 5, 2025", amount: "K5,000" },
                                  { desc: "Term 1 Tuition Fee", date: "September 3, 2024", amount: "K5,000" },
                                  { desc: "Registration Fee", date: "August 15, 2024", amount: "K500" },
                                ].map((payment, i) => (
                                  <tr key={i}>
                                    <td className="px-6 py-4 whitespace-nowrap">{payment.desc}</td>
                                    <td className="px-6 py-4 whitespace-nowrap">{payment.date}</td>
                                    <td className="px-6 py-4 whitespace-nowrap">{payment.amount}</td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <Badge variant="default">Paid</Badge>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <Button variant="ghost" size="sm">
                                        <Download className="h-4 w-4 mr-1" />
                                        Receipt
                                      </Button>
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg font-medium mb-2">Upcoming Payments</h3>
                          <div className="rounded-lg border p-4 bg-gray-50">
                            <div className="flex justify-between items-center">
                              <div>
                                <p className="font-medium">Term 3 Tuition Fee</p>
                                <p className="text-sm text-gray-500">Due: {childData.nextFeeDate}</p>
                              </div>
                              <div className="text-right">
                                <p className="font-bold">K5,000</p>
                                <Button size="sm" className="mt-2">
                                  Pay Now
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </>
          )}
        </>
      )}
    </div>
  )
}
