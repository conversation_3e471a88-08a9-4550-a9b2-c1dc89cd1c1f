import { NextResponse } from "next/server"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const class_id = searchParams.get("class_id") || ""
    const subject_id = searchParams.get("subject_id") || ""
    const day_of_week = searchParams.get("day_of_week") || ""

    // Forward the request to our Flask backend
    const response = await fetch(
      `http://localhost:4000/api/v1/schedules?class_id=${class_id}&subject_id=${subject_id}&day_of_week=${day_of_week}`,
      {
        headers: {
          Cookie: request.headers.get("cookie") || "",
        },
      },
    )

    if (!response.ok) {
      return NextResponse.json({ error: "Failed to fetch schedules" }, { status: response.status })
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Schedules API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Forward the request to our Flask backend
    const response = await fetch("http://localhost:4000/api/v1/schedules", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Cookie: request.headers.get("cookie") || "",
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: data.message || "Failed to create schedule" }, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Create schedule API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
