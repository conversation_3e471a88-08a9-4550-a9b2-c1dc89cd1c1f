import type { Metada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { Star } from "lucide-react"

import { LandingHeader } from "@/components/landing/landing-header"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"

export const metadata: Metadata = {
  title: "Testimonials | School Management System",
  description: "Read what our customers have to say about our school management system.",
}

export default function TestimonialPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <LandingHeader />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 py-20 md:py-28">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">What Our Customers Say</h1>
                <p className="mx-auto max-w-[700px] text-slate-500 dark:text-slate-400 md:text-xl">
                  Discover how our school management system is transforming education worldwide.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Testimonials */}
        <section className="py-20">
          <div className="container px-4 md:px-6">
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {featuredTestimonials.map((testimonial, index) => (
                <TestimonialCard key={index} testimonial={testimonial} featured />
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials by Role */}
        <section className="bg-slate-50 dark:bg-slate-900 py-20">
          <div className="container px-4 md:px-6">
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl text-center mb-10">
              Success Stories by Role
            </h2>

            <Tabs defaultValue="administrators" className="w-full">
              <div className="flex justify-center mb-8">
                <TabsList>
                  <TabsTrigger value="administrators">Administrators</TabsTrigger>
                  <TabsTrigger value="teachers">Teachers</TabsTrigger>
                  <TabsTrigger value="parents">Parents</TabsTrigger>
                  <TabsTrigger value="it-staff">IT Staff</TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="administrators">
                <div className="grid gap-8 md:grid-cols-2">
                  {administratorTestimonials.map((testimonial, index) => (
                    <TestimonialCard key={index} testimonial={testimonial} />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="teachers">
                <div className="grid gap-8 md:grid-cols-2">
                  {teacherTestimonials.map((testimonial, index) => (
                    <TestimonialCard key={index} testimonial={testimonial} />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="parents">
                <div className="grid gap-8 md:grid-cols-2">
                  {parentTestimonials.map((testimonial, index) => (
                    <TestimonialCard key={index} testimonial={testimonial} />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="it-staff">
                <div className="grid gap-8 md:grid-cols-2">
                  {itStaffTestimonials.map((testimonial, index) => (
                    <TestimonialCard key={index} testimonial={testimonial} />
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* Video Testimonials */}
        <section className="py-20">
          <div className="container px-4 md:px-6">
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl text-center mb-10">Video Testimonials</h2>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {videoTestimonials.map((video, index) => (
                <Card key={index} className="overflow-hidden">
                  <div className="aspect-video relative bg-slate-200 dark:bg-slate-800">
                    {/* Replace with actual video embed */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="48"
                        height="48"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-primary cursor-pointer"
                      >
                        <circle cx="12" cy="12" r="10"></circle>
                        <polygon points="10 8 16 12 10 16 10 8"></polygon>
                      </svg>
                    </div>
                  </div>
                  <CardContent className="p-6">
                    <h3 className="font-bold text-xl mb-2">{video.title}</h3>
                    <p className="text-slate-500 dark:text-slate-400">{video.description}</p>
                    <div className="flex items-center mt-4">
                      <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                        <Image
                          src={video.avatar || "/placeholder.svg"}
                          alt={video.name}
                          width={40}
                          height={40}
                          className="object-cover"
                        />
                      </div>
                      <div>
                        <p className="font-medium">{video.name}</p>
                        <p className="text-sm text-slate-500 dark:text-slate-400">{video.role}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Case Studies */}
        <section className="bg-slate-50 dark:bg-slate-900 py-20">
          <div className="container px-4 md:px-6">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Case Studies</h2>
              <p className="mx-auto max-w-[700px] text-slate-500 dark:text-slate-400 md:text-xl mt-4">
                Detailed success stories from educational institutions using our platform
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {caseStudies.map((study, index) => (
                <Card key={index} className="overflow-hidden">
                  <div className="aspect-video relative">
                    <Image src={study.image || "/placeholder.svg"} alt={study.title} fill className="object-cover" />
                  </div>
                  <CardContent className="p-6">
                    <h3 className="font-bold text-xl mb-2">{study.title}</h3>
                    <p className="text-slate-500 dark:text-slate-400 mb-4">{study.description}</p>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium bg-primary/10 text-primary px-3 py-1 rounded-full">
                        {study.category}
                      </span>
                      <Button variant="link" asChild>
                        <Link href={study.link}>Read Case Study</Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Join Our Growing Community</h2>
                <p className="mx-auto max-w-[600px] text-slate-500 dark:text-slate-400 md:text-xl">
                  Experience the difference our school management system can make for your institution.
                </p>
              </div>
              <div className="space-x-4">
                <Button size="lg" asChild>
                  <Link href="/auth/register">Start Free Trial</Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/contact">Schedule Demo</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}

function TestimonialCard({ testimonial, featured = false }) {
  return (
    <Card className={`${featured ? "border-primary" : ""}`}>
      <CardContent className="p-6">
        <div className="flex mb-4">
          {[...Array(5)].map((_, i) => (
            <Star key={i} className="w-5 h-5 fill-current text-yellow-500" />
          ))}
        </div>
        <blockquote className="mb-4">
          <p className="text-lg italic">{testimonial.quote}</p>
        </blockquote>
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
            <Image
              src={testimonial.avatar || "/placeholder.svg"}
              alt={testimonial.name}
              width={48}
              height={48}
              className="object-cover"
            />
          </div>
          <div>
            <p className="font-bold">{testimonial.name}</p>
            <p className="text-sm text-slate-500 dark:text-slate-400">
              {testimonial.role}, {testimonial.institution}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

const featuredTestimonials = [
  {
    quote:
      "This system has completely transformed how we manage our school. The administrative burden has been reduced by at least 50%, allowing our staff to focus more on education and less on paperwork.",
    name: "Dr. Sarah Johnson",
    role: "Principal",
    institution: "Westfield Academy",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "The parent communication features have dramatically improved our engagement levels. Parents are more involved in their children's education, and we've seen a measurable improvement in student performance as a result.",
    name: "Michael Chen",
    role: "Vice Principal",
    institution: "Oakridge High School",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "As a teacher, the grading and assessment tools have saved me countless hours. What used to take me entire weekends now takes just a few hours, and the insights I get from the analytics help me tailor my teaching approach.",
    name: "Emily Rodriguez",
    role: "Science Teacher",
    institution: "Lincoln Middle School",
    avatar: "/placeholder.svg?height=48&width=48",
  },
]

const administratorTestimonials = [
  {
    quote:
      "The reporting features have given us unprecedented insights into our school's performance. We can now make data-driven decisions that have a real impact on student outcomes.",
    name: "Robert Thompson",
    role: "Superintendent",
    institution: "Riverdale School District",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "Managing multiple campuses used to be a logistical nightmare. Now, with this system, I can oversee operations across all our locations from a single dashboard.",
    name: "Patricia Williams",
    role: "Executive Director",
    institution: "Greenwood Charter Schools",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "The financial management module has streamlined our budgeting process and helped us identify areas where we can optimize our resources.",
    name: "James Wilson",
    role: "Business Manager",
    institution: "St. Mary's Academy",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "Implementing this system was the best decision we made. The onboarding process was smooth, and the support team was there every step of the way.",
    name: "Linda Martinez",
    role: "School Administrator",
    institution: "Parkview Elementary",
    avatar: "/placeholder.svg?height=48&width=48",
  },
]

const teacherTestimonials = [
  {
    quote:
      "The lesson planning tools have revolutionized how I prepare for classes. I can easily align my lessons with curriculum standards and share resources with students.",
    name: "David Anderson",
    role: "Math Teacher",
    institution: "Washington High School",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "Tracking student progress has never been easier. I can quickly identify which students need additional support and tailor my teaching accordingly.",
    name: "Jennifer Lee",
    role: "English Teacher",
    institution: "Fairview Middle School",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "The ability to communicate directly with parents through the platform has improved my parent-teacher relationships significantly.",
    name: "Thomas Brown",
    role: "History Teacher",
    institution: "Central High School",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "As a special education teacher, the individualized education plan features have been invaluable for tracking accommodations and progress toward goals.",
    name: "Maria Garcia",
    role: "Special Education Teacher",
    institution: "Sunshine Elementary",
    avatar: "/placeholder.svg?height=48&width=48",
  },
]

const parentTestimonials = [
  {
    quote:
      "Being able to track my children's homework assignments and grades in real-time has helped me stay involved in their education like never before.",
    name: "John Davis",
    role: "Parent of 2",
    institution: "Maple Grove Elementary",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "The communication tools make it so easy to reach out to teachers with questions or concerns. I feel much more connected to what's happening at school.",
    name: "Sophia Kim",
    role: "Parent",
    institution: "Pinecrest Academy",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "Paying school fees online and tracking expenses has simplified what used to be a confusing process. I appreciate the transparency.",
    name: "Marcus Johnson",
    role: "Parent of 3",
    institution: "Lakeside School",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "As a working parent, being able to check attendance and receive notifications about my child's activities has been incredibly helpful.",
    name: "Rebecca Taylor",
    role: "Parent",
    institution: "Hillcrest Elementary",
    avatar: "/placeholder.svg?height=48&width=48",
  },
]

const itStaffTestimonials = [
  {
    quote:
      "The system's architecture is robust and scalable. We've been able to integrate it with our existing infrastructure with minimal issues.",
    name: "Alan Wong",
    role: "IT Director",
    institution: "Eastside School District",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "User management is straightforward, and the role-based access controls ensure that everyone has appropriate permissions.",
    name: "Samantha Miller",
    role: "Systems Administrator",
    institution: "Northridge Academy",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "The API documentation is comprehensive, which has allowed us to build custom integrations for our specific needs.",
    name: "Raj Patel",
    role: "Software Developer",
    institution: "Tech Magnet School",
    avatar: "/placeholder.svg?height=48&width=48",
  },
  {
    quote:
      "Data security was our primary concern, and this system has exceeded our expectations with its robust security features and compliance standards.",
    name: "Christine Nelson",
    role: "IT Security Specialist",
    institution: "Valley School District",
    avatar: "/placeholder.svg?height=48&width=48",
  },
]

const videoTestimonials = [
  {
    title: "Transforming Westfield Academy",
    description: "Dr. Johnson shares how our system helped improve administrative efficiency by 50%.",
    name: "Dr. Sarah Johnson",
    role: "Principal, Westfield Academy",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    title: "Improving Parent Engagement",
    description: "See how Oakridge High School increased parent participation through our platform.",
    name: "Michael Chen",
    role: "Vice Principal, Oakridge High School",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    title: "A Teacher's Perspective",
    description: "Emily explains how our grading tools saved her countless hours of work.",
    name: "Emily Rodriguez",
    role: "Science Teacher, Lincoln Middle School",
    avatar: "/placeholder.svg?height=40&width=40",
  },
]

const caseStudies = [
  {
    title: "Riverdale School District: District-wide Implementation",
    description: "How a large school district successfully rolled out our platform across 15 schools.",
    category: "K-12 District",
    image: "/placeholder.svg?height=200&width=300",
    link: "/case-studies/riverdale",
  },
  {
    title: "St. Mary's Academy: Improving Financial Management",
    description: "How St. Mary's optimized their budget and reduced administrative costs by 30%.",
    category: "Private School",
    image: "/placeholder.svg?height=200&width=300",
    link: "/case-studies/st-marys",
  },
  {
    title: "Tech Magnet School: Custom Integrations",
    description: "How a technology-focused school extended our platform with custom features.",
    category: "Specialized School",
    image: "/placeholder.svg?height=200&width=300",
    link: "/case-studies/tech-magnet",
  },
]
