#!/bin/bash

# Set working directory
cd /home/<USER>/unchartedtechnologies/school_management-project/edusync-frontend/backend

# Generate Prisma client based on the schema
echo "Generating Prisma client..."
npx prisma generate

# Create database schema
echo "Creating database schema..."
npx prisma db push --accept-data-loss

# Initialize database with seed data
echo "Initializing database with seed data..."
node src/utils/initDb.js

echo "Database setup completed!"
