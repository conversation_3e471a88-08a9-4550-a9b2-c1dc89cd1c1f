import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  GraduationCap,
  Clock,
  Award,
  FileText,
  Users,
  CreditCard,
} from "lucide-react"
import Link from "next/link"

export default function StudentProfilePage({ params }: { params: { id: string } }) {
  const studentId = params.id

  // This would normally come from a database
  const student = {
    id: studentId,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    dateOfBirth: "April 15, 2008",
    address: "456 Student Lane, Education City, NY 10001",
    grade: "10th Grade",
    section: "Section A",
    rollNumber: "2023-10-042",
    admissionDate: "September 5, 2021",
    parentName: "<PERSON>",
    parentEmail: "r<PERSON><PERSON><PERSON>@example.com",
    parentPhone: "+****************",
    attendance: "94%",
    gpa: "3.8",
    status: "Active",
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Student Profile</h1>
          <p className="text-muted-foreground">View and manage student information, academic records, and more.</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <FileText className="mr-2 h-4 w-4" />
            Export Profile
          </Button>
          <Button>
            <User className="mr-2 h-4 w-4" />
            Edit Profile
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader className="flex flex-row items-center gap-4 pb-2">
            <Avatar className="h-16 w-16">
              <AvatarImage alt={student.name} src={`/placeholder.svg?height=64&width=64`} />
              <AvatarFallback>
                {student.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle>{student.name}</CardTitle>
              <CardDescription>
                {student.grade} • {student.section}
              </CardDescription>
              <div className="mt-1 inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold">
                #{student.rollNumber}
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>{student.email}</span>
              </div>
              <div className="flex items-center text-sm">
                <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>{student.phone}</span>
              </div>
              <div className="flex items-center text-sm">
                <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>DOB: {student.dateOfBirth}</span>
              </div>
              <div className="flex items-start text-sm">
                <MapPin className="mr-2 h-4 w-4 text-muted-foreground mt-0.5" />
                <span>{student.address}</span>
              </div>
            </div>

            <div className="pt-4 border-t space-y-2">
              <h3 className="text-sm font-medium">Parent/Guardian Information</h3>
              <div className="flex items-center text-sm">
                <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>{student.parentName}</span>
              </div>
              <div className="flex items-center text-sm">
                <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>{student.parentEmail}</span>
              </div>
              <div className="flex items-center text-sm">
                <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>{student.parentPhone}</span>
              </div>
            </div>

            <div className="pt-4 border-t space-y-2">
              <h3 className="text-sm font-medium">Academic Information</h3>
              <div className="flex items-center text-sm">
                <GraduationCap className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Grade: {student.grade}</span>
              </div>
              <div className="flex items-center text-sm">
                <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Section: {student.section}</span>
              </div>
              <div className="flex items-center text-sm">
                <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Admission Date: {student.admissionDate}</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-2">
            <div className="grid grid-cols-2 gap-2 w-full">
              <Button variant="outline" className="w-full" asChild>
                <Link href={`/dashboard/students/${studentId}/attendance`}>
                  <Clock className="mr-2 h-4 w-4" />
                  Attendance
                </Link>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href={`/dashboard/students/${studentId}/grades`}>
                  <Award className="mr-2 h-4 w-4" />
                  Grades
                </Link>
              </Button>
            </div>
            <Button variant="outline" className="w-full" asChild>
              <Link href={`/dashboard/students/${studentId}/fees`}>
                <CreditCard className="mr-2 h-4 w-4" />
                Fee Details
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Academic Overview</CardTitle>
            <CardDescription>Student's academic performance and activities</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="summary" className="space-y-4">
              <TabsList>
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="subjects">Subjects</TabsTrigger>
                <TabsTrigger value="activities">Activities</TabsTrigger>
                <TabsTrigger value="documents">Documents</TabsTrigger>
              </TabsList>

              <TabsContent value="summary" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">GPA</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{student.gpa}</div>
                      <p className="text-xs text-muted-foreground">Out of 4.0</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Attendance</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{student.attendance}</div>
                      <p className="text-xs text-muted-foreground">Academic Year 2023-24</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Status</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-green-500">{student.status}</div>
                      <p className="text-xs text-muted-foreground">Current enrollment status</p>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Recent Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium">Mathematics</span>
                          <span className="text-sm font-medium">92%</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div className="bg-primary h-2 rounded-full" style={{ width: "92%" }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium">Science</span>
                          <span className="text-sm font-medium">88%</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div className="bg-primary h-2 rounded-full" style={{ width: "88%" }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium">English</span>
                          <span className="text-sm font-medium">95%</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div className="bg-primary h-2 rounded-full" style={{ width: "95%" }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium">History</span>
                          <span className="text-sm font-medium">78%</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div className="bg-primary h-2 rounded-full" style={{ width: "78%" }}></div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Upcoming Assessments</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { subject: "Mathematics", type: "Quiz", date: "Oct 15, 2023", topic: "Algebra II" },
                        { subject: "Science", type: "Lab Report", date: "Oct 18, 2023", topic: "Chemical Reactions" },
                        { subject: "English", type: "Essay", date: "Oct 20, 2023", topic: "American Literature" },
                      ].map((assessment, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">
                              {assessment.subject}: {assessment.type}
                            </p>
                            <p className="text-sm text-muted-foreground">{assessment.topic}</p>
                          </div>
                          <div className="text-sm">{assessment.date}</div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="subjects" className="space-y-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Current Subjects</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { name: "Mathematics", teacher: "Dr. Robert Smith", grade: "A", credits: 4 },
                        { name: "Science", teacher: "Ms. Jennifer Lee", grade: "A-", credits: 4 },
                        { name: "English", teacher: "Mr. David Wilson", grade: "A+", credits: 4 },
                        { name: "History", teacher: "Mrs. Patricia Brown", grade: "B+", credits: 3 },
                        { name: "Physical Education", teacher: "Mr. Michael Johnson", grade: "A", credits: 2 },
                        { name: "Art", teacher: "Ms. Emily Davis", grade: "A-", credits: 2 },
                      ].map((subject, index) => (
                        <div key={index} className="flex items-center justify-between pb-2 border-b last:border-0">
                          <div>
                            <p className="font-medium">{subject.name}</p>
                            <p className="text-sm text-muted-foreground">Teacher: {subject.teacher}</p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">{subject.grade}</p>
                            <p className="text-sm text-muted-foreground">{subject.credits} credits</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="activities" className="space-y-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Extracurricular Activities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        {
                          name: "Basketball Team",
                          role: "Team Captain",
                          schedule: "Mon, Wed, Fri 4-6 PM",
                          advisor: "Coach Thompson",
                        },
                        {
                          name: "Science Club",
                          role: "Member",
                          schedule: "Tuesday 3-4:30 PM",
                          advisor: "Ms. Jennifer Lee",
                        },
                        {
                          name: "Debate Team",
                          role: "Member",
                          schedule: "Thursday 3-5 PM",
                          advisor: "Mr. David Wilson",
                        },
                      ].map((activity, index) => (
                        <div key={index} className="pb-4 border-b last:border-0 last:pb-0">
                          <p className="font-medium">{activity.name}</p>
                          <div className="grid grid-cols-2 gap-2 mt-1">
                            <div>
                              <p className="text-xs text-muted-foreground">Role</p>
                              <p className="text-sm">{activity.role}</p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Schedule</p>
                              <p className="text-sm">{activity.schedule}</p>
                            </div>
                            <div className="col-span-2">
                              <p className="text-xs text-muted-foreground">Advisor</p>
                              <p className="text-sm">{activity.advisor}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Achievements & Awards</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        {
                          name: "First Place - Science Fair",
                          date: "May 2023",
                          description: "Project on Renewable Energy Sources",
                        },
                        {
                          name: "MVP - Basketball Tournament",
                          date: "March 2023",
                          description: "Regional High School Championship",
                        },
                        {
                          name: "Honor Roll",
                          date: "2022-2023 Academic Year",
                          description: "Maintained GPA above 3.5",
                        },
                      ].map((award, index) => (
                        <div key={index} className="flex items-start gap-4 pb-4 border-b last:border-0 last:pb-0">
                          <Award className="h-5 w-5 text-primary mt-0.5" />
                          <div>
                            <p className="font-medium">{award.name}</p>
                            <p className="text-sm text-muted-foreground">{award.date}</p>
                            <p className="text-sm mt-1">{award.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="documents" className="space-y-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Student Documents</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { name: "Birth Certificate", type: "PDF", size: "1.2 MB", uploaded: "Sep 5, 2021" },
                        { name: "Immunization Records", type: "PDF", size: "2.4 MB", uploaded: "Sep 5, 2021" },
                        { name: "Previous School Transcripts", type: "PDF", size: "3.8 MB", uploaded: "Sep 5, 2021" },
                        { name: "Medical Information Form", type: "PDF", size: "1.5 MB", uploaded: "Sep 10, 2021" },
                      ].map((doc, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center">
                            <FileText className="h-8 w-8 text-primary mr-3" />
                            <div>
                              <p className="font-medium">{doc.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {doc.type} • {doc.size} • Uploaded: {doc.uploaded}
                              </p>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              View
                            </Button>
                            <Button variant="outline" size="sm">
                              Download
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full">
                      <FileText className="mr-2 h-4 w-4" />
                      Upload New Document
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
