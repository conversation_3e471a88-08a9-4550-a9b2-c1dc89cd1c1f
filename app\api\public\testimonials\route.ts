import { type NextRequest, NextResponse } from "next/server"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get("category")
    const featured = searchParams.get("featured")

    let url = `${BACKEND_URL}/api/v1/content/testimonials`
    const params = new URLSearchParams()

    if (category) params.append("category", category)
    if (featured) params.append("featured", featured)

    if (params.toString()) {
      url += `?${params.toString()}`
    }

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      next: { revalidate: 300 }, // Cache for 5 minutes
    })

    if (!response.ok) {
      throw new Error("Failed to fetch testimonials")
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching testimonials:", error)
    // Return fallback data
    return NextResponse.json({
      success: true,
      data: [
        {
          id: 1,
          name: "<PERSON>",
          position: "Principal",
          company: "Greenwood Elementary",
          content:
            "EduManage has transformed how we manage our school. The system is intuitive and our teachers love it.",
          rating: 5,
          avatar_url: "/placeholder.svg?height=64&width=64",
          category: "school",
          is_featured: true,
        },
        {
          id: 2,
          name: "Michael Chen",
          position: "IT Director",
          company: "Metro High School",
          content: "The implementation was smooth and the support team is excellent. Highly recommended!",
          rating: 5,
          avatar_url: "/placeholder.svg?height=64&width=64",
          category: "school",
          is_featured: true,
        },
      ],
    })
  }
}
