"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { FileText, Search, Download, TrendingUp, TrendingDown, Minus, BarChart, Pie<PERSON>hart, BookOpen } from "lucide-react"
import { useTenant } from "@/contexts/tenant-context"
import { Progress } from "@/components/ui/progress"

// Mock data for teacher view
const teacherGradesData = {
  classes: [
    { id: "1", name: "Grade 5A", subject: "Mathematics" },
    { id: "2", name: "Grade 6B", subject: "Mathematics" },
    { id: "3", name: "Grade 5C", subject: "Science" },
  ],
  students: [
    {
      id: "1",
      name: "Alice <PERSON>",
      class: "Grade 5A",
      midterm: 85,
      final: 92,
      assignments: 88,
      participation: 95,
      overall: 90,
      trend: "up",
    },
    {
      id: "2",
      name: "Bob Smith",
      class: "Grade 5A",
      midterm: 72,
      final: 78,
      assignments: 80,
      participation: 85,
      overall: 78,
      trend: "up",
    },
    {
      id: "3",
      name: "Charlie Brown",
      class: "Grade 5A",
      midterm: 90,
      final: 88,
      assignments: 92,
      participation: 90,
      overall: 90,
      trend: "same",
    },
    {
      id: "4",
      name: "Diana Miller",
      class: "Grade 5A",
      midterm: 65,
      final: 75,
      assignments: 70,
      participation: 80,
      overall: 72,
      trend: "up",
    },
    {
      id: "5",
      name: "Edward Wilson",
      class: "Grade 5A",
      midterm: 78,
      final: 72,
      assignments: 75,
      participation: 70,
      overall: 74,
      trend: "down",
    },
    {
      id: "6",
      name: "Fiona Garcia",
      class: "Grade 5A",
      midterm: 92,
      final: 95,
      assignments: 90,
      participation: 95,
      overall: 93,
      trend: "up",
    },
  ],
}

// Mock data for student view
const studentGradesData = {
  subjects: [
    {
      id: "1",
      name: "Mathematics",
      teacher: "Mr. John Smith",
      midterm: 85,
      final: 92,
      assignments: 88,
      participation: 95,
      overall: 90,
      grade: "A-",
      trend: "up",
    },
    {
      id: "2",
      name: "Science",
      teacher: "Mrs. Emily Johnson",
      midterm: 78,
      final: 82,
      assignments: 85,
      participation: 90,
      overall: 83,
      grade: "B+",
      trend: "up",
    },
    {
      id: "3",
      name: "English",
      teacher: "Ms. Sarah Williams",
      midterm: 92,
      final: 95,
      assignments: 90,
      participation: 95,
      overall: 93,
      grade: "A",
      trend: "same",
    },
    {
      id: "4",
      name: "History",
      teacher: "Mr. Robert Brown",
      midterm: 75,
      final: 80,
      assignments: 78,
      participation: 85,
      overall: 79,
      grade: "B",
      trend: "up",
    },
    {
      id: "5",
      name: "Physical Education",
      teacher: "Mr. Michael Davis",
      midterm: 90,
      final: 88,
      assignments: 85,
      participation: 95,
      overall: 90,
      grade: "A-",
      trend: "down",
    },
  ],
  assignments: [
    { id: "1", subject: "Mathematics", title: "Algebra Quiz", score: 92, maxScore: 100, date: "Apr 15, 2025" },
    { id: "2", subject: "Mathematics", title: "Geometry Test", score: 85, maxScore: 100, date: "Apr 28, 2025" },
    { id: "3", subject: "Science", title: "Lab Report", score: 88, maxScore: 100, date: "Apr 20, 2025" },
    { id: "4", subject: "Science", title: "Midterm Exam", score: 78, maxScore: 100, date: "Mar 15, 2025" },
    { id: "5", subject: "English", title: "Essay", score: 90, maxScore: 100, date: "Apr 10, 2025" },
    { id: "6", subject: "English", title: "Book Report", score: 95, maxScore: 100, date: "Apr 25, 2025" },
    { id: "7", subject: "History", title: "Research Paper", score: 82, maxScore: 100, date: "Apr 18, 2025" },
    { id: "8", subject: "History", title: "Midterm Exam", score: 75, maxScore: 100, date: "Mar 16, 2025" },
  ],
}

// Mock data for parent view
const parentGradesData = {
  children: [
    { id: "1", name: "Emma Wilson", grade: "Grade 5", class: "5A" },
    { id: "2", name: "Noah Wilson", grade: "Grade 3", class: "3C" },
  ],
  subjects: [
    {
      id: "1",
      name: "Mathematics",
      teacher: "Mr. John Smith",
      midterm: 85,
      final: 92,
      assignments: 88,
      participation: 95,
      overall: 90,
      grade: "A-",
      trend: "up",
    },
    {
      id: "2",
      name: "Science",
      teacher: "Mrs. Emily Johnson",
      midterm: 78,
      final: 82,
      assignments: 85,
      participation: 90,
      overall: 83,
      grade: "B+",
      trend: "up",
    },
    {
      id: "3",
      name: "English",
      teacher: "Ms. Sarah Williams",
      midterm: 92,
      final: 95,
      assignments: 90,
      participation: 95,
      overall: 93,
      grade: "A",
      trend: "same",
    },
    {
      id: "4",
      name: "History",
      teacher: "Mr. Robert Brown",
      midterm: 75,
      final: 80,
      assignments: 78,
      participation: 85,
      overall: 79,
      grade: "B",
      trend: "up",
    },
    {
      id: "5",
      name: "Physical Education",
      teacher: "Mr. Michael Davis",
      midterm: 90,
      final: 88,
      assignments: 85,
      participation: 95,
      overall: 90,
      grade: "A-",
      trend: "down",
    },
  ],
}

function getGradeColor(grade: number) {
  if (grade >= 90) return "text-emerald-600"
  if (grade >= 80) return "text-blue-600"
  if (grade >= 70) return "text-amber-600"
  return "text-red-600"
}

function getGradeLetter(grade: number) {
  if (grade >= 97) return "A+"
  if (grade >= 93) return "A"
  if (grade >= 90) return "A-"
  if (grade >= 87) return "B+"
  if (grade >= 83) return "B"
  if (grade >= 80) return "B-"
  if (grade >= 77) return "C+"
  if (grade >= 73) return "C"
  if (grade >= 70) return "C-"
  if (grade >= 67) return "D+"
  if (grade >= 63) return "D"
  if (grade >= 60) return "D-"
  return "F"
}

function getTrendIcon(trend: string) {
  if (trend === "up") return <TrendingUp className="h-4 w-4 text-emerald-500" />
  if (trend === "down") return <TrendingDown className="h-4 w-4 text-red-500" />
  return <Minus className="h-4 w-4 text-gray-500" />
}

export default function GradesPage() {
  const { currentUser } = useTenant()
  const [isLoading, setIsLoading] = useState(true)
  const [selectedClass, setSelectedClass] = useState("")
  const [selectedChild, setSelectedChild] = useState("")
  const [searchQuery, setSearchQuery] = useState("")
  const [data, setData] = useState<any>({})

  useEffect(() => {
    // Simulate API call to fetch grades data based on user role
    const fetchData = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000))

      if (currentUser?.role === "TEACHER") {
        setData(teacherGradesData)
        setSelectedClass(teacherGradesData.classes[0].id)
      } else if (currentUser?.role === "STUDENT") {
        setData(studentGradesData)
      } else if (currentUser?.role === "PARENT") {
        setData(parentGradesData)
        setSelectedChild(parentGradesData.children[0].id)
      }

      setIsLoading(false)
    }

    fetchData()
  }, [currentUser])

  const isTeacher = currentUser?.role === "TEACHER"
  const isStudent = currentUser?.role === "STUDENT"
  const isParent = currentUser?.role === "PARENT"

  // Filter students based on search query and selected class
  const filteredStudents = isTeacher
    ? data.students?.filter(
        (student: any) =>
          student.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
          (selectedClass ? student.class === data.classes.find((c: any) => c.id === selectedClass)?.name : true),
      )
    : []

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {isTeacher ? "Grade Management" : isParent ? "Children's Grades" : "My Grades"}
          </h1>
          <p className="text-gray-500">
            {isTeacher
              ? "Manage and track student performance"
              : isParent
                ? "Monitor your children's academic performance"
                : "Track your academic performance and progress"}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            {isTeacher ? "Export Grades" : "Download Report"}
          </Button>
          {isTeacher && (
            <Button>
              <FileText className="mr-2 h-4 w-4" />
              Grade Reports
            </Button>
          )}
        </div>
      </div>

      {isTeacher && (
        <div className="flex flex-col md:flex-row gap-4">
          <div className="md:w-1/3">
            <Select value={selectedClass} onValueChange={setSelectedClass}>
              <SelectTrigger>
                <SelectValue placeholder="Select a class" />
              </SelectTrigger>
              <SelectContent>
                {data.classes?.map((classItem: any) => (
                  <SelectItem key={classItem.id} value={classItem.id}>
                    {classItem.name} - {classItem.subject}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="md:w-2/3 relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search students..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      )}

      {isParent && (
        <div className="flex flex-col md:flex-row gap-4">
          <div className="md:w-1/3">
            <Select value={selectedChild} onValueChange={setSelectedChild}>
              <SelectTrigger>
                <SelectValue placeholder="Select a child" />
              </SelectTrigger>
              <SelectContent>
                {data.children?.map((child: any) => (
                  <SelectItem key={child.id} value={child.id}>
                    {child.name} ({child.grade} {child.class})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          {isTeacher && <TabsTrigger value="class-performance">Class Performance</TabsTrigger>}
          {(isStudent || isParent) && <TabsTrigger value="subjects">Subjects</TabsTrigger>}
          {(isStudent || isParent) && <TabsTrigger value="assignments">Assignments</TabsTrigger>}
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <p>Loading grades data...</p>
            </div>
          ) : isTeacher ? (
            <Card>
              <CardHeader>
                <CardTitle>Student Grades</CardTitle>
                <CardDescription>
                  {selectedClass
                    ? `${data.classes.find((c: any) => c.id === selectedClass)?.name} - ${data.classes.find((c: any) => c.id === selectedClass)?.subject}`
                    : "All Classes"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Student</TableHead>
                        <TableHead>Midterm</TableHead>
                        <TableHead>Final</TableHead>
                        <TableHead>Assignments</TableHead>
                        <TableHead>Participation</TableHead>
                        <TableHead>Overall</TableHead>
                        <TableHead>Grade</TableHead>
                        <TableHead>Trend</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredStudents.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center h-24">
                            No students found
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredStudents.map((student: any) => (
                          <TableRow key={student.id}>
                            <TableCell className="font-medium">{student.name}</TableCell>
                            <TableCell>{student.midterm}%</TableCell>
                            <TableCell>{student.final}%</TableCell>
                            <TableCell>{student.assignments}%</TableCell>
                            <TableCell>{student.participation}%</TableCell>
                            <TableCell className={getGradeColor(student.overall)}>
                              <span className="font-medium">{student.overall}%</span>
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  student.overall >= 80 ? "default" : student.overall >= 70 ? "secondary" : "outline"
                                }
                              >
                                {getGradeLetter(student.overall)}
                              </Badge>
                            </TableCell>
                            <TableCell>{getTrendIcon(student.trend)}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle>Overall Performance</CardTitle>
                  <CardDescription>Current academic term</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <div className="text-5xl font-bold text-emerald-600">
                      {isStudent
                        ? `${Math.round(data.subjects?.reduce((acc: number, subj: any) => acc + subj.overall, 0) / data.subjects?.length)}%`
                        : `${Math.round(data.subjects?.reduce((acc: number, subj: any) => acc + subj.overall, 0) / data.subjects?.length)}%`}
                    </div>
                    <div className="text-xl font-semibold">
                      {isStudent
                        ? getGradeLetter(
                            Math.round(
                              data.subjects?.reduce((acc: number, subj: any) => acc + subj.overall, 0) /
                                data.subjects?.length,
                            ),
                          )
                        : getGradeLetter(
                            Math.round(
                              data.subjects?.reduce((acc: number, subj: any) => acc + subj.overall, 0) /
                                data.subjects?.length,
                            ),
                          )}
                    </div>
                    <div className="text-sm text-gray-500 mt-2">
                      {isStudent
                        ? "Your overall grade across all subjects"
                        : `${data.children?.find((c: any) => c.id === selectedChild)?.name}'s overall grade`}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Subject Breakdown</CardTitle>
                  <CardDescription>Performance by subject</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {data.subjects?.slice(0, 5).map((subject: any) => (
                      <div key={subject.id} className="space-y-1">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">{subject.name}</span>
                          <span className={`text-sm font-medium ${getGradeColor(subject.overall)}`}>
                            {subject.overall}% ({subject.grade})
                          </span>
                        </div>
                        <Progress value={subject.overall} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Assessments</CardTitle>
                  <CardDescription>Latest grades and feedback</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {isStudent &&
                      data.assignments?.slice(0, 5).map((assignment: any) => (
                        <div
                          key={assignment.id}
                          className="flex justify-between items-start border-b pb-3 last:border-0"
                        >
                          <div>
                            <p className="font-medium">{assignment.title}</p>
                            <p className="text-sm text-gray-500">{assignment.subject}</p>
                            <p className="text-xs text-gray-500">{assignment.date}</p>
                          </div>
                          <Badge
                            variant={
                              assignment.score >= 90 ? "default" : assignment.score >= 80 ? "secondary" : "outline"
                            }
                          >
                            {assignment.score}/{assignment.maxScore}
                          </Badge>
                        </div>
                      ))}
                    {isParent &&
                      data.subjects?.slice(0, 5).map((subject: any) => (
                        <div key={subject.id} className="flex justify-between items-start border-b pb-3 last:border-0">
                          <div>
                            <p className="font-medium">{subject.name}</p>
                            <p className="text-sm text-gray-500">Teacher: {subject.teacher}</p>
                          </div>
                          <div className="flex items-center">
                            {getTrendIcon(subject.trend)}
                            <Badge
                              className="ml-2"
                              variant={
                                subject.overall >= 90 ? "default" : subject.overall >= 80 ? "secondary" : "outline"
                              }
                            >
                              {subject.grade}
                            </Badge>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {(isStudent || isParent) && (
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Trends</CardTitle>
                  <CardDescription>Academic progress over time</CardDescription>
                </CardHeader>
                <CardContent className="h-80 flex items-center justify-center">
                  <div className="text-center text-gray-500 flex flex-col items-center">
                    <BarChart className="h-16 w-16 mb-2 text-gray-400" />
                    <p>Performance chart will be displayed here</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Grade Distribution</CardTitle>
                  <CardDescription>Breakdown of grades by category</CardDescription>
                </CardHeader>
                <CardContent className="h-80 flex items-center justify-center">
                  <div className="text-center text-gray-500 flex flex-col items-center">
                    <PieChart className="h-16 w-16 mb-2 text-gray-400" />
                    <p>Grade distribution chart will be displayed here</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        {isTeacher && (
          <TabsContent value="class-performance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Class Performance Analytics</CardTitle>
                <CardDescription>Overall performance metrics for the selected class</CardDescription>
              </CardHeader>
              <CardContent className="h-80 flex items-center justify-center">
                <div className="text-center text-gray-500 flex flex-col items-center">
                  <BarChart className="h-16 w-16 mb-2 text-gray-400" />
                  <p>Class performance analytics will be displayed here</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {(isStudent || isParent) && (
          <TabsContent value="subjects" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Subject Performance</CardTitle>
                <CardDescription>
                  {isStudent
                    ? "Detailed breakdown of your performance by subject"
                    : `Detailed breakdown of ${data.children?.find((c: any) => c.id === selectedChild)?.name}'s performance by subject`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Subject</TableHead>
                        <TableHead>Teacher</TableHead>
                        <TableHead>Midterm</TableHead>
                        <TableHead>Final</TableHead>
                        <TableHead>Assignments</TableHead>
                        <TableHead>Participation</TableHead>
                        <TableHead>Overall</TableHead>
                        <TableHead>Grade</TableHead>
                        <TableHead>Trend</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {data.subjects?.map((subject: any) => (
                        <TableRow key={subject.id}>
                          <TableCell className="font-medium">{subject.name}</TableCell>
                          <TableCell>{subject.teacher}</TableCell>
                          <TableCell>{subject.midterm}%</TableCell>
                          <TableCell>{subject.final}%</TableCell>
                          <TableCell>{subject.assignments}%</TableCell>
                          <TableCell>{subject.participation}%</TableCell>
                          <TableCell className={getGradeColor(subject.overall)}>
                            <span className="font-medium">{subject.overall}%</span>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                subject.overall >= 90 ? "default" : subject.overall >= 80 ? "secondary" : "outline"
                              }
                            >
                              {subject.grade}
                            </Badge>
                          </TableCell>
                          <TableCell>{getTrendIcon(subject.trend)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {(isStudent || isParent) && (
          <TabsContent value="assignments" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Assignment Grades</CardTitle>
                <CardDescription>
                  {isStudent
                    ? "All your graded assignments and assessments"
                    : `${data.children?.find((c: any) => c.id === selectedChild)?.name}'s graded assignments and assessments`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Subject</TableHead>
                        <TableHead>Assignment</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Score</TableHead>
                        <TableHead>Grade</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isStudent ? (
                        data.assignments?.map((assignment: any) => (
                          <TableRow key={assignment.id}>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <BookOpen className="h-4 w-4 text-gray-500" />
                                <span>{assignment.subject}</span>
                              </div>
                            </TableCell>
                            <TableCell className="font-medium">{assignment.title}</TableCell>
                            <TableCell>{assignment.date}</TableCell>
                            <TableCell>
                              {assignment.score}/{assignment.maxScore}
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  assignment.score >= 90 ? "default" : assignment.score >= 80 ? "secondary" : "outline"
                                }
                              >
                                {getGradeLetter(assignment.score)}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center h-24">
                            Select a child to view their assignments
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{isTeacher ? "Grade Reports" : isParent ? "Progress Reports" : "Academic Reports"}</CardTitle>
              <CardDescription>
                {isTeacher
                  ? "Generate and download grade reports"
                  : isParent
                    ? "View and download your children's progress reports"
                    : "View and download your academic reports"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {["Term 1 Report", "Midterm Evaluation", "Progress Report", "Final Term Report"].map((report, i) => (
                  <div key={i} className="flex items-center justify-between border-b pb-4 last:border-0">
                    <div className="flex items-center gap-3">
                      <div className="bg-blue-100 text-blue-700 p-2 rounded">
                        <FileText className="h-5 w-5" />
                      </div>
                      <div>
                        <p className="font-medium">{report}</p>
                        <p className="text-sm text-gray-500">
                          {["January 2025", "March 2025", "April 2025", "June 2025"][i]}
                        </p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      Download
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
