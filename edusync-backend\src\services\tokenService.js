const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const { PrismaClient } = require('@prisma/client');
const AppError = require('../utils/appError');
const config = require('../config');

const prisma = new PrismaClient();

/**
 * Generate JWT access and refresh tokens
 * @param {Object} user - User object
 * @returns {Object} Access and refresh tokens
 */
const generateTokens = async (user) => {
  // Generate access token
  const accessToken = jwt.sign(
    {
      userId: user.id,
      email: user.email,
      role: user.role,
    },
    config.jwt.access_secret,
    {
      expiresIn: config.jwt.access_expiration,
    }
  );

  // Generate refresh token
  const refreshToken = uuidv4();
  const refreshTokenExpiration = new Date(
    Date.now() + 7 * 24 * 60 * 60 * 1000 // 7 days
  );

  // Store refresh token in database
  await prisma.refreshToken.create({
    data: {
      token: refreshToken,
      userId: user.id,
      expiresAt: refreshTokenExpiration,
    },
  });

  return {
    accessToken,
    refreshToken,
  };
};

/**
 * Verify refresh token
 * @param {string} token - Refresh token
 * @returns {Object} User ID from token
 */
const verifyRefreshToken = async (token) => {
  // Find refresh token in database
  const refreshToken = await prisma.refreshToken.findFirst({
    where: {
      token,
      expiresAt: {
        gt: new Date(),
      },
      isRevoked: false,
    },
  });

  if (!refreshToken) {
    throw new AppError('Invalid or expired refresh token', 401);
  }

  return { userId: refreshToken.userId };
};

/**
 * Create a one-time token for operations like email verification
 * @param {string} type - Token type
 * @returns {string} Generated token
 */
const createToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

module.exports = {
  generateTokens,
  verifyRefreshToken,
  createToken
};
