"use client"

import type React from "react"

import { UserCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useOnboarding } from "@/contexts/onboarding-context"

export function AdminProfileStep() {
  const { onboardingData, updateOnboardingData, nextStep, prevStep } = useOnboarding()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    nextStep()
  }

  return (
    <Card className="border-0 shadow-none">
      <CardHeader>
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100">
          <UserCircle className="h-6 w-6 text-emerald-600" />
        </div>
        <CardTitle className="text-center text-2xl">Administrator Profile</CardTitle>
        <CardDescription className="text-center">Set up your administrator account details</CardDescription>
      </CardHeader>
      <CardContent>
        <form id="admin-form" onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                placeholder="e.g., John"
                value={onboardingData.firstName}
                onChange={(e) => updateOnboardingData({ firstName: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                placeholder="e.g., Doe"
                value={onboardingData.lastName}
                onChange={(e) => updateOnboardingData({ lastName: e.target.value })}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="e.g., <EMAIL>"
              value={onboardingData.email}
              onChange={(e) => updateOnboardingData({ email: e.target.value })}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              type="tel"
              placeholder="e.g., +260 97 1234567"
              value={onboardingData.phone}
              onChange={(e) => updateOnboardingData({ phone: e.target.value })}
              required
            />
          </div>

          <div className="rounded-lg bg-muted p-4">
            <p className="text-sm font-medium">Important Note</p>
            <p className="text-xs text-muted-foreground">
              This account will have full administrative privileges for your institution. You'll be able to add more
              administrators later.
            </p>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={prevStep}>
          Back
        </Button>
        <Button type="submit" form="admin-form">
          Continue
        </Button>
      </CardFooter>
    </Card>
  )
}
