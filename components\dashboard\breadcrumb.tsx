"use client"

import { usePathname } from "next/navigation"
import { Breadcrumb } from "@/components/ui/breadcrumb"

export function DashboardBreadcrumb({ className }: { className?: string }) {
  const pathname = usePathname()

  // Skip rendering breadcrumbs on the main dashboard page
  if (pathname === "/dashboard") {
    return null
  }

  // Create breadcrumb segments from the pathname
  const segments = pathname
    .split("/")
    .filter(Boolean)
    .slice(1) // Remove "dashboard" from the segments array
    .map((segment, index, array) => {
      // Create the href for this segment
      const href = "/" + ["dashboard", ...array.slice(0, index + 1)].join("/")

      // Format the segment title (capitalize and replace hyphens with spaces)
      const title = segment
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")

      return { title, href }
    })

  return <Breadcrumb segments={segments} homeHref="/dashboard" className={className} />
}
