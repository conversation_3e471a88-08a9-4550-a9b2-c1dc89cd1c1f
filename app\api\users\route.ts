import { NextResponse } from "next/server"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Get auth token from cookies or headers
    const authHeader = request.headers.get("authorization")
    const cookieHeader = request.headers.get("cookie")

    let authToken = null

    // Try to get from Authorization header first
    if (authHeader && authHeader.startsWith("Bearer ")) {
      authToken = authHeader.replace("Bearer ", "")
    }
    // Try to get from cookies
    else if (cookieHeader) {
      const cookies = cookieHeader.split(';').map(c => c.trim())
      const accessTokenCookie = cookies.find(c => c.startsWith('accessToken='))
      if (accessTokenCookie) {
        authToken = accessTokenCookie.split('=')[1]
      }
    }

    console.log("🔍 Users API: Auth token found:", authToken ? "✅ Yes" : "❌ No")
    console.log("🔍 Users API: Cookie header:", cookieHeader ? "✅ Present" : "❌ Missing")

    if (!authToken) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    // Forward the request to the backend API
    const response = await fetch(`${BACKEND_URL}/api/v1/users`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${authToken}`,
      },
      body: JSON.stringify(body),
    })

    const responseData = await response.json()

    if (!response.ok) {
      return NextResponse.json(responseData, { status: response.status })
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error("Users API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())

    // Get auth token from cookies or headers
    const authHeader = request.headers.get("authorization")
    const cookieHeader = request.headers.get("cookie")

    let authToken = null

    // Try to get from Authorization header first
    if (authHeader && authHeader.startsWith("Bearer ")) {
      authToken = authHeader.replace("Bearer ", "")
    }
    // Try to get from cookies
    else if (cookieHeader) {
      const cookies = cookieHeader.split(';').map(c => c.trim())
      const accessTokenCookie = cookies.find(c => c.startsWith('accessToken='))
      if (accessTokenCookie) {
        authToken = accessTokenCookie.split('=')[1]
      }
    }

    if (!authToken) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    // Build query string
    const queryString = new URLSearchParams(queryParams).toString()

    // Forward the request to the backend API
    const response = await fetch(`${BACKEND_URL}/api/v1/users${queryString ? `?${queryString}` : ''}`, {
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${authToken}`,
      },
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Users API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
