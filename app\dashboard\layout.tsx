"use client"

import type React from "react"
import { <PERSON><PERSON> } from "@/components/dashboard/header"
import { Sidebar } from "@/components/dashboard/sidebar"
import { ToastContainer } from "@/components/notifications/toast-container"
import { DashboardBreadcrumb } from "@/components/dashboard/breadcrumb"
import { ProtectedRoute } from "@/components/auth/protected-route"

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute>
      <div className="flex min-h-screen flex-col">
        <Header />
        <div className="flex flex-1 relative">
          <div className="fixed left-0 top-16 h-[calc(100vh-4rem)] w-64 hidden md:block">
            <Sidebar className="h-full" />
          </div>
          <main className="flex-1 p-6 md:ml-64">
            <DashboardBreadcrumb className="mb-6" />
            {children}
          </main>
        </div>
        <ToastContainer />
      </div>
    </ProtectedRoute>
  )
}
