// Use built-in fetch (Node.js 18+) or require http module
const http = require('http');

async function testClassAPI() {
  try {
    console.log('🔍 Testing Classes API endpoint...');
    
    // Test GET endpoint (should work without auth for testing)
    console.log('\n📥 Testing GET /api/v1/classes...');
    const getResponse = await fetch('http://localhost:4000/api/v1/classes');
    console.log(`   Status: ${getResponse.status}`);
    
    if (getResponse.status === 401) {
      console.log('   ✅ GET endpoint requires authentication (expected)');
    } else if (getResponse.status === 200) {
      const getData = await getResponse.json();
      console.log('   ✅ GET endpoint works!');
      console.log(`   Classes found: ${getData.data?.classes?.length || 0}`);
    } else {
      console.log(`   ❌ Unexpected status: ${getResponse.status}`);
    }
    
    // Test POST endpoint
    console.log('\n📤 Testing POST /api/v1/classes...');
    const postData = {
      name: 'API Test Class',
      gradeLevel: '3',
      section: 'C',
      capacity: 25,
      roomNumber: '303'
    };
    
    const postResponse = await fetch('http://localhost:4000/api/v1/classes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(postData)
    });
    
    console.log(`   Status: ${postResponse.status}`);
    
    if (postResponse.status === 401) {
      console.log('   ✅ POST endpoint requires authentication (expected)');
    } else if (postResponse.status === 201) {
      const responseData = await postResponse.json();
      console.log('   ✅ POST endpoint works!');
      console.log(`   Created class: ${responseData.data?.name}`);
    } else {
      console.log(`   ❌ Unexpected status: ${postResponse.status}`);
      const errorData = await postResponse.text();
      console.log(`   Error: ${errorData}`);
    }
    
    console.log('\n🎯 API endpoint test completed!');
    
  } catch (error) {
    console.error('❌ Error testing API:', error);
  }
}

testClassAPI();
