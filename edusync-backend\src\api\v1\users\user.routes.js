const express = require('express');
const { body, param } = require('express-validator');
const userController = require('./user.controller');
const { validateRequest } = require('../../../middleware/validateRequest');
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const router = express.Router();

/**
 * @route GET /api/v1/users
 * @desc Get all users (with pagination and filtering)
 * @access Private (Admin)
 */
router.get(
  '/',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'),
  userController.getUsers
);

/**
 * @route GET /api/v1/users/:id
 * @desc Get user by ID
 * @access Private (Admin or own user)
 */
router.get(
  '/:id',
  authenticate,
  param('id').isUUID().withMessage('Invalid user ID'),
  validateRequest,
  userController.getUserById
);

/**
 * @route POST /api/v1/users
 * @desc Create new user
 * @access Private (Admin)
 */
router.post(
  '/',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'),
  [
    body('email').isEmail().withMessage('Please provide a valid email'),
    body('password')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*])/)
      .withMessage('Password must include one lowercase, one uppercase, one number, and one special character'),
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName').notEmpty().withMessage('Last name is required'),
    body('role').isIn(['INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER', 'STUDENT', 'PARENT', 'STAFF'])
      .withMessage('Invalid role provided'),
    validateRequest,
  ],
  userController.createUser
);

/**
 * @route PATCH /api/v1/users/:id
 * @desc Update user
 * @access Private (Admin or own user)
 */
router.patch(
  '/:id',
  authenticate,
  param('id').isUUID().withMessage('Invalid user ID'),
  [
    body('email').optional().isEmail().withMessage('Please provide a valid email'),
    body('firstName').optional().notEmpty().withMessage('First name cannot be empty'),
    body('lastName').optional().notEmpty().withMessage('Last name cannot be empty'),
    body('role').optional().isIn(['INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER', 'STUDENT', 'PARENT', 'STAFF'])
      .withMessage('Invalid role provided'),
    body('phoneNumber').optional(),
    body('profileImageUrl').optional(),
    body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
    validateRequest,
  ],
  userController.updateUser
);

/**
 * @route DELETE /api/v1/users/:id
 * @desc Delete user
 * @access Private (Admin)
 */
router.delete(
  '/:id',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'),
  param('id').isUUID().withMessage('Invalid user ID'),
  validateRequest,
  userController.deleteUser
);

/**
 * @route POST /api/v1/users/:id/institution
 * @desc Add user to institution
 * @access Private (Admin)
 */
router.post(
  '/:id/institution',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN'),
  param('id').isUUID().withMessage('Invalid user ID'),
  [
    body('institutionId').isUUID().withMessage('Invalid institution ID'),
    body('role').isIn(['INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER', 'STUDENT', 'PARENT', 'STAFF'])
      .withMessage('Invalid role provided'),
    validateRequest,
  ],
  userController.addUserToInstitution
);

/**
 * @route POST /api/v1/users/:id/school
 * @desc Add user to school
 * @access Private (Admin)
 */
router.post(
  '/:id/school',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'),
  param('id').isUUID().withMessage('Invalid user ID'),
  [
    body('schoolId').isUUID().withMessage('Invalid school ID'),
    body('role').isIn(['SCHOOL_ADMIN', 'TEACHER', 'STUDENT', 'PARENT', 'STAFF'])
      .withMessage('Invalid role provided'),
    validateRequest,
  ],
  userController.addUserToSchool
);

/**
 * @route GET /api/v1/users/institution/:institutionId
 * @desc Get users by institution
 * @access Private (Admin)
 */
router.get(
  '/institution/:institutionId',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'),
  param('institutionId').isUUID().withMessage('Invalid institution ID'),
  validateRequest,
  userController.getUsersByInstitution
);

/**
 * @route GET /api/v1/users/school/:schoolId
 * @desc Get users by school
 * @access Private (Admin)
 */
router.get(
  '/school/:schoolId',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'),
  param('schoolId').isUUID().withMessage('Invalid school ID'),
  validateRequest,
  userController.getUsersBySchool
);

/**
 * @route DELETE /api/v1/users/:id/institution/:institutionId
 * @desc Remove user from institution
 * @access Private (Admin)
 */
router.delete(
  '/:id/institution/:institutionId',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN'),
  param('id').isUUID().withMessage('Invalid user ID'),
  param('institutionId').isUUID().withMessage('Invalid institution ID'),
  validateRequest,
  userController.removeUserFromInstitution
);

/**
 * @route DELETE /api/v1/users/:id/school/:schoolId
 * @desc Remove user from school
 * @access Private (Admin)
 */
router.delete(
  '/:id/school/:schoolId',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'),
  param('id').isUUID().withMessage('Invalid user ID'),
  param('schoolId').isUUID().withMessage('Invalid school ID'),
  validateRequest,
  userController.removeUserFromSchool
);

module.exports = router;
