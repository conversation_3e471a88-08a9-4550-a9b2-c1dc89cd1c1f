"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>ooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { School, Clock, Calendar, FileText, ChevronRight } from "lucide-react"
import { useTenant } from "@/contexts/tenant-context"
import { Progress } from "@/components/ui/progress"

// Mock data for classes
const teacherClasses = [
  {
    id: "1",
    name: "Grade 5A",
    subject: "Mathematics",
    students: 32,
    schedule: "Mon, Wed, Fri 9:00 - 10:30 AM",
    room: "Room 101",
    progress: 65,
    nextLesson: "Fractions and Decimals",
    nextDate: "May 24, 2025",
  },
  {
    id: "2",
    name: "Grade 6B",
    subject: "Mathematics",
    students: 28,
    schedule: "<PERSON><PERSON>, <PERSON>hu 11:00 AM - 12:30 PM",
    room: "Room 105",
    progress: 72,
    nextLesson: "Introduction to Algebra",
    nextDate: "May 23, 2025",
  },
  {
    id: "3",
    name: "Grade 5C",
    subject: "Science",
    students: 30,
    schedule: "Mon, Wed 1:00 - 2:30 PM",
    room: "Lab 2",
    progress: 58,
    nextLesson: "Plant Life Cycle",
    nextDate: "May 24, 2025",
  },
]

const studentClasses = [
  {
    id: "1",
    name: "Mathematics",
    teacher: "Mr. John Smith",
    schedule: "Mon, Wed, Fri 9:00 - 10:30 AM",
    room: "Room 101",
    grade: "A-",
    attendance: 95,
    nextAssignment: "Algebra Quiz",
    dueDate: "May 26, 2025",
  },
  {
    id: "2",
    name: "Science",
    teacher: "Mrs. Emily Johnson",
    schedule: "Tue, Thu 11:00 AM - 12:30 PM",
    room: "Lab 2",
    grade: "B+",
    attendance: 92,
    nextAssignment: "Ecosystem Project",
    dueDate: "May 30, 2025",
  },
  {
    id: "3",
    name: "English",
    teacher: "Ms. Sarah Williams",
    schedule: "Mon, Wed 1:00 - 2:30 PM",
    room: "Room 203",
    grade: "A",
    attendance: 98,
    nextAssignment: "Essay Submission",
    dueDate: "May 28, 2025",
  },
  {
    id: "4",
    name: "History",
    teacher: "Mr. Robert Brown",
    schedule: "Tue, Fri 2:30 - 4:00 PM",
    room: "Room 105",
    grade: "B",
    attendance: 90,
    nextAssignment: "Research Paper",
    dueDate: "June 5, 2025",
  },
]

export default function MyClassesPage() {
  const { currentUser } = useTenant()
  const [isLoading, setIsLoading] = useState(true)
  const [classes, setClasses] = useState([])

  useEffect(() => {
    // Simulate API call to fetch classes based on user role
    const fetchClasses = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000))

      if (currentUser?.role === "TEACHER") {
        setClasses(teacherClasses)
      } else if (currentUser?.role === "STUDENT") {
        setClasses(studentClasses)
      }

      setIsLoading(false)
    }

    fetchClasses()
  }, [currentUser])

  const isTeacher = currentUser?.role === "TEACHER"
  const isStudent = currentUser?.role === "STUDENT"

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Classes</h1>
          <p className="text-gray-500">
            {isTeacher
              ? "Manage your classes, lessons, and student progress"
              : "View your enrolled classes, assignments, and grades"}
          </p>
        </div>
        <div className="flex gap-2">
          {isTeacher && (
            <Button>
              <FileText className="mr-2 h-4 w-4" />
              Lesson Plans
            </Button>
          )}
          {isStudent && (
            <Button>
              <Calendar className="mr-2 h-4 w-4" />
              My Schedule
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="current" className="space-y-4">
        <TabsList>
          <TabsTrigger value="current">Current Classes</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming Lessons</TabsTrigger>
          {isTeacher && <TabsTrigger value="materials">Teaching Materials</TabsTrigger>}
          {isStudent && <TabsTrigger value="assignments">Assignments</TabsTrigger>}
        </TabsList>

        <TabsContent value="current" className="space-y-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <p>Loading classes...</p>
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {classes.map((classItem) => (
                <Card key={classItem.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg font-bold">{isTeacher ? classItem.name : classItem.name}</CardTitle>
                      <Badge variant="outline" className="ml-2">
                        {isTeacher ? classItem.subject : ""}
                      </Badge>
                    </div>
                    <CardDescription>
                      {isTeacher ? `${classItem.students} students` : `Teacher: ${classItem.teacher}`}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <Clock className="h-4 w-4 mr-2 mt-0.5 text-gray-500" />
                        <span className="text-sm">{classItem.schedule}</span>
                      </div>
                      <div className="flex items-start">
                        <School className="h-4 w-4 mr-2 mt-0.5 text-gray-500" />
                        <span className="text-sm">{classItem.room}</span>
                      </div>
                      {isTeacher && (
                        <div className="pt-2">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-sm font-medium">Curriculum Progress</span>
                            <span className="text-sm font-medium">{classItem.progress}%</span>
                          </div>
                          <Progress value={classItem.progress} className="h-2" />
                        </div>
                      )}
                      {isStudent && (
                        <div className="pt-2">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-sm font-medium">Current Grade</span>
                            <Badge
                              variant={
                                classItem.grade.startsWith("A")
                                  ? "default"
                                  : classItem.grade.startsWith("B")
                                    ? "secondary"
                                    : "outline"
                              }
                            >
                              {classItem.grade}
                            </Badge>
                          </div>
                          <div className="flex justify-between items-center mt-2">
                            <span className="text-sm font-medium">Attendance</span>
                            <span className="text-sm font-medium">{classItem.attendance}%</span>
                          </div>
                          <Progress value={classItem.attendance} className="h-2 mt-1" />
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between pt-2">
                    <div className="text-sm text-gray-500">
                      <p className="font-medium">Next {isTeacher ? "Lesson" : "Assignment"}:</p>
                      <p>{isTeacher ? classItem.nextLesson : classItem.nextAssignment}</p>
                      <p>{isTeacher ? classItem.nextDate : `Due: ${classItem.dueDate}`}</p>
                    </div>
                    <Button variant="ghost" size="sm" asChild className="ml-auto">
                      <Link href={`/dashboard/my-classes/${classItem.id}`}>
                        Details <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming {isTeacher ? "Lessons" : "Classes"}</CardTitle>
              <CardDescription>Your scheduled {isTeacher ? "lessons" : "classes"} for the next 7 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="flex items-center justify-between border-b pb-4 last:border-0">
                    <div className="flex items-start gap-3">
                      <div className="bg-emerald-100 text-emerald-700 px-2 py-1 rounded text-xs font-medium w-20 text-center">
                        {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"][i - 1]}
                      </div>
                      <div>
                        <p className="font-medium">
                          {isTeacher
                            ? [
                                "Grade 5A Math",
                                "Grade 6B Math",
                                "Grade 5C Science",
                                "Grade 6A Math",
                                "Grade 5B Science",
                              ][i - 1]
                            : ["Mathematics", "Science", "English", "History", "Physical Education"][i - 1]}
                        </p>
                        <p className="text-sm text-gray-500">
                          {
                            [
                              "9:00 - 10:30 AM",
                              "11:00 AM - 12:30 PM",
                              "1:00 - 2:30 PM",
                              "2:30 - 4:00 PM",
                              "9:00 - 10:30 AM",
                            ][i - 1]
                          }
                        </p>
                        <p className="text-sm text-gray-500">
                          {["Room 101", "Room 105", "Lab 2", "Room 203", "Sports Field"][i - 1]}
                        </p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {isTeacher && (
          <TabsContent value="materials" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Teaching Materials</CardTitle>
                <CardDescription>Access and manage your teaching resources</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {["Lesson Plans", "Worksheets", "Presentations", "Assessment Materials", "Reference Materials"].map(
                    (category, i) => (
                      <div key={i} className="flex items-center justify-between border-b pb-4 last:border-0">
                        <div className="flex items-center gap-3">
                          <div className="bg-blue-100 text-blue-700 p-2 rounded">
                            <FileText className="h-5 w-5" />
                          </div>
                          <div>
                            <p className="font-medium">{category}</p>
                            <p className="text-sm text-gray-500">{[15, 24, 8, 12, 6][i]} files</p>
                          </div>
                        </div>
                        <Button variant="outline" size="sm">
                          Browse
                        </Button>
                      </div>
                    ),
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {isStudent && (
          <TabsContent value="assignments" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Pending Assignments</CardTitle>
                <CardDescription>Track your upcoming assignments and deadlines</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { subject: "Mathematics", title: "Algebra Quiz", due: "May 26, 2025", status: "Not Started" },
                    { subject: "Science", title: "Ecosystem Project", due: "May 30, 2025", status: "In Progress" },
                    { subject: "English", title: "Essay Submission", due: "May 28, 2025", status: "In Progress" },
                    { subject: "History", title: "Research Paper", due: "June 5, 2025", status: "Not Started" },
                    { subject: "Physical Education", title: "Fitness Log", due: "May 25, 2025", status: "Almost Done" },
                  ].map((assignment, i) => (
                    <div key={i} className="flex items-center justify-between border-b pb-4 last:border-0">
                      <div className="flex items-start gap-3">
                        <div className="bg-amber-100 text-amber-700 px-2 py-1 rounded text-xs font-medium mt-1">
                          {assignment.subject}
                        </div>
                        <div>
                          <p className="font-medium">{assignment.title}</p>
                          <p className="text-sm text-gray-500">Due: {assignment.due}</p>
                          <Badge
                            variant={
                              assignment.status === "Not Started"
                                ? "outline"
                                : assignment.status === "Almost Done"
                                  ? "default"
                                  : "secondary"
                            }
                            className="mt-1"
                          >
                            {assignment.status}
                          </Badge>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        View
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}
