// Load environment variables first
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

/**
 * Configuration settings loaded from environment variables
 */
const config = {
  // Server configuration
  node_env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '4000', 10),
  
  // Database
  database_url: process.env.DATABASE_URL,
  
  // JWT configuration
  jwt: {
    access_secret: process.env.JWT_ACCESS_SECRET,
    refresh_secret: process.env.JWT_REFRESH_SECRET,
    access_expiration: process.env.JWT_ACCESS_EXPIRATION || '15m',
    refresh_expiration: process.env.JWT_REFRESH_EXPIRATION || '7d',
  },
  
  // Email configuration
  email: {
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT || '587', 10),
    use_tls: process.env.EMAIL_USE_TLS === 'True',
    user: process.env.EMAIL_USER,
    password: process.env.EMAIL_PASSWORD,
    from: process.env.EMAIL_FROM,
  },
  
  // Frontend URL (for email links, etc.)
  frontend_url: process.env.FRONTEND_URL || 'http://localhost:3000',
  
  // Backend URL (for email verification links)
  backend_url: process.env.BACKEND_URL || `http://localhost:${process.env.PORT || '4000'}`,
  
  // General app settings
  app_name: process.env.APP_NAME || 'EduSync',
};

module.exports = config;
