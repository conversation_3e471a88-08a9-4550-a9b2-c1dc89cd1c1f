import { type NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  try {
    // Get auth token from cookies or headers
    const authToken =
      request.cookies.get("accessToken")?.value || request.headers.get("authorization")?.replace("Bearer ", "")

    if (!authToken) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    // Forward request to Flask backend
    const backendUrl = process.env.BACKEND_URL || "http://localhost:4000"
    const response = await fetch(`${backendUrl}/api/v1/auth/me`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      if (response.status === 401) {
        return NextResponse.json({ error: "Invalid or expired token" }, { status: 401 })
      }
      throw new Error("Failed to fetch user data")
    }

    const userData = await response.json()
    return NextResponse.json(userData)
  } catch (error) {
    console.error("Error fetching current user:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get auth token from cookies or headers
    const authToken =
      request.cookies.get("accessToken")?.value || request.headers.get("authorization")?.replace("Bearer ", "")

    if (!authToken) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    // Get the request body
    const updateData = await request.json()

    // Forward request to Flask backend
    const backendUrl = process.env.BACKEND_URL || "http://localhost:4000"
    const response = await fetch(`${backendUrl}/api/v1/auth/me`, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateData),
    })

    if (!response.ok) {
      if (response.status === 401) {
        return NextResponse.json({ error: "Invalid or expired token" }, { status: 401 })
      }
      if (response.status === 400) {
        const errorData = await response.json()
        return NextResponse.json({ error: errorData.message || "Invalid data" }, { status: 400 })
      }
      throw new Error("Failed to update user profile")
    }

    const userData = await response.json()
    return NextResponse.json(userData)
  } catch (error) {
    console.error("Error updating user profile:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
