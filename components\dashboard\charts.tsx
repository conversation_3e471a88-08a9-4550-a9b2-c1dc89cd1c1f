"use client"

import { Bar, Line } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  type ChartOptions,
} from "chart.js"

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, Filler)

export function LineChart() {
  const data = {
    labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
    datasets: [
      {
        label: "Students",
        data: [1150, 1170, 1180, 1200, 1220, 1210, 1200, 1220, 1240, 1250, 1260, 1250],
        borderColor: "rgb(16, 185, 129)",
        backgroundColor: "rgba(16, 185, 129, 0.1)",
        tension: 0.3,
        fill: true,
      },
    ],
  }

  const options: ChartOptions<"line"> = {
    responsive: true,
    plugins: {
      legend: {
        position: "top" as const,
      },
    },
    scales: {
      y: {
        beginAtZero: false,
        min: 1100,
      },
    },
    maintainAspectRatio: false,
  }

  return (
    <div style={{ height: "300px" }}>
      <Line data={data} options={options} />
    </div>
  )
}

export function BarChart() {
  const data = {
    labels: ["Grade 1", "Grade 2", "Grade 3", "Grade 4", "Grade 5", "Grade 6"],
    datasets: [
      {
        label: "Average Score (%)",
        data: [78, 82, 75, 85, 76, 80],
        backgroundColor: "rgba(16, 185, 129, 0.8)",
      },
    ],
  }

  const options: ChartOptions<"bar"> = {
    responsive: true,
    plugins: {
      legend: {
        position: "top" as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
      },
    },
    maintainAspectRatio: false,
  }

  return (
    <div style={{ height: "300px" }}>
      <Bar data={data} options={options} />
    </div>
  )
}
