"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Users, 
  GraduationCap, 
  Building2, 
  Calendar, 
  TrendingUp, 
  TrendingDown,
  BookOpen,
  Clock,
  DollarSign,
  FileText,
  CheckCircle,
  AlertCircle
} from "lucide-react"

interface Institution {
  id: string
  name: string
  domain?: string
  logo?: string
  primaryColor?: string
  secondaryColor?: string
  isActive: boolean
  subscriptionStatus: string
  subscriptionEndDate?: string
  address?: string
  city?: string
  state?: string
  country?: string
  postalCode?: string
  phoneNumber?: string
  email?: string
  website?: string
  verificationStatus: boolean
  studentCount?: number
  teacherCount?: number
  referralSource?: string
  specialRequirements?: string
  createdAt: string
  updatedAt: string
}

interface InstitutionStatisticsProps {
  institution: Institution
}

export function InstitutionStatistics({ institution }: InstitutionStatisticsProps) {
  // Mock data for demonstration - in a real app, this would come from the backend
  const stats = {
    totalStudents: institution.studentCount || 1245,
    totalTeachers: institution.teacherCount || 87,
    totalSchools: 3,
    totalClasses: 45,
    attendanceRate: 94.2,
    academicPerformance: 87.5,
    subscriptionUsage: 65,
    storageUsed: 2.4, // GB
    storageLimit: 10, // GB
    lastBackup: "2024-01-15T10:30:00Z",
    activeUsers: 156,
    totalUsers: 180,
  }

  const getSubscriptionStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "trial":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "expired":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      case "suspended":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    })
  }

  const getDaysUntilExpiry = () => {
    if (!institution.subscriptionEndDate) return null
    const endDate = new Date(institution.subscriptionEndDate)
    const today = new Date()
    const diffTime = endDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const daysUntilExpiry = getDaysUntilExpiry()

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalStudents.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 text-green-500 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Teachers</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTeachers}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 text-green-500 mr-1" />
              +3% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Schools</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalSchools}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 text-green-500 mr-1" />
              +1 this year
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Classes</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalClasses}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 text-green-500 mr-1" />
              +5% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Academic Performance</CardTitle>
            <CardDescription>Overall student performance metrics</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Attendance Rate</span>
                <span className="font-medium">{stats.attendanceRate}%</span>
              </div>
              <Progress value={stats.attendanceRate} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Academic Performance</span>
                <span className="font-medium">{stats.academicPerformance}%</span>
              </div>
              <Progress value={stats.academicPerformance} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Usage</CardTitle>
            <CardDescription>Platform utilization and storage</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Subscription Usage</span>
                <span className="font-medium">{stats.subscriptionUsage}%</span>
              </div>
              <Progress value={stats.subscriptionUsage} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Storage Used</span>
                <span className="font-medium">{stats.storageUsed}GB / {stats.storageLimit}GB</span>
              </div>
              <Progress value={(stats.storageUsed / stats.storageLimit) * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Subscription & Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Subscription Status</CardTitle>
            <CardDescription>Current subscription details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Status</span>
              <Badge className={getSubscriptionStatusColor(institution.subscriptionStatus)}>
                {institution.subscriptionStatus}
              </Badge>
            </div>
            
            {institution.subscriptionEndDate && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Expires</span>
                <span className="text-sm text-muted-foreground">
                  {formatDate(institution.subscriptionEndDate)}
                </span>
              </div>
            )}
            
            {daysUntilExpiry !== null && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Days Remaining</span>
                <span className={`text-sm font-medium ${
                  daysUntilExpiry <= 30 ? "text-red-600" : 
                  daysUntilExpiry <= 90 ? "text-yellow-600" : "text-green-600"
                }`}>
                  {daysUntilExpiry} days
                </span>
              </div>
            )}
            
            {institution.verificationStatus && (
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-600">Institution Verified</span>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Information</CardTitle>
            <CardDescription>Platform and account details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Active Users</span>
              <span className="text-sm font-medium">{stats.activeUsers} / {stats.totalUsers}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Last Backup</span>
              <span className="text-sm text-muted-foreground">
                {formatDate(stats.lastBackup)}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Created</span>
              <span className="text-sm text-muted-foreground">
                {formatDate(institution.createdAt)}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Last Updated</span>
              <span className="text-sm text-muted-foreground">
                {formatDate(institution.updatedAt)}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common tasks and operations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex flex-col items-center gap-2 p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
              <Users className="h-6 w-6 text-primary" />
              <span className="text-sm font-medium">Manage Users</span>
            </div>
            
            <div className="flex flex-col items-center gap-2 p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
              <Building2 className="h-6 w-6 text-primary" />
              <span className="text-sm font-medium">Add School</span>
            </div>
            
            <div className="flex flex-col items-center gap-2 p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
              <FileText className="h-6 w-6 text-primary" />
              <span className="text-sm font-medium">Generate Report</span>
            </div>
            
            <div className="flex flex-col items-center gap-2 p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
              <DollarSign className="h-6 w-6 text-primary" />
              <span className="text-sm font-medium">Billing</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 