"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { AlertCircle, Building, CheckCircle, Upload, Eye, EyeOff } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface SchoolRegistrationData {
  // Institution Details
  institutionName: string
  institutionType: string
  address: string
  city: string
  state: string
  country: string
  postalCode: string
  phone: string
  institutionEmail: string
  website: string
  logo: File | null

  // School Details
  schoolName: string
  schoolType: string
  schoolAddress: string
  schoolCity: string
  schoolState: string
  schoolCountry: string
  schoolPostalCode: string
  schoolPhone: string
  schoolEmail: string
  schoolWebsite: string

  // Admin Details
  adminFirstName: string
  adminLastName: string
  adminEmail: string
  adminPhone: string
  adminPassword: string
  confirmPassword: string

  // Additional Info
  studentCount: string
  teacherCount: string
  subscriptionPlan: string
  referralSource: string
  specialRequirements: string
}

const STEPS = [
  { id: 1, title: "Institution Details", description: "Basic information about your institution" },
  { id: 2, title: "School Details", description: "Information about your school" },
  { id: 3, title: "Administrator Account", description: "Create your admin account" },
  { id: 4, title: "Additional Information", description: "Additional details about your institution" },
  { id: 5, title: "Review & Submit", description: "Review and submit your application" },
]

const INSTITUTION_TYPES = [
  { value: "educational", label: "Educational Institution" },
  { value: "school_district", label: "School District" },
  { value: "other", label: "Other" },
]

const SCHOOL_TYPES = [
  { value: "KINDERGARTEN", label: "Kindergarten" },
  { value: "PRIMARY", label: "Primary School" },
  { value: "SECONDARY", label: "Secondary School" },
  { value: "TERTIARY", label: "Tertiary/University" },
  { value: "VOCATIONAL", label: "Vocational Training" },
  { value: "SPECIAL_NEEDS", label: "Special Needs" },
]

const SUBSCRIPTION_PLANS = [
  {
    value: "basic",
    label: "Basic Plan",
    price: "$29/month",
    features: ["Up to 100 students", "Basic reporting", "Email support"],
  },
  {
    value: "standard",
    label: "Standard Plan",
    price: "$79/month",
    features: ["Up to 500 students", "Advanced reporting", "Priority support", "Mobile app"],
  },
  {
    value: "premium",
    label: "Premium Plan",
    price: "$149/month",
    features: ["Up to 2000 students", "Custom reports", "24/7 support", "API access"],
  },
  {
    value: "enterprise",
    label: "Enterprise Plan",
    price: "Custom pricing",
    features: ["Unlimited students", "Custom features", "Dedicated support", "On-premise option"],
  },
]

export default function SchoolRegistrationPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [formData, setFormData] = useState<SchoolRegistrationData>({
    // Institution Details
    institutionName: "",
    institutionType: "",
    address: "",
    city: "",
    state: "",
    country: "Zambia",
    postalCode: "",
    phone: "",
    institutionEmail: "",
    website: "",
    logo: null,

    // School Details
    schoolName: "",
    schoolType: "TERTIARY",
    schoolAddress: "",
    schoolCity: "",
    schoolState: "",
    schoolCountry: "Zambia",
    schoolPostalCode: "",
    schoolPhone: "",
    schoolEmail: "",
    schoolWebsite: "",

    // Admin Details
    adminFirstName: "",
    adminLastName: "",
    adminEmail: "",
    adminPhone: "",
    adminPassword: "",
    confirmPassword: "",

    // Additional Info
    studentCount: "",
    teacherCount: "",
    subscriptionPlan: "",
    referralSource: "",
    specialRequirements: "",
  })

  const handleInputChange = (field: keyof SchoolRegistrationData, value: string | File | null) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    setError("")
  }

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      if (file.size > 5 * 1024 * 1024) {
        // 5MB limit
        setError("Logo file size must be less than 5MB")
        return
      }
      handleInputChange("logo", file)
    }
  }

  const validateStep = (step: number): boolean => {
    setError("")

    switch (step) {
      case 1:
        if (
          !formData.institutionName ||
          !formData.institutionType ||
          !formData.address ||
          !formData.city ||
          !formData.phone ||
          !formData.institutionEmail
        ) {
          setError("Please fill in all required fields")
          return false
        }
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.institutionEmail)) {
          setError("Please enter a valid institution email")
          return false
        }
        break

      case 2:
        if (!formData.schoolName || !formData.schoolType) {
          setError("Please fill in all required school fields")
          return false
        }
        // Validate school email if provided
        if (formData.schoolEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.schoolEmail)) {
          setError("Please enter a valid school email")
          return false
        }
        break

      case 3:
        if (
          !formData.adminFirstName ||
          !formData.adminLastName ||
          !formData.adminEmail ||
          !formData.adminPassword ||
          !formData.confirmPassword
        ) {
          setError("Please fill in all required fields")
          return false
        }
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.adminEmail)) {
          setError("Please enter a valid admin email")
          return false
        }
        if (formData.adminPassword !== formData.confirmPassword) {
          setError("Passwords do not match")
          return false
        }
        if (formData.adminPassword.length < 8) {
          setError("Password must be at least 8 characters long")
          return false
        }
        break

      case 4:
        if (!formData.studentCount || !formData.teacherCount || !formData.subscriptionPlan) {
          setError("Please fill in all required fields")
          return false
        }
        break
    }

    return true
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep((prev) => Math.min(prev + 1, STEPS.length))
    }
  }

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return

    setIsLoading(true)
    setError("")

    try {
      // Create FormData for file upload
      const submitData = new FormData()

      // Add all form fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key === "logo" && value instanceof File) {
          submitData.append(key, value)
        } else if (typeof value === "string") {
          submitData.append(key, value)
        }
      })

      const response = await fetch("/api/schools/register", {
        method: "POST",
        body: submitData,
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Registration failed")
      }

      // Redirect to success page with the correct token from result.data
      router.push(`/auth/register-school/success?token=${result.data.verificationToken}`)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="institutionName">Institution Name *</Label>
              <Input
                id="institutionName"
                placeholder="e.g., Lusaka Primary School"
                value={formData.institutionName}
                onChange={(e) => handleInputChange("institutionName", e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="institutionType">Institution Type *</Label>
              <Select
                value={formData.institutionType}
                onValueChange={(value) => handleInputChange("institutionType", value)}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select institution type" />
                </SelectTrigger>
                <SelectContent>
                  {INSTITUTION_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Address *</Label>
              <Textarea
                id="address"
                placeholder="Street address"
                value={formData.address}
                onChange={(e) => handleInputChange("address", e.target.value)}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  placeholder="e.g., Lusaka"
                  value={formData.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  value={formData.country}
                  onChange={(e) => handleInputChange("country", e.target.value)}
                  disabled
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="state">State/Province</Label>
                <Input
                  id="state"
                  placeholder="e.g., Lusaka Province"
                  value={formData.state}
                  onChange={(e) => handleInputChange("state", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="postalCode">Postal Code</Label>
                <Input
                  id="postalCode"
                  placeholder="e.g., 10101"
                  value={formData.postalCode}
                  onChange={(e) => handleInputChange("postalCode", e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="+260 XXX XXX XXX"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="institutionEmail">Institution Email *</Label>
                <Input
                  id="institutionEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.institutionEmail}
                  onChange={(e) => handleInputChange("institutionEmail", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="website">Website (Optional)</Label>
              <Input
                id="website"
                type="url"
                placeholder="https://www.yourschool.edu.zm"
                value={formData.website}
                onChange={(e) => handleInputChange("website", e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="logo">Institution Logo (Optional)</Label>
              <div className="flex items-center gap-4">
                <div className="h-16 w-16 rounded-md border flex items-center justify-center overflow-hidden bg-gray-50">
                  {formData.logo ? (
                    <img
                      src={URL.createObjectURL(formData.logo) || "/placeholder.svg"}
                      alt="Logo preview"
                      className="h-full w-full object-contain"
                    />
                  ) : (
                    <div className="text-2xl font-bold text-muted-foreground/30">
                      {formData.institutionName ? formData.institutionName.charAt(0) : "L"}
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <Input id="logo" type="file" accept="image/*" onChange={handleLogoUpload} className="hidden" />
                  <Button type="button" variant="outline" onClick={() => document.getElementById("logo")?.click()}>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Logo
                  </Button>
                  <p className="text-xs text-gray-500 mt-1">Max file size: 5MB</p>
                </div>
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="schoolName">School Name *</Label>
              <Input
                id="schoolName"
                placeholder="e.g., Lusaka Primary School"
                value={formData.schoolName}
                onChange={(e) => handleInputChange("schoolName", e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="schoolType">School Type *</Label>
              <Select
                value={formData.schoolType}
                onValueChange={(value) => handleInputChange("schoolType", value)}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select school type" />
                </SelectTrigger>
                <SelectContent>
                  {SCHOOL_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="schoolAddress">School Address</Label>
              <Textarea
                id="schoolAddress"
                placeholder="Street address (leave empty to use institution address)"
                value={formData.schoolAddress}
                onChange={(e) => handleInputChange("schoolAddress", e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="schoolCity">City</Label>
                <Input
                  id="schoolCity"
                  placeholder="e.g., Lusaka"
                  value={formData.schoolCity}
                  onChange={(e) => handleInputChange("schoolCity", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="schoolCountry">Country</Label>
                <Input
                  id="schoolCountry"
                  value={formData.schoolCountry}
                  onChange={(e) => handleInputChange("schoolCountry", e.target.value)}
                  disabled
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="schoolState">State/Province</Label>
                <Input
                  id="schoolState"
                  placeholder="e.g., Lusaka Province"
                  value={formData.schoolState}
                  onChange={(e) => handleInputChange("schoolState", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="schoolPostalCode">Postal Code</Label>
                <Input
                  id="schoolPostalCode"
                  placeholder="e.g., 10101"
                  value={formData.schoolPostalCode}
                  onChange={(e) => handleInputChange("schoolPostalCode", e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="schoolPhone">School Phone</Label>
                <Input
                  id="schoolPhone"
                  type="tel"
                  placeholder="+260 XXX XXX XXX"
                  value={formData.schoolPhone}
                  onChange={(e) => handleInputChange("schoolPhone", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="schoolEmail">School Email</Label>
                <Input
                  id="schoolEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.schoolEmail}
                  onChange={(e) => handleInputChange("schoolEmail", e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="schoolWebsite">School Website (Optional)</Label>
              <Input
                id="schoolWebsite"
                type="url"
                placeholder="https://www.yourschool.edu.zm"
                value={formData.schoolWebsite}
                onChange={(e) => handleInputChange("schoolWebsite", e.target.value)}
              />
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="adminFirstName">First Name *</Label>
                <Input
                  id="adminFirstName"
                  placeholder="John"
                  value={formData.adminFirstName}
                  onChange={(e) => handleInputChange("adminFirstName", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="adminLastName">Last Name *</Label>
                <Input
                  id="adminLastName"
                  placeholder="Doe"
                  value={formData.adminLastName}
                  onChange={(e) => handleInputChange("adminLastName", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="adminEmail">Admin Email *</Label>
              <Input
                id="adminEmail"
                type="email"
                placeholder="<EMAIL>"
                value={formData.adminEmail}
                onChange={(e) => handleInputChange("adminEmail", e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="adminPhone">Admin Phone (Optional)</Label>
              <Input
                id="adminPhone"
                type="tel"
                placeholder="+260 XXX XXX XXX"
                value={formData.adminPhone}
                onChange={(e) => handleInputChange("adminPhone", e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="adminPassword">Password *</Label>
              <div className="relative">
                <Input
                  id="adminPassword"
                  type={showPassword ? "text" : "password"}
                  value={formData.adminPassword}
                  onChange={(e) => handleInputChange("adminPassword", e.target.value)}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password *</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="text-xs text-gray-500">
              <p>Password must contain:</p>
              <ul className="list-disc pl-5 mt-1 space-y-1">
                <li>At least 8 characters</li>
                <li>One uppercase letter</li>
                <li>One lowercase letter</li>
                <li>One number</li>
                <li>One special character</li>
              </ul>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="studentCount">Approximate Student Count *</Label>
                <Select
                  value={formData.studentCount}
                  onValueChange={(value) => handleInputChange("studentCount", value)}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-50">1-50 students</SelectItem>
                    <SelectItem value="51-100">51-100 students</SelectItem>
                    <SelectItem value="101-250">101-250 students</SelectItem>
                    <SelectItem value="251-500">251-500 students</SelectItem>
                    <SelectItem value="501-1000">501-1000 students</SelectItem>
                    <SelectItem value="1000+">1000+ students</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="teacherCount">Approximate Teacher Count *</Label>
                <Select
                  value={formData.teacherCount}
                  onValueChange={(value) => handleInputChange("teacherCount", value)}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-10">1-10 teachers</SelectItem>
                    <SelectItem value="11-25">11-25 teachers</SelectItem>
                    <SelectItem value="26-50">26-50 teachers</SelectItem>
                    <SelectItem value="51-100">51-100 teachers</SelectItem>
                    <SelectItem value="100+">100+ teachers</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="subscriptionPlan">Subscription Plan *</Label>
              <div className="grid grid-cols-1 gap-3">
                {SUBSCRIPTION_PLANS.map((plan) => (
                  <div
                    key={plan.value}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      formData.subscriptionPlan === plan.value
                        ? "border-emerald-500 bg-emerald-50"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => handleInputChange("subscriptionPlan", plan.value)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">{plan.label}</h4>
                        <p className="text-sm text-gray-600">{plan.price}</p>
                        <ul className="text-xs text-gray-500 mt-2 space-y-1">
                          {plan.features.map((feature, index) => (
                            <li key={index}>• {feature}</li>
                          ))}
                        </ul>
                      </div>
                      <div
                        className={`w-4 h-4 rounded-full border-2 ${
                          formData.subscriptionPlan === plan.value
                            ? "border-emerald-500 bg-emerald-500"
                            : "border-gray-300"
                        }`}
                      >
                        {formData.subscriptionPlan === plan.value && (
                          <div className="w-2 h-2 bg-white rounded-full m-0.5" />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="referralSource">How did you hear about us? (Optional)</Label>
              <Select
                value={formData.referralSource}
                onValueChange={(value) => handleInputChange("referralSource", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="google">Google Search</SelectItem>
                  <SelectItem value="social-media">Social Media</SelectItem>
                  <SelectItem value="referral">Referral from colleague</SelectItem>
                  <SelectItem value="conference">Conference/Event</SelectItem>
                  <SelectItem value="advertisement">Advertisement</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="specialRequirements">Special Requirements (Optional)</Label>
              <Textarea
                id="specialRequirements"
                placeholder="Any specific features or requirements you need..."
                value={formData.specialRequirements}
                onChange={(e) => handleInputChange("specialRequirements", e.target.value)}
                rows={3}
              />
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Review Your Information</h3>
              <p className="text-gray-600">Please review all details before submitting your application</p>
            </div>

            <div className="space-y-4">
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">Institution Details</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium">Name:</span> {formData.institutionName}
                  </div>
                  <div>
                    <span className="font-medium">Type:</span>{" "}
                    {INSTITUTION_TYPES.find((t) => t.value === formData.institutionType)?.label}
                  </div>
                  <div>
                    <span className="font-medium">City:</span> {formData.city}
                  </div>
                  <div>
                    <span className="font-medium">Email:</span> {formData.institutionEmail}
                  </div>
                </div>
              </div>

              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">School Details</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium">Name:</span> {formData.schoolName || formData.institutionName}
                  </div>
                  <div>
                    <span className="font-medium">Type:</span>{" "}
                    {SCHOOL_TYPES.find((t) => t.value === formData.schoolType)?.label}
                  </div>
                  <div>
                    <span className="font-medium">City:</span> {formData.schoolCity || formData.city}
                  </div>
                  <div>
                    <span className="font-medium">Email:</span> {formData.schoolEmail || formData.institutionEmail}
                  </div>
                </div>
              </div>

              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">Administrator</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium">Name:</span> {formData.adminFirstName} {formData.adminLastName}
                  </div>
                  <div>
                    <span className="font-medium">Email:</span> {formData.adminEmail}
                  </div>
                </div>
              </div>

              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">School Information</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium">Students:</span> {formData.studentCount}
                  </div>
                  <div>
                    <span className="font-medium">Teachers:</span> {formData.teacherCount}
                  </div>
                  <div>
                    <span className="font-medium">Plan:</span>{" "}
                    {SUBSCRIPTION_PLANS.find((p) => p.value === formData.subscriptionPlan)?.label}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">What happens next?</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• We'll review your application within 24-48 hours</li>
                <li>• You'll receive an email confirmation with next steps</li>
                <li>• Our team will contact you to schedule a demo</li>
                <li>• Once approved, you can start your free trial</li>
              </ul>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <Link href="/" className="text-3xl font-bold text-emerald-600 mb-4 inline-block">
            EduManage
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">Register Your School</h1>
          <p className="text-gray-600 mt-2">Join thousands of schools using EduManage</p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            {STEPS.map((step, index) => (
              <div key={step.id} className={`flex items-center ${index < STEPS.length - 1 ? "flex-1" : ""}`}>
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep >= step.id ? "bg-emerald-600 text-white" : "bg-gray-200 text-gray-600"
                  }`}
                >
                  {currentStep > step.id ? <CheckCircle className="w-5 h-5" /> : step.id}
                </div>
                {index < STEPS.length - 1 && (
                  <div className={`flex-1 h-1 mx-2 ${currentStep > step.id ? "bg-emerald-600" : "bg-gray-200"}`} />
                )}
              </div>
            ))}
          </div>
          <div className="text-center">
            <h2 className="text-lg font-medium">{STEPS[currentStep - 1].title}</h2>
            <p className="text-sm text-gray-600">{STEPS[currentStep - 1].description}</p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Building className="h-5 w-5 text-emerald-600" />
              <CardTitle>
                Step {currentStep} of {STEPS.length}
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {renderStep()}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={prevStep} disabled={currentStep === 1}>
              Previous
            </Button>

            {currentStep < STEPS.length ? (
              <Button onClick={nextStep}>Next</Button>
            ) : (
              <Button onClick={handleSubmit} disabled={isLoading}>
                {isLoading ? "Submitting..." : "Submit Application"}
              </Button>
            )}
          </CardFooter>
        </Card>

        <div className="text-center mt-6">
          <p className="text-sm text-gray-600">
            Already have an account?{" "}
            <Link href="/auth/login" className="text-emerald-600 hover:text-emerald-500 font-medium">
              Sign in here
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
