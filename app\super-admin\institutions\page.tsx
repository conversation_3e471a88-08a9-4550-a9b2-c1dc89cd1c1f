"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, MoreHorizontal, Download, X, Building, CheckCircle, AlertCircle } from "lucide-react"
import { superAdminService, institutionService } from "@/lib/backend-api"

// Mock data for institutions
const mockInstitutions = [
  {
    id: "1",
    name: "Lusaka Primary School",
    type: "Primary",
    location: "Lusaka",
    students: 850,
    staff: 45,
    status: "Active",
    subscription: "Premium",
    createdAt: "2023-05-15",
  },
  {
    id: "2",
    name: "Kitwe Secondary School",
    type: "Secondary",
    location: "Kitwe",
    students: 1200,
    staff: 78,
    status: "Active",
    subscription: "Standard",
    createdAt: "2023-08-22",
  },
  {
    id: "3",
    name: "Ndola Academy",
    type: "Primary",
    location: "Ndola",
    students: 650,
    staff: 35,
    status: "Active",
    subscription: "Premium",
    createdAt: "2022-11-10",
  },
  {
    id: "4",
    name: "Livingstone College",
    type: "College",
    location: "Livingstone",
    students: 1500,
    staff: 120,
    status: "Pending",
    subscription: "Enterprise",
    createdAt: "2025-05-18",
  },
  {
    id: "5",
    name: "Kabwe University",
    type: "University",
    location: "Kabwe",
    students: 3200,
    staff: 210,
    status: "Active",
    subscription: "Enterprise",
    createdAt: "2021-09-05",
  },
]

// Define interfaces for type safety
interface Institution {
  id: string
  name: string
  type: string
  location: string
  students: number
  staff: number
  status: string
  subscription: string
  createdAt: string
}

interface PaginationState {
  currentPage: number
  totalPages: number
  totalInstitutions: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

export default function InstitutionsPage() {
  const [institutions, setInstitutions] = useState<Institution[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    totalPages: 1,
    totalInstitutions: 0,
    hasNextPage: false,
    hasPrevPage: false,
  })
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchInstitutions = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const params: any = {
          page: pagination.currentPage.toString(),
          limit: "10",
        }

        if (searchQuery) params.search = searchQuery
        if (typeFilter && typeFilter !== "all") params.type = typeFilter
        if (statusFilter && statusFilter !== "all") params.status = statusFilter

        const result = await institutionService.getAllInstitutions(params)
        
        if (result.success) {
          setInstitutions(result.data.institutions || [])
          setPagination(result.data.pagination || pagination)
        } else {
          throw new Error(result.error || 'Failed to fetch institutions')
        }

      } catch (err: any) {
        console.error('Institutions API error:', err)
        setError(err?.message || 'Failed to fetch institutions')
        // Fallback to mock data on error
        setInstitutions(mockInstitutions)
      } finally {
        setIsLoading(false)
      }
    }

    fetchInstitutions()
  }, [searchQuery, typeFilter, statusFilter, pagination.currentPage])

  const filteredInstitutions = institutions

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Institutions</h1>
          <p className="text-gray-500">Manage all registered educational institutions</p>
        </div>
        <div className="flex gap-2">
          <Link href="/super-admin/institutions/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Institution
            </Button>
          </Link>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Institution Records</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search by name or location..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select onValueChange={setTypeFilter} value={typeFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="Primary">Primary</SelectItem>
                  <SelectItem value="Secondary">Secondary</SelectItem>
                  <SelectItem value="College">College</SelectItem>
                  <SelectItem value="University">University</SelectItem>
                </SelectContent>
              </Select>

              <Select onValueChange={setStatusFilter} value={statusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="Suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>

              {(typeFilter && typeFilter !== "all") || (statusFilter && statusFilter !== "all") ? (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    setTypeFilter("all")
                    setStatusFilter("all")
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              ) : null}
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <p>Loading institutions...</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Students</TableHead>
                    <TableHead>Staff</TableHead>
                    <TableHead>Subscription</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredInstitutions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center h-24">
                        No institutions found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredInstitutions.map((institution) => (
                      <TableRow key={institution.id}>
                        <TableCell>
                          <Link
                            href={`/super-admin/institutions/${institution.id}`}
                            className="font-medium text-emerald-600 hover:underline flex items-center"
                          >
                            <Building className="h-4 w-4 mr-2" />
                            {institution.name}
                          </Link>
                        </TableCell>
                        <TableCell>{institution.type}</TableCell>
                        <TableCell>{institution.location}</TableCell>
                        <TableCell>{institution.students.toLocaleString()}</TableCell>
                        <TableCell>{institution.staff}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{institution.subscription}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {institution.status === "Active" ? (
                              <CheckCircle className="h-4 w-4 text-emerald-500 mr-1" />
                            ) : institution.status === "Pending" ? (
                              <AlertCircle className="h-4 w-4 text-amber-500 mr-1" />
                            ) : (
                              <AlertCircle className="h-4 w-4 text-red-500 mr-1" />
                            )}
                            <Badge
                              variant={
                                institution.status === "Active"
                                  ? "default"
                                  : institution.status === "Pending"
                                    ? "outline"
                                    : "destructive"
                              }
                            >
                              {institution.status}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Actions</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Link href={`/super-admin/institutions/${institution.id}`} className="flex w-full">
                                  View Details
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Link href={`/super-admin/institutions/${institution.id}/edit`} className="flex w-full">
                                  Edit
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Link
                                  href={`/super-admin/institutions/${institution.id}/users`}
                                  className="flex w-full"
                                >
                                  Manage Users
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Link
                                  href={`/super-admin/institutions/${institution.id}/subscription`}
                                  className="flex w-full"
                                >
                                  Subscription
                                </Link>
                              </DropdownMenuItem>
                              {institution.status === "Pending" && <DropdownMenuItem>Approve</DropdownMenuItem>}
                              {institution.status === "Active" && (
                                <DropdownMenuItem className="text-red-500">Suspend</DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
