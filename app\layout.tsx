import type React from "react";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import { TenantProvider } from "@/contexts/tenant-context";
import { NotificationProvider } from "@/contexts/notification-context";
import "./globals.css";
import { AuthProvider } from "@/contexts/auth-context";



const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "School Management System",
  description: "Multi-tenant SaaS School Management System for Zambian schools",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>

          <AuthProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="light"
              enableSystem
              disableTransitionOnChange
            >
              <TenantProvider>
                <NotificationProvider>{children}</NotificationProvider>
              </TenantProvider>
            </ThemeProvider>
          </AuthProvider>

      </body>
    </html>
  );
}
