"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Edit, Save, X, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface Institution {
  id: string
  name: string
  domain?: string
  logo?: string
  primaryColor?: string
  secondaryColor?: string
  isActive: boolean
  subscriptionStatus: string
  subscriptionEndDate?: string
  address?: string
  city?: string
  state?: string
  country?: string
  postalCode?: string
  phoneNumber?: string
  email?: string
  website?: string
  verificationStatus: boolean
  studentCount?: number
  teacherCount?: number
  referralSource?: string
  specialRequirements?: string
  createdAt: string
  updatedAt: string
}

interface InstitutionInformationFormProps {
  institution: Institution
  onUpdate: (data: Partial<Institution>) => Promise<void>
}

export function InstitutionInformationForm({ institution, onUpdate }: InstitutionInformationFormProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    name: institution.name,
    isActive: institution.isActive,
    address: institution.address || "",
    city: institution.city || "",
    state: institution.state || "",
    country: institution.country || "",
    postalCode: institution.postalCode || "",
    phoneNumber: institution.phoneNumber || "",
    email: institution.email || "",
    website: institution.website || "",
  })
  const { toast } = useToast()

  const handleInputChange = (field: string, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      await onUpdate(formData)
      setIsEditing(false)
      toast({
        title: "Success",
        description: "Institution information updated successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update institution information. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      name: institution.name,
      isActive: institution.isActive,
      address: institution.address || "",
      city: institution.city || "",
      state: institution.state || "",
      country: institution.country || "",
      postalCode: institution.postalCode || "",
      phoneNumber: institution.phoneNumber || "",
      email: institution.email || "",
      website: institution.website || "",
    })
    setIsEditing(false)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Institution Information</CardTitle>
          <CardDescription>
            {isEditing ? "Edit your institution details" : "Basic information about your institution"}
          </CardDescription>
        </div>
        {!isEditing ? (
          <Button onClick={() => setIsEditing(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Information
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Basic Institution Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Basic Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Institution Name *</Label>
              {isEditing ? (
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Enter institution name"
                  required
                />
              ) : (
                <p className="p-2 border rounded-md bg-muted/50">{institution.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="isActive">Institution Status</Label>
              {isEditing ? (
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => handleInputChange("isActive", checked)}
                  />
                  <Label htmlFor="isActive">
                    {formData.isActive ? "Active" : "Inactive"}
                  </Label>
                </div>
              ) : (
                <p className="p-2 border rounded-md bg-muted/50">
                  {institution.isActive ? "Active" : "Inactive"}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Contact Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              {isEditing ? (
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  placeholder="Enter email address"
                />
              ) : (
                <p className="p-2 border rounded-md bg-muted/50">{institution.email || "Not provided"}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phoneNumber">Phone Number</Label>
              {isEditing ? (
                <Input
                  id="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                  placeholder="Enter phone number"
                />
              ) : (
                <p className="p-2 border rounded-md bg-muted/50">{institution.phoneNumber || "Not provided"}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="website">Website</Label>
              {isEditing ? (
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => handleInputChange("website", e.target.value)}
                  placeholder="Enter website URL"
                />
              ) : (
                <p className="p-2 border rounded-md bg-muted/50">{institution.website || "Not provided"}</p>
              )}
            </div>
          </div>
        </div>

        {/* Address Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Address Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="address">Street Address</Label>
              {isEditing ? (
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange("address", e.target.value)}
                  placeholder="Enter street address"
                />
              ) : (
                <p className="p-2 border rounded-md bg-muted/50">{institution.address || "Not provided"}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              {isEditing ? (
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  placeholder="Enter city"
                />
              ) : (
                <p className="p-2 border rounded-md bg-muted/50">{institution.city || "Not provided"}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="state">State/Province</Label>
              {isEditing ? (
                <Input
                  id="state"
                  value={formData.state}
                  onChange={(e) => handleInputChange("state", e.target.value)}
                  placeholder="Enter state or province"
                />
              ) : (
                <p className="p-2 border rounded-md bg-muted/50">{institution.state || "Not provided"}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              {isEditing ? (
                <Input
                  id="country"
                  value={formData.country}
                  onChange={(e) => handleInputChange("country", e.target.value)}
                  placeholder="Enter country"
                />
              ) : (
                <p className="p-2 border rounded-md bg-muted/50">{institution.country || "Not provided"}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="postalCode">Postal Code</Label>
              {isEditing ? (
                <Input
                  id="postalCode"
                  value={formData.postalCode}
                  onChange={(e) => handleInputChange("postalCode", e.target.value)}
                  placeholder="Enter postal code"
                />
              ) : (
                <p className="p-2 border rounded-md bg-muted/50">{institution.postalCode || "Not provided"}</p>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 