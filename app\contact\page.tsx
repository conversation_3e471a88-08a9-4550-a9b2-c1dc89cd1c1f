import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { Mail, MapPin, Phone } from "lucide-react"

import { LandingHeader } from "@/components/landing/landing-header"
import { Footer } from "@/components/landing/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

export const metadata: Metadata = {
  title: "Contact Us | School Management System",
  description:
    "Get in touch with our team for inquiries, support, or to schedule a demo of our school management system.",
}

export default function ContactPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <LandingHeader />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 py-20 md:py-28">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Get in Touch</h1>
                <p className="mx-auto max-w-[700px] text-slate-500 dark:text-slate-400 md:text-xl">
                  Have questions or need assistance? Our team is here to help.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Form Section */}
        <section className="py-20">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="grid gap-10 lg:grid-cols-2">
              <div>
                <h2 className="text-3xl font-bold tracking-tighter mb-4">Contact Information</h2>
                <p className="text-slate-500 dark:text-slate-400 mb-8">
                  Fill out the form and our team will get back to you within 24 hours.
                </p>

                <div className="space-y-6">
                  <div className="flex items-start">
                    <MapPin className="w-6 h-6 text-primary mr-4 mt-1" />
                    <div>
                      <h3 className="font-medium">Our Office</h3>
                      <address className="not-italic text-slate-500 dark:text-slate-400">
                        18256/M Chalala
                        <br />
                        Lusaka, Zambia
                      </address>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Phone className="w-6 h-6 text-primary mr-4 mt-1" />
                    <div>
                      <h3 className="font-medium">Phone</h3>
                      <p className="text-slate-500 dark:text-slate-400">
                        <a href="tel:+260979628259" className="hover:text-primary">
                          +260 979628259
                        </a>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Mail className="w-6 h-6 text-primary mr-4 mt-1" />
                    <div>
                      <h3 className="font-medium">Email</h3>
                      <p className="text-slate-500 dark:text-slate-400">
                        <a href="mailto:<EMAIL>" className="hover:text-primary">
                          <EMAIL>
                        </a>
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-10">
                  <h3 className="text-xl font-bold mb-4">Office Hours</h3>
                  <table className="w-full text-left">
                    <tbody>
                      <tr className="border-b">
                        <td className="py-2 font-medium">Monday - Friday</td>
                        <td className="py-2">9:00 AM - 6:00 PM EST</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-2 font-medium">Saturday</td>
                        <td className="py-2">10:00 AM - 4:00 PM EST</td>
                      </tr>
                      <tr>
                        <td className="py-2 font-medium">Sunday</td>
                        <td className="py-2">Closed</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <Card>
                <CardContent className="p-6">
                  <form className="space-y-6">
                    <div className="space-y-4">
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="space-y-2">
                          <Label htmlFor="first-name">First name</Label>
                          <Input id="first-name" placeholder="Jay" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="last-name">Last name</Label>
                          <Input id="last-name" placeholder="Kaluwaji" />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input id="email" type="email" placeholder="<EMAIL>" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone (optional)</Label>
                        <Input id="phone" type="tel" placeholder="+260 979628259" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="institution">Institution Name (optional)</Label>
                        <Input id="institution" placeholder="ABC School" />
                      </div>

                      <div className="space-y-2">
                        <Label>I am a</Label>
                        <RadioGroup defaultValue="school-admin">
                          <div className="flex flex-wrap gap-4">
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="school-admin" id="school-admin" />
                              <Label htmlFor="school-admin">School Administrator</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="teacher" id="teacher" />
                              <Label htmlFor="teacher">Teacher</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="it-staff" id="it-staff" />
                              <Label htmlFor="it-staff">IT Staff</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="other" id="other" />
                              <Label htmlFor="other">Other</Label>
                            </div>
                          </div>
                        </RadioGroup>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="inquiry-type">Inquiry Type</Label>
                        <Select>
                          <SelectTrigger id="inquiry-type">
                            <SelectValue placeholder="Select an option" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="general">General Inquiry</SelectItem>
                            <SelectItem value="sales">Sales Question</SelectItem>
                            <SelectItem value="demo">Request a Demo</SelectItem>
                            <SelectItem value="support">Technical Support</SelectItem>
                            <SelectItem value="partnership">Partnership Opportunity</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="message">Message</Label>
                        <Textarea id="message" placeholder="Please provide details about your inquiry..." rows={5} />
                      </div>
                    </div>

                    <Button type="submit" className="w-full">
                      Send Message
                    </Button>

                    <p className="text-sm text-slate-500 dark:text-slate-400 text-center">
                      By submitting this form, you agree to our{" "}
                      <Link href="/privacy-policy" className="text-primary hover:underline">
                        Privacy Policy
                      </Link>
                      .
                    </p>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Map Section */}
        <section className="py-10">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="rounded-xl overflow-hidden h-[400px] bg-slate-200 dark:bg-slate-800 shadow-lg">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3830.1234567890123!2d28.2875!3d-15.4167!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1941f4c0c0c0c0c0%3A0x0!2zMTXCsDI1JzAwLjEiUyAyOMKwMTcnMTUuMCJF!5e0!3m2!1sen!2szm!4v1234567890"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Edusync Office Location"
              ></iframe>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="bg-slate-50 dark:bg-slate-900 py-20">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Frequently Asked Questions</h2>
              <p className="mx-auto max-w-[700px] text-slate-500 dark:text-slate-400 md:text-xl mt-4">
                Find quick answers to common questions
              </p>
            </div>
            <div className="grid gap-6 md:grid-cols-2 lg:gap-10 max-w-4xl mx-auto">
              {contactFaqs.map((faq, index) => (
                <div key={index} className="space-y-2">
                  <h3 className="text-xl font-bold">{faq.question}</h3>
                  <p className="text-slate-500 dark:text-slate-400">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}

const contactFaqs = [
  {
    question: "How quickly will I receive a response?",
    answer:
      "We aim to respond to all inquiries within 24 hours during business days. For urgent matters, please call our support line.",
  },
  {
    question: "Can I schedule a demo?",
    answer:
      "Yes! You can request a demo through the contact form or <NAME_EMAIL>. We'll set up a personalized demonstration of our platform.",
  },
  {
    question: "Do you offer technical support?",
    answer:
      "Yes, we provide technical support to all our customers. The level of support depends on your subscription plan.",
  },
  {
    question: "How can I report a bug?",
    answer:
      "You can report bugs through our support portal or <NAME_EMAIL> with details about the issue you're experiencing.",
  },
]
