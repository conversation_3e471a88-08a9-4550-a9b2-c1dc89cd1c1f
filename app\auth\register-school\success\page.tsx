"use client"

import { useEffect, useState } from "react"
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, Mail, Clock, Phone, ArrowRight } from "lucide-react"

export default function SchoolRegistrationSuccessPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [applicationId, setApplicationId] = useState<string | null>(null)

  useEffect(() => {
    const token = searchParams.get("token")
    if (token) {
      setApplicationId(token.slice(0, 8).toUpperCase())
    } else {
      // If no token is provided, redirect to the registration page
      router.push('/register-school')
    }
  }, [searchParams, router])

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <Link href="/" className="text-3xl font-bold text-emerald-600 mb-4 inline-block">
            EduManage
          </Link>
        </div>

        <Card className="text-center">
          <CardHeader>
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-emerald-100">
              <CheckCircle className="h-8 w-8 text-emerald-600" />
            </div>
            <CardTitle className="text-2xl">Application Submitted Successfully!</CardTitle>
            <CardDescription className="text-lg">
              Thank you for choosing EduManage for your school management needs
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {applicationId && (
              <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
                <p className="text-sm text-emerald-800 mb-1">Your Application ID</p>
                <p className="text-2xl font-bold text-emerald-900">{applicationId}</p>
                <p className="text-xs text-emerald-700 mt-1">Please save this ID for your records</p>
              </div>
            )}

            <div className="space-y-4">
              <div className="flex items-start gap-3 text-left">
                <Mail className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h3 className="font-medium">Verify Your Email</h3>
                  <p className="text-sm text-gray-600">
                    We've sent a verification email to your institution address. Please check your inbox and click the
                    verification link to activate your account.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 text-left">
                <Clock className="h-5 w-5 text-orange-600 mt-0.5" />
                <div>
                  <h3 className="font-medium">Review Process</h3>
                  <p className="text-sm text-gray-600">
                    Our team will review your application within 24-48 hours and contact you with the next steps.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 text-left">
                <Phone className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h3 className="font-medium">Demo & Setup</h3>
                  <p className="text-sm text-gray-600">
                    Once approved, we'll schedule a personalized demo and help you set up your school's system.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">What's Next?</h4>
              <ul className="text-sm text-blue-800 space-y-1 text-left">
                <li>• Verify your email by clicking the link in your inbox</li>
                <li>• Complete your profile and set up your institution details</li>
                <li>• Begin adding schools and users to your institution</li>
                <li>• Explore your 30-day free trial with full access</li>
                <li>• Receive personalized onboarding assistance from our team</li>
              </ul>
            </div>

            <div className="space-y-3">
              <Button onClick={() => router.push("/auth/login")} className="w-full">
                <ArrowRight className="mr-2 h-4 w-4" />
                Go to Login Page
              </Button>

              <Button variant="outline" onClick={() => router.push("/")} className="w-full">
                Return to Homepage
              </Button>
            </div>

            <div className="text-center pt-4 border-t">
              <p className="text-sm text-gray-600 mb-2">Need immediate assistance?</p>
              <div className="flex justify-center gap-4 text-sm">
                <a href="mailto:<EMAIL>" className="text-emerald-600 hover:text-emerald-500">
                  <EMAIL>
                </a>
                <span className="text-gray-300">|</span>
                <a href="tel:+260123456789" className="text-emerald-600 hover:text-emerald-500">
                  +260 123 456 789
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
