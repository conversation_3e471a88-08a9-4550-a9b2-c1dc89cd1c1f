"use client"

import { Separator } from "@/components/ui/separator"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, Filter, Plus, Send, Paperclip, ChevronRight } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

// Mock data for support tickets
const mockTickets = [
  {
    id: "T-1001",
    subject: "Cannot access student records",
    institution: {
      id: "1",
      name: "Lusaka Primary School",
    },
    status: "Open",
    priority: "High",
    category: "Access Issue",
    createdBy: "Michael Banda",
    createdAt: "May 22, 2025 09:15 AM",
    lastUpdated: "May 22, 2025 10:30 AM",
    assignedTo: "Support Team",
    messages: [
      {
        id: "1",
        sender: "Michael Banda",
        role: "Institution Admin",
        content:
          "I'm unable to access student records for Grade 5. When I click on the class, I get an error message saying 'Access Denied'.",
        timestamp: "May 22, 2025 09:15 AM",
        isStaff: false,
      },
      {
        id: "2",
        sender: "Support Team",
        role: "Support Agent",
        content:
          "Thank you for reporting this issue. Could you please provide more details about when this started happening? Were you able to access these records before?",
        timestamp: "May 22, 2025 10:30 AM",
        isStaff: true,
      },
    ],
  },
  {
    id: "T-1002",
    subject: "Billing discrepancy on latest invoice",
    institution: {
      id: "2",
      name: "Kitwe Secondary School",
    },
    status: "In Progress",
    priority: "Medium",
    category: "Billing",
    createdBy: "John Banda",
    createdAt: "May 21, 2025 14:20 PM",
    lastUpdated: "May 22, 2025 08:45 AM",
    assignedTo: "Finance Team",
    messages: [
      {
        id: "1",
        sender: "John Banda",
        role: "Institution Admin",
        content:
          "We were charged for 1200 students but we only have 1150 students registered in the system. Please review our billing.",
        timestamp: "May 21, 2025 14:20 PM",
        isStaff: false,
      },
      {
        id: "2",
        sender: "Finance Team",
        role: "Billing Specialist",
        content:
          "Thank you for bringing this to our attention. We're reviewing your account and will get back to you shortly.",
        timestamp: "May 22, 2025 08:45 AM",
        isStaff: true,
      },
    ],
  },
  {
    id: "T-1003",
    subject: "Feature request: Export attendance to Excel",
    institution: {
      id: "3",
      name: "Ndola Academy",
    },
    status: "Closed",
    priority: "Low",
    category: "Feature Request",
    createdBy: "Sarah Mwanza",
    createdAt: "May 15, 2025 11:30 AM",
    lastUpdated: "May 20, 2025 15:45 PM",
    assignedTo: "Product Team",
    messages: [
      {
        id: "1",
        sender: "Sarah Mwanza",
        role: "Teacher",
        content:
          "It would be very helpful if we could export attendance records to Excel for further analysis and reporting.",
        timestamp: "May 15, 2025 11:30 AM",
        isStaff: false,
      },
      {
        id: "2",
        sender: "Product Team",
        role: "Product Manager",
        content:
          "Thank you for your suggestion. We've added this to our feature request list and will consider it for a future update.",
        timestamp: "May 18, 2025 09:20 AM",
        isStaff: true,
      },
      {
        id: "3",
        sender: "Sarah Mwanza",
        role: "Teacher",
        content: "Thank you for considering my request. Looking forward to this feature.",
        timestamp: "May 18, 2025 10:15 AM",
        isStaff: false,
      },
      {
        id: "4",
        sender: "Product Team",
        role: "Product Manager",
        content:
          "Good news! We've prioritized this feature and it will be included in our next release scheduled for June 5th.",
        timestamp: "May 20, 2025 15:45 PM",
        isStaff: true,
      },
    ],
  },
]

// Mock data for knowledge base articles
const mockArticles = [
  {
    id: "KB-001",
    title: "Getting Started with Edusync",
    category: "General",
    views: 1250,
    lastUpdated: "May 10, 2025",
    excerpt:
      "Learn how to set up your institution, add users, and configure basic settings to get started with Edusync.",
  },
  {
    id: "KB-002",
    title: "Managing Student Records",
    category: "Students",
    views: 980,
    lastUpdated: "May 12, 2025",
    excerpt:
      "Comprehensive guide on adding, editing, and managing student records, including enrollment, transfers, and graduations.",
  },
  {
    id: "KB-003",
    title: "Setting Up Classes and Subjects",
    category: "Academics",
    views: 875,
    lastUpdated: "May 15, 2025",
    excerpt: "Learn how to create classes, assign teachers, and manage subjects for your educational institution.",
  },
  {
    id: "KB-004",
    title: "Attendance Tracking System",
    category: "Attendance",
    views: 760,
    lastUpdated: "May 18, 2025",
    excerpt:
      "Detailed guide on using the attendance tracking system, including daily attendance, reports, and notifications.",
  },
  {
    id: "KB-005",
    title: "Billing and Subscription Management",
    category: "Billing",
    views: 650,
    lastUpdated: "May 20, 2025",
    excerpt: "Information on managing your subscription, understanding billing cycles, and handling payments.",
  },
]

export default function SupportPage() {
  const [activeTab, setActiveTab] = useState("tickets")
  const [isLoading, setIsLoading] = useState(true)
  const [tickets, setTickets] = useState([])
  const [articles, setArticles] = useState([])
  const [selectedTicket, setSelectedTicket] = useState(null)
  const [newMessage, setNewMessage] = useState("")
  const [searchQuery, setSearchQuery] = useState("")
  const [error, setError] = useState(null)
  const { toast } = useToast()

  useEffect(() => {
    const fetchSupportData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Fetch support tickets
        const ticketsResponse = await fetch('/api/super-admin/support')
        if (ticketsResponse.ok) {
          const ticketsResult = await ticketsResponse.json()
          if (ticketsResult.success) {
            setTickets(ticketsResult.data || [])
          }
        }

        // Fetch knowledge base articles (if endpoint exists)
        const articlesResponse = await fetch('/api/super-admin/support?type=articles')
        if (articlesResponse.ok) {
          const articlesResult = await articlesResponse.json()
          if (articlesResult.success) {
            setArticles(articlesResult.data || mockArticles)
          }
        } else {
          // Fallback to mock articles
          setArticles(mockArticles)
        }

      } catch (err) {
        console.error('Support API error:', err)
        setError(err.message)
        // Fallback to mock data
        setTickets(mockTickets)
        setArticles(mockArticles)
      } finally {
        setIsLoading(false)
      }
    }

    fetchSupportData()
  }, [])

  const handleSendMessage = () => {
    if (!newMessage.trim()) return

    // In a real app, this would call your API
    const updatedTicket = {
      ...selectedTicket,
      messages: [
        ...selectedTicket.messages,
        {
          id: `${selectedTicket.messages.length + 1}`,
          sender: "Support Team",
          role: "Support Agent",
          content: newMessage,
          timestamp: new Date().toLocaleString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "numeric",
            minute: "numeric",
            hour12: true,
          }),
          isStaff: true,
        },
      ],
      lastUpdated: new Date().toLocaleString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        hour12: true,
      }),
      status: "In Progress",
    }

    // Update the tickets array
    const updatedTickets = tickets.map((ticket) => (ticket.id === selectedTicket.id ? updatedTicket : ticket))

    setTickets(updatedTickets)
    setSelectedTicket(updatedTicket)
    setNewMessage("")

    toast({
      title: "Message sent",
      description: "Your response has been sent to the user.",
    })
  }

  const handleCloseTicket = () => {
    // In a real app, this would call your API
    const updatedTicket = {
      ...selectedTicket,
      status: "Closed",
      lastUpdated: new Date().toLocaleString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        hour12: true,
      }),
    }

    // Update the tickets array
    const updatedTickets = tickets.map((ticket) => (ticket.id === selectedTicket.id ? updatedTicket : ticket))

    setTickets(updatedTickets)
    setSelectedTicket(updatedTicket)

    toast({
      title: "Ticket closed",
      description: `Ticket ${selectedTicket.id} has been closed.`,
    })
  }

  const handleReopenTicket = () => {
    // In a real app, this would call your API
    const updatedTicket = {
      ...selectedTicket,
      status: "Open",
      lastUpdated: new Date().toLocaleString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        hour12: true,
      }),
    }

    // Update the tickets array
    const updatedTickets = tickets.map((ticket) => (ticket.id === selectedTicket.id ? updatedTicket : ticket))

    setTickets(updatedTickets)
    setSelectedTicket(updatedTicket)

    toast({
      title: "Ticket reopened",
      description: `Ticket ${selectedTicket.id} has been reopened.`,
    })
  }

  const filteredTickets = tickets.filter(
    (ticket) =>
      ticket.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.institution.name.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const filteredArticles = articles.filter(
    (article) =>
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.excerpt.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Support Center</h1>
        <p className="text-muted-foreground">Manage support tickets and knowledge base</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="tickets">Support Tickets</TabsTrigger>
          <TabsTrigger value="knowledge">Knowledge Base</TabsTrigger>
          <TabsTrigger value="faq">FAQ</TabsTrigger>
        </TabsList>

        <TabsContent value="tickets" className="space-y-4">
          {selectedTicket ? (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Button variant="outline" onClick={() => setSelectedTicket(null)}>
                  Back to Tickets
                </Button>
                <div className="flex gap-2">
                  <Select defaultValue={selectedTicket.priority}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Low">Low Priority</SelectItem>
                      <SelectItem value="Medium">Medium Priority</SelectItem>
                      <SelectItem value="High">High Priority</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select defaultValue={selectedTicket.assignedTo}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Assigned To" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Support Team">Support Team</SelectItem>
                      <SelectItem value="Technical Team">Technical Team</SelectItem>
                      <SelectItem value="Finance Team">Finance Team</SelectItem>
                      <SelectItem value="Product Team">Product Team</SelectItem>
                    </SelectContent>
                  </Select>
                  {selectedTicket.status === "Closed" ? (
                    <Button onClick={handleReopenTicket}>Reopen Ticket</Button>
                  ) : (
                    <Button variant="outline" onClick={handleCloseTicket}>
                      Close Ticket
                    </Button>
                  )}
                </div>
              </div>

              <Card>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-xl">
                        {selectedTicket.id}: {selectedTicket.subject}
                      </CardTitle>
                      <CardDescription>
                        {selectedTicket.institution.name} • {selectedTicket.category} •{" "}
                        <Badge
                          variant={
                            selectedTicket.status === "Open"
                              ? "default"
                              : selectedTicket.status === "In Progress"
                                ? "outline"
                                : "secondary"
                          }
                        >
                          {selectedTicket.status}
                        </Badge>
                      </CardDescription>
                    </div>
                    <Badge
                      variant={
                        selectedTicket.priority === "High"
                          ? "destructive"
                          : selectedTicket.priority === "Medium"
                            ? "outline"
                            : "secondary"
                      }
                    >
                      {selectedTicket.priority} Priority
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <div>
                      <span className="font-medium">Created by:</span> {selectedTicket.createdBy}
                    </div>
                    <div>
                      <span className="font-medium">Created on:</span> {selectedTicket.createdAt}
                    </div>
                    <div>
                      <span className="font-medium">Last updated:</span> {selectedTicket.lastUpdated}
                    </div>
                    <div>
                      <span className="font-medium">Assigned to:</span> {selectedTicket.assignedTo}
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    {selectedTicket.messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex gap-4 ${message.isStaff ? "justify-start" : "justify-start"}`}
                      >
                        <Avatar>
                          <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
                          <AvatarFallback>{message.sender.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div
                          className={`space-y-2 rounded-lg p-4 max-w-[80%] ${
                            message.isStaff ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                          }`}
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <span className="font-medium">{message.sender}</span>
                              <span className="text-xs ml-2">({message.role})</span>
                            </div>
                            <span className="text-xs">{message.timestamp}</span>
                          </div>
                          <p className="text-sm">{message.content}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {selectedTicket.status !== "Closed" && (
                    <div className="space-y-2">
                      <Label htmlFor="reply">Reply</Label>
                      <Textarea
                        id="reply"
                        placeholder="Type your response here..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        className="min-h-[100px]"
                      />
                      <div className="flex justify-between">
                        <Button variant="outline" size="sm">
                          <Paperclip className="mr-2 h-4 w-4" />
                          Attach File
                        </Button>
                        <Button onClick={handleSendMessage} disabled={!newMessage.trim()}>
                          <Send className="mr-2 h-4 w-4" />
                          Send Response
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Support Tickets</CardTitle>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Ticket
                  </Button>
                </div>
                <CardDescription>Manage and respond to support requests</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center mb-4">
                  <div className="relative w-64">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search tickets..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Select defaultValue="all">
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="open">Open</SelectItem>
                        <SelectItem value="in-progress">In Progress</SelectItem>
                        <SelectItem value="closed">Closed</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button variant="outline" size="icon">
                      <Filter className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {isLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <p>Loading tickets...</p>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Ticket ID</TableHead>
                          <TableHead>Subject</TableHead>
                          <TableHead>Institution</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Priority</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead>Last Updated</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredTickets.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={8} className="text-center h-24">
                              No tickets found
                            </TableCell>
                          </TableRow>
                        ) : (
                          filteredTickets.map((ticket) => (
                            <TableRow key={ticket.id}>
                              <TableCell className="font-medium">{ticket.id}</TableCell>
                              <TableCell>{ticket.subject}</TableCell>
                              <TableCell>{ticket.institution.name}</TableCell>
                              <TableCell>
                                <Badge
                                  variant={
                                    ticket.status === "Open"
                                      ? "default"
                                      : ticket.status === "In Progress"
                                        ? "outline"
                                        : "secondary"
                                  }
                                >
                                  {ticket.status}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge
                                  variant={
                                    ticket.priority === "High"
                                      ? "destructive"
                                      : ticket.priority === "Medium"
                                        ? "outline"
                                        : "secondary"
                                  }
                                >
                                  {ticket.priority}
                                </Badge>
                              </TableCell>
                              <TableCell>{ticket.createdAt}</TableCell>
                              <TableCell>{ticket.lastUpdated}</TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setSelectedTicket(ticket)}
                                  className="flex items-center"
                                >
                                  View
                                  <ChevronRight className="ml-1 h-4 w-4" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="knowledge" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Knowledge Base</CardTitle>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  New Article
                </Button>
              </div>
              <CardDescription>Manage help articles and documentation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center mb-4">
                <div className="relative w-64">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search articles..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="general">General</SelectItem>
                      <SelectItem value="students">Students</SelectItem>
                      <SelectItem value="academics">Academics</SelectItem>
                      <SelectItem value="attendance">Attendance</SelectItem>
                      <SelectItem value="billing">Billing</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <p>Loading articles...</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Views</TableHead>
                        <TableHead>Last Updated</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredArticles.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center h-24">
                            No articles found
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredArticles.map((article) => (
                          <TableRow key={article.id}>
                            <TableCell>
                              <div className="font-medium">{article.title}</div>
                              <div className="text-sm text-muted-foreground line-clamp-1">{article.excerpt}</div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">{article.category}</Badge>
                            </TableCell>
                            <TableCell>{article.views.toLocaleString()}</TableCell>
                            <TableCell>{article.lastUpdated}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button variant="ghost" size="sm">
                                  Edit
                                </Button>
                                <Button variant="ghost" size="sm">
                                  View
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="faq" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Frequently Asked Questions</CardTitle>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add FAQ
                </Button>
              </div>
              <CardDescription>Manage common questions and answers</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="relative w-full mb-4">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Search FAQs..." className="pl-8" />
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">General Questions</h3>
                  <Card>
                    <CardHeader className="py-4">
                      <CardTitle className="text-base">What is Edusync?</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Edusync is a comprehensive school management system designed for educational institutions in
                        Zambia. It helps schools manage students, teachers, classes, attendance, grades, and more.
                      </p>
                    </CardContent>
                    <CardFooter className="flex justify-end py-2">
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                    </CardFooter>
                  </Card>
                  <Card>
                    <CardHeader className="py-4">
                      <CardTitle className="text-base">How do I get started with Edusync?</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        To get started, register your institution on our website. After registration, you'll go through
                        an onboarding process where you'll set up your school details, create admin accounts, and
                        configure basic settings.
                      </p>
                    </CardContent>
                    <CardFooter className="flex justify-end py-2">
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                    </CardFooter>
                  </Card>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Subscription & Billing</h3>
                  <Card>
                    <CardHeader className="py-4">
                      <CardTitle className="text-base">What subscription plans do you offer?</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        We offer several subscription plans: Basic, Standard, Premium, and Enterprise. Each plan
                        includes different features and supports different numbers of students. You can view detailed
                        plan information on our pricing page.
                      </p>
                    </CardContent>
                    <CardFooter className="flex justify-end py-2">
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                    </CardFooter>
                  </Card>
                  <Card>
                    <CardHeader className="py-4">
                      <CardTitle className="text-base">How is billing calculated?</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Billing is based on your subscription plan and the number of students registered in your
                        institution. We offer monthly and annual billing options, with discounts for annual payments.
                      </p>
                    </CardContent>
                    <CardFooter className="flex justify-end py-2">
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                    </CardFooter>
                  </Card>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Technical Support</h3>
                  <Card>
                    <CardHeader className="py-4">
                      <CardTitle className="text-base">How do I get technical support?</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        You can get technical support by creating a support ticket in the Support Center. Our team will
                        respond to your inquiry as soon as possible. You can also check our Knowledge Base for answers
                        to common questions.
                      </p>
                    </CardContent>
                    <CardFooter className="flex justify-end py-2">
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                    </CardFooter>
                  </Card>
                  <Card>
                    <CardHeader className="py-4">
                      <CardTitle className="text-base">What are your support hours?</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Our support team is available Monday to Friday, 8:00 AM to 5:00 PM Zambian time. Premium and
                        Enterprise customers have access to extended support hours and priority response times.
                      </p>
                    </CardContent>
                    <CardFooter className="flex justify-end py-2">
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
