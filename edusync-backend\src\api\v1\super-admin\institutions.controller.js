const { PrismaClient } = require('@prisma/client');
const asyncHandler = require('../../../utils/asyncHandler');

const prisma = new PrismaClient();

/**
 * Get all institutions with pagination and filtering
 */
const getAllInstitutions = asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search, 
    status, 
    type,
    sortBy = 'createdAt',
    order = 'desc'
  } = req.query;

  const skip = (page - 1) * limit;
  const where = {};

  // Add search filter
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { location: { contains: search, mode: 'insensitive' } }
    ];
  }

  // Add status filter
  if (status) {
    where.status = status;
  }

  // Add type filter
  if (type) {
    where.type = type;
  }

  const [institutions, totalInstitutions] = await Promise.all([
    prisma.institution.findMany({
      where,
      skip: parseInt(skip),
      take: parseInt(limit),
      include: {
        _count: {
          select: {
            users: true,
            students: true,
            teachers: true
          }
        }
      },
      orderBy: { [sortBy]: order }
    }),
    prisma.institution.count({ where })
  ]);

  res.status(200).json({
    success: true,
    data: {
      institutions: institutions.map(institution => ({
        id: institution.id,
        name: institution.name,
        type: institution.type,
        email: institution.email,
        phone: institution.phone,
        location: institution.location,
        status: institution.status,
        subscription: institution.subscriptionPlan || 'Basic',
        userCount: institution._count.users,
        studentCount: institution._count.students,
        teacherCount: institution._count.teachers,
        createdAt: institution.createdAt,
        lastActive: institution.lastActiveAt
      })),
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalInstitutions / limit),
        totalInstitutions,
        hasNextPage: skip + institutions.length < totalInstitutions,
        hasPrevPage: page > 1
      }
    }
  });
});

/**
 * Get institution by ID with detailed information
 */
const getInstitutionById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const institution = await prisma.institution.findUnique({
    where: { id },
    include: {
      users: {
        where: { role: { in: ['ADMIN', 'SUPER_ADMIN'] } },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true,
          isActive: true,
          lastLoginAt: true
        }
      },
      _count: {
        select: {
          users: true,
          students: true,
          teachers: true,
          classes: true
        }
      },
      auditLogs: {
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      }
    }
  });

  if (!institution) {
    return res.status(404).json({
      success: false,
      error: 'Institution not found'
    });
  }

  res.status(200).json({
    success: true,
    data: {
      id: institution.id,
      name: institution.name,
      type: institution.type,
      email: institution.email,
      phone: institution.phone,
      address: institution.address,
      location: institution.location,
      website: institution.website,
      status: institution.status,
      subscriptionPlan: institution.subscriptionPlan,
      subscriptionExpiry: institution.subscriptionExpiry,
      maxStudents: institution.maxStudents,
      maxTeachers: institution.maxTeachers,
      storageUsed: institution.storageUsed || 0,
      storageLimit: institution.storageLimit || 1000,
      features: institution.features || [],
      admins: institution.users,
      statistics: {
        totalUsers: institution._count.users,
        totalStudents: institution._count.students,
        totalTeachers: institution._count.teachers,
        totalClasses: institution._count.classes
      },
      recentActivity: institution.auditLogs,
      createdAt: institution.createdAt,
      lastActiveAt: institution.lastActiveAt
    }
  });
});

/**
 * Create new institution
 */
const createInstitution = asyncHandler(async (req, res) => {
  const { 
    name, 
    type, 
    email, 
    phone, 
    address, 
    location, 
    website,
    subscriptionPlan = 'Basic',
    maxStudents = 200,
    maxTeachers = 20,
    storageLimit = 1000
  } = req.body;

  // Check if institution with email already exists
  const existingInstitution = await prisma.institution.findUnique({
    where: { email }
  });

  if (existingInstitution) {
    return res.status(400).json({
      success: false,
      error: 'Institution with this email already exists'
    });
  }

  const institution = await prisma.institution.create({
    data: {
      name,
      type,
      email,
      phone,
      address,
      location,
      website,
      status: 'PENDING',
      subscriptionPlan,
      maxStudents: parseInt(maxStudents),
      maxTeachers: parseInt(maxTeachers),
      storageLimit: parseInt(storageLimit),
      features: []
    }
  });

  // Log the creation
  await prisma.auditLog.create({
    data: {
      userId: req.user.id,
      action: 'INSTITUTION_CREATED',
      details: `New institution created: ${name}`,
      category: 'Institution Management',
      severity: 'Info',
      entityType: 'Institution',
      entityId: institution.id,
      entityName: name,
      userEmail: req.user.email,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      timestamp: new Date()
    }
  });

  res.status(201).json({
    success: true,
    message: 'Institution created successfully',
    data: institution
  });
});

/**
 * Update institution
 */
const updateInstitution = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  const institution = await prisma.institution.findUnique({
    where: { id }
  });

  if (!institution) {
    return res.status(404).json({
      success: false,
      error: 'Institution not found'
    });
  }

  // Check if email is being changed and if it already exists
  if (updateData.email && updateData.email !== institution.email) {
    const existingInstitution = await prisma.institution.findUnique({
      where: { email: updateData.email }
    });

    if (existingInstitution) {
      return res.status(400).json({
        success: false,
        error: 'Email already exists'
      });
    }
  }

  const updatedInstitution = await prisma.institution.update({
    where: { id },
    data: {
      ...updateData,
      updatedAt: new Date()
    }
  });

  // Log the update
  await prisma.auditLog.create({
    data: {
      userId: req.user.id,
      action: 'INSTITUTION_UPDATED',
      details: `Institution updated: ${updatedInstitution.name}`,
      category: 'Institution Management',
      severity: 'Info',
      entityType: 'Institution',
      entityId: institution.id,
      entityName: updatedInstitution.name,
      userEmail: req.user.email,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Institution updated successfully',
    data: updatedInstitution
  });
});

/**
 * Delete institution (soft delete)
 */
const deleteInstitution = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const institution = await prisma.institution.findUnique({
    where: { id },
    include: {
      _count: {
        select: { users: true, students: true }
      }
    }
  });

  if (!institution) {
    return res.status(404).json({
      success: false,
      error: 'Institution not found'
    });
  }

  // Prevent deletion if institution has users or students
  if (institution._count.users > 0 || institution._count.students > 0) {
    return res.status(400).json({
      success: false,
      error: 'Cannot delete institution with existing users or students'
    });
  }

  await prisma.institution.update({
    where: { id },
    data: { 
      status: 'DELETED',
      deletedAt: new Date()
    }
  });

  // Log the deletion
  await prisma.auditLog.create({
    data: {
      userId: req.user.id,
      action: 'INSTITUTION_DELETED',
      details: `Institution deleted: ${institution.name}`,
      category: 'Institution Management',
      severity: 'Important',
      entityType: 'Institution',
      entityId: institution.id,
      entityName: institution.name,
      userEmail: req.user.email,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Institution deleted successfully'
  });
});

/**
 * Approve institution
 */
const approveInstitution = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { subscriptionPlan, features = [] } = req.body;

  const institution = await prisma.institution.findUnique({
    where: { id }
  });

  if (!institution) {
    return res.status(404).json({
      success: false,
      error: 'Institution not found'
    });
  }

  if (institution.status !== 'PENDING') {
    return res.status(400).json({
      success: false,
      error: 'Institution is not pending approval'
    });
  }

  const updatedInstitution = await prisma.institution.update({
    where: { id },
    data: {
      status: 'ACTIVE',
      subscriptionPlan: subscriptionPlan || institution.subscriptionPlan,
      features,
      approvedAt: new Date(),
      approvedBy: req.user.id
    }
  });

  // Log the approval
  await prisma.auditLog.create({
    data: {
      userId: req.user.id,
      action: 'INSTITUTION_APPROVED',
      details: `Institution approved: ${institution.name}`,
      category: 'Institution Management',
      severity: 'Important',
      entityType: 'Institution',
      entityId: institution.id,
      entityName: institution.name,
      userEmail: req.user.email,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Institution approved successfully',
    data: updatedInstitution
  });
});

/**
 * Reject institution
 */
const rejectInstitution = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { reason } = req.body;

  const institution = await prisma.institution.findUnique({
    where: { id }
  });

  if (!institution) {
    return res.status(404).json({
      success: false,
      error: 'Institution not found'
    });
  }

  if (institution.status !== 'PENDING') {
    return res.status(400).json({
      success: false,
      error: 'Institution is not pending approval'
    });
  }

  const updatedInstitution = await prisma.institution.update({
    where: { id },
    data: {
      status: 'REJECTED',
      rejectionReason: reason,
      rejectedAt: new Date(),
      rejectedBy: req.user.id
    }
  });

  // Log the rejection
  await prisma.auditLog.create({
    data: {
      userId: req.user.id,
      action: 'INSTITUTION_REJECTED',
      details: `Institution rejected: ${institution.name}. Reason: ${reason}`,
      category: 'Institution Management',
      severity: 'Important',
      entityType: 'Institution',
      entityId: institution.id,
      entityName: institution.name,
      userEmail: req.user.email,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Institution rejected successfully',
    data: updatedInstitution
  });
});

/**
 * Get institution statistics
 */
const getInstitutionStats = asyncHandler(async (req, res) => {
  const [
    totalInstitutions,
    activeInstitutions,
    pendingInstitutions,
    rejectedInstitutions,
    institutionsByType,
    institutionsByPlan
  ] = await Promise.all([
    prisma.institution.count(),
    prisma.institution.count({ where: { status: 'ACTIVE' } }),
    prisma.institution.count({ where: { status: 'PENDING' } }),
    prisma.institution.count({ where: { status: 'REJECTED' } }),
    prisma.institution.groupBy({
      by: ['type'],
      _count: { id: true }
    }),
    prisma.institution.groupBy({
      by: ['subscriptionPlan'],
      _count: { id: true }
    })
  ]);

  const recentInstitutions = await prisma.institution.findMany({
    take: 5,
    orderBy: { createdAt: 'desc' },
    select: {
      id: true,
      name: true,
      type: true,
      status: true,
      createdAt: true
    }
  });

  res.status(200).json({
    success: true,
    data: {
      totals: {
        total: totalInstitutions,
        active: activeInstitutions,
        pending: pendingInstitutions,
        rejected: rejectedInstitutions
      },
      distributions: {
        byType: institutionsByType.reduce((acc, item) => {
          acc[item.type] = item._count.id;
          return acc;
        }, {}),
        byPlan: institutionsByPlan.reduce((acc, item) => {
          acc[item.subscriptionPlan] = item._count.id;
          return acc;
        }, {})
      },
      recent: recentInstitutions
    }
  });
});

/**
 * Export institutions data
 */
const exportInstitutions = asyncHandler(async (req, res) => {
  const { format = 'csv', status } = req.query;

  // This would generate actual export file
  // For now, return a success message
  res.status(200).json({
    success: true,
    message: `Institutions data exported successfully in ${format} format`,
    downloadUrl: `/exports/institutions-${Date.now()}.${format}`
  });
});

module.exports = {
  getAllInstitutions,
  getInstitutionById,
  createInstitution,
  updateInstitution,
  deleteInstitution,
  approveInstitution,
  rejectInstitution,
  getInstitutionStats,
  exportInstitutions
};
