import { NextResponse } from "next/server"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const page = searchParams.get("page") || "1"
    const per_page = searchParams.get("per_page") || "10"
    const search = searchParams.get("search") || ""
    const subject_id = searchParams.get("subject_id") || ""
    const exam_type = searchParams.get("exam_type") || ""

    // Forward the request to our Flask backend
    const response = await fetch(
      `http://localhost:4000/api/v1/exams?page=${page}&per_page=${per_page}&search=${search}&subject_id=${subject_id}&exam_type=${exam_type}`,
      {
        headers: {
          Cookie: request.headers.get("cookie") || "",
        },
      },
    )

    if (!response.ok) {
      return NextResponse.json({ error: "Failed to fetch exams" }, { status: response.status })
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Exams API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Forward the request to our Flask backend
    const response = await fetch("http://localhost:4000/api/v1/exams", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Cookie: request.headers.get("cookie") || "",
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: data.message || "Failed to create exam" }, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Create exam API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
