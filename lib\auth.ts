import { NextRequest } from "next/server"
import { cookies } from "next/headers"

/**
 * Authenticate user from request
 */
export async function authenticateUser(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get("accessToken")?.value

    if (!token) {
      return {
        user: null,
        error: "Authentication required",
        status: 401
      }
    }

    // Verify token with backend
    const backendUrl = process.env.BACKEND_URL || "http://localhost:4000"
    const response = await fetch(`${backendUrl}/api/v1/auth/me`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      return {
        user: null,
        error: "Invalid or expired token",
        status: 401
      }
    }

    const data = await response.json()
    
    return {
      user: data.data || data.user,
      error: null,
      status: 200
    }
  } catch (error) {
    console.error("Authentication error:", error)
    return {
      user: null,
      error: "Authentication failed",
      status: 500
    }
  }
}

/**
 * Create authentication headers for backend requests
 */
export async function createAuthHeaders(request: NextRequest) {
  const cookieStore = await cookies()
  const token = cookieStore.get("accessToken")?.value

  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  }

  if (token) {
    headers.Authorization = `Bearer ${token}`
  }

  return headers
}

/**
 * Get auth token from request
 */
export async function getAuthToken(request: NextRequest): Promise<string | null> {
  const cookieStore = await cookies()
  return cookieStore.get("accessToken")?.value || null
}

/**
 * Check if user has required role
 */
export function hasRole(user: any, roles: string[]): boolean {
  if (!user || !user.role) {
    return false
  }
  return roles.includes(user.role)
}
