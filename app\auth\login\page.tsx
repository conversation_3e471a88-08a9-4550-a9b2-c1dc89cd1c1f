"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, Eye, EyeOff } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/contexts/auth-context"

// Define role-based routes
const roleBasedRoutes = {
  SUPER_ADMIN: '/super-admin',
  INSTITUTION_ADMIN: '/dashboard',
  SCHOOL_ADMIN: '/dashboard',
  TEACHER: '/dashboard',
  STUDENT: '/dashboard',
  PARENT: '/dashboard',
  STAFF: '/dashboard',
}

export default function LoginPage() {
  const router = useRouter()
  const { login } = useAuth()
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    mfaToken: "",
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [mfaRequired, setMfaRequired] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const result = await login(
        formData.email,
        formData.password,
        formData.mfaToken || undefined,
      )

      if (!result.success) {
        if (result.mfaRequired) {
          setMfaRequired(true)
          setError("Please enter your MFA code to continue")
        } else {
          setError(result.error || "Login failed")
        }
      } else {
        // Handle role-based redirect after successful login
        const role = result.user?.role as keyof typeof roleBasedRoutes
        const redirectUrl = roleBasedRoutes[role] || '/'
        router.push(redirectUrl)
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            <Link href="/" className="text-2xl font-bold text-emerald-600">
              Edusync
            </Link>
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            {mfaRequired ? "Enter MFA Code" : "Log in to your account"}
          </CardTitle>
          <CardDescription className="text-center">
            {mfaRequired
              ? "Enter the 6-digit code from your authenticator app"
              : "Enter your email and password to access your school dashboard"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          <form onSubmit={handleSubmit} className="space-y-4">
            {!mfaRequired ? (
              <>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password">Password</Label>
                    <Link href="/auth/forgot-password" className="text-sm text-emerald-600 hover:text-emerald-500">
                      Forgot password?
                    </Link>
                  </div>
                  <div className="relative">
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={handleChange}
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="mfaToken">MFA Code</Label>
                <Input
                  id="mfaToken"
                  name="mfaToken"
                  placeholder="123456"
                  value={formData.mfaToken}
                  onChange={handleChange}
                  maxLength={6}
                  required
                />
                <p className="text-xs text-gray-500">
                  Enter the 6-digit code from your authenticator app or use a backup code
                </p>
              </div>
            )}

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Logging in..." : mfaRequired ? "Verify & Login" : "Log in"}
            </Button>

            {mfaRequired && (
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={() => {
                  setMfaRequired(false)
                  setFormData((prev) => ({ ...prev, mfaToken: "" }))
                  setError("")
                }}
              >
                Back to Login
              </Button>
            )}
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-sm text-gray-600">
            Don't have an account?{" "}
            <Link href="/auth/register-school" className="text-emerald-600 hover:text-emerald-500 font-medium">
              Register your School
            </Link>
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}
