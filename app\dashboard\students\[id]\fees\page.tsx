import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  FileText,
  CreditCard,
  DollarSign,
  Calendar,
  CheckCircle,
  AlertCircle,
  Download,
  Receipt,
} from "lucide-react"
import Link from "next/link"

export default function StudentFeesPage({ params }: { params: { id: string } }) {
  const studentId = params.id

  // This would normally come from a database
  const student = {
    id: studentId,
    name: "<PERSON>",
    grade: "10th Grade",
    section: "Section A",
    rollNumber: "2023-10-042",
    feeStatus: "Paid",
    academicYear: "2023-2024",
    totalFees: "$12,500",
    paidAmount: "$12,500",
    dueAmount: "$0",
    nextPaymentDate: "N/A",
  }

  // Sample fee structure data
  const feeStructure = [
    { type: "Tuition Fee", amount: "$8,000", status: "Paid", dueDate: "Aug 15, 2023" },
    { type: "Technology Fee", amount: "$1,200", status: "Paid", dueDate: "Aug 15, 2023" },
    { type: "Library Fee", amount: "$800", status: "Paid", dueDate: "Aug 15, 2023" },
    { type: "Laboratory Fee", amount: "$1,000", status: "Paid", dueDate: "Aug 15, 2023" },
    { type: "Sports & Activities", amount: "$1,500", status: "Paid", dueDate: "Aug 15, 2023" },
  ]

  // Sample payment history
  const paymentHistory = [
    {
      id: "INV-2023-001",
      date: "Aug 10, 2023",
      amount: "$12,500",
      method: "Credit Card",
      status: "Paid",
      description: "Full payment for Academic Year 2023-2024",
    },
  ]

  // Sample scholarship/financial aid data
  const financialAid = [
    {
      type: "Merit Scholarship",
      amount: "$2,000",
      status: "Approved",
      awardDate: "Jul 15, 2023",
      details: "Academic Excellence Award",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <Link href={`/dashboard/students/${studentId}`}>
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Back to student profile</span>
              </Link>
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">Fee Details</h1>
          </div>
          <p className="text-muted-foreground ml-10">
            Fee information for {student.name} ({student.grade}, {student.section})
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Receipt className="mr-2 h-4 w-4" />
            Generate Receipt
          </Button>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Download Statement
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Fees</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{student.totalFees}</div>
            <p className="text-xs text-muted-foreground">Academic Year {student.academicYear}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Paid Amount</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-500">{student.paidAmount}</div>
            <p className="text-xs text-muted-foreground">Last payment: Aug 10, 2023</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Due Amount</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{student.dueAmount}</div>
            <p className="text-xs text-muted-foreground">Next due date: {student.nextPaymentDate}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Payment Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-500">{student.feeStatus}</div>
            <p className="text-xs text-muted-foreground">All fees cleared</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="structure" className="space-y-4">
        <TabsList>
          <TabsTrigger value="structure">Fee Structure</TabsTrigger>
          <TabsTrigger value="history">Payment History</TabsTrigger>
          <TabsTrigger value="financial-aid">Financial Aid</TabsTrigger>
          <TabsTrigger value="payment-options">Payment Options</TabsTrigger>
        </TabsList>

        <TabsContent value="structure" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Fee Structure - Academic Year {student.academicYear}</CardTitle>
              <CardDescription>Breakdown of all applicable fees</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-4 bg-muted/50 p-3 text-sm font-medium">
                  <div>Fee Type</div>
                  <div>Amount</div>
                  <div>Due Date</div>
                  <div>Status</div>
                </div>
                <div className="divide-y">
                  {feeStructure.map((fee, index) => (
                    <div key={index} className="grid grid-cols-4 p-3 text-sm">
                      <div className="font-medium">{fee.type}</div>
                      <div>{fee.amount}</div>
                      <div>{fee.dueDate}</div>
                      <div>
                        <span
                          className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                            fee.status === "Paid"
                              ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                              : "bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400"
                          }`}
                        >
                          {fee.status === "Paid" ? (
                            <CheckCircle className="mr-1 h-3 w-3" />
                          ) : (
                            <AlertCircle className="mr-1 h-3 w-3" />
                          )}
                          {fee.status}
                        </span>
                      </div>
                    </div>
                  ))}
                  <div className="grid grid-cols-4 p-3 text-sm font-medium bg-muted/30">
                    <div>Total</div>
                    <div>{student.totalFees}</div>
                    <div></div>
                    <div>
                      <span className="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        Paid
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Payment Schedule</CardTitle>
              <CardDescription>Fee payment timeline</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="relative">
                  <div className="absolute h-full w-px bg-border left-7 top-0"></div>
                  <ol className="space-y-6">
                    <li className="relative pl-14">
                      <div className="absolute left-0 flex h-14 w-14 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30">
                        <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <h3 className="font-medium">Full Payment</h3>
                        <p className="text-sm text-muted-foreground">August 10, 2023</p>
                        <p className="text-sm mt-1">
                          Full payment of {student.totalFees} for the academic year 2023-2024 received.
                        </p>
                      </div>
                    </li>
                    <li className="relative pl-14">
                      <div className="absolute left-0 flex h-14 w-14 items-center justify-center rounded-full bg-muted">
                        <Calendar className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <div>
                        <h3 className="font-medium">Next Academic Year</h3>
                        <p className="text-sm text-muted-foreground">August 15, 2024</p>
                        <p className="text-sm mt-1">Fee payment for the next academic year 2024-2025 will be due.</p>
                      </div>
                    </li>
                  </ol>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payment History</CardTitle>
              <CardDescription>Record of all fee payments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-6 bg-muted/50 p-3 text-sm font-medium">
                  <div>Invoice #</div>
                  <div>Date</div>
                  <div>Amount</div>
                  <div>Payment Method</div>
                  <div>Status</div>
                  <div>Actions</div>
                </div>
                <div className="divide-y">
                  {paymentHistory.map((payment, index) => (
                    <div key={index} className="grid grid-cols-6 p-3 text-sm">
                      <div className="font-medium">{payment.id}</div>
                      <div>{payment.date}</div>
                      <div>{payment.amount}</div>
                      <div>{payment.method}</div>
                      <div>
                        <span className="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400">
                          <CheckCircle className="mr-1 h-3 w-3" />
                          {payment.status}
                        </span>
                      </div>
                      <div>
                        <Button variant="ghost" size="sm">
                          <Receipt className="h-4 w-4" />
                          <span className="sr-only">View Receipt</span>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-4">
                <h3 className="text-sm font-medium mb-2">Payment Details</h3>
                <div className="space-y-2">
                  {paymentHistory.map((payment, index) => (
                    <Card key={index}>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">Payment #{payment.id}</CardTitle>
                        <CardDescription>{payment.date}</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <p className="text-sm text-muted-foreground">Amount</p>
                            <p className="font-medium">{payment.amount}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Method</p>
                            <p className="font-medium">{payment.method}</p>
                          </div>
                          <div className="col-span-2">
                            <p className="text-sm text-muted-foreground">Description</p>
                            <p className="font-medium">{payment.description}</p>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Button variant="outline" size="sm" className="w-full">
                          <Receipt className="mr-2 h-4 w-4" />
                          Download Receipt
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="financial-aid" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Scholarships & Financial Aid</CardTitle>
              <CardDescription>Details of scholarships and financial assistance</CardDescription>
            </CardHeader>
            <CardContent>
              {financialAid.length > 0 ? (
                <div className="space-y-4">
                  {financialAid.map((aid, index) => (
                    <Card key={index}>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">{aid.type}</CardTitle>
                        <CardDescription>Awarded on {aid.awardDate}</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <p className="text-sm text-muted-foreground">Amount</p>
                            <p className="font-medium">{aid.amount}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Status</p>
                            <p className="font-medium text-green-500">{aid.status}</p>
                          </div>
                          <div className="col-span-2">
                            <p className="text-sm text-muted-foreground">Details</p>
                            <p className="font-medium">{aid.details}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="inline-flex h-12 w-12 items-center justify-center rounded-full bg-muted mb-4">
                    <AlertCircle className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h3 className="font-medium text-lg mb-2">No Financial Aid</h3>
                  <p className="text-muted-foreground max-w-sm mx-auto">
                    There are currently no scholarships or financial aid awards for this student.
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button className="w-full">
                <FileText className="mr-2 h-4 w-4" />
                Apply for Scholarship
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Available Scholarships</CardTitle>
              <CardDescription>Scholarships the student may be eligible for</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    name: "Academic Excellence Scholarship",
                    amount: "Up to $5,000",
                    criteria: "GPA of 3.8 or higher",
                    deadline: "March 15, 2024",
                  },
                  {
                    name: "Sports Achievement Scholarship",
                    amount: "Up to $3,000",
                    criteria: "Outstanding performance in sports",
                    deadline: "April 1, 2024",
                  },
                  {
                    name: "Community Service Award",
                    amount: "Up to $2,000",
                    criteria: "100+ hours of community service",
                    deadline: "May 1, 2024",
                  },
                ].map((scholarship, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <h3 className="font-medium">{scholarship.name}</h3>
                    <div className="grid grid-cols-3 gap-2 mt-2 text-sm">
                      <div>
                        <p className="text-muted-foreground">Amount</p>
                        <p>{scholarship.amount}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Deadline</p>
                        <p>{scholarship.deadline}</p>
                      </div>
                      <div className="text-right">
                        <Button variant="outline" size="sm">
                          Apply
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm mt-2">
                      <span className="text-muted-foreground">Eligibility: </span>
                      {scholarship.criteria}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payment-options" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
              <CardDescription>Available options for fee payment</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="flex items-center text-base">
                        <CreditCard className="mr-2 h-5 w-5 text-primary" />
                        Credit/Debit Card
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="text-sm">
                      <p>Pay securely using your credit or debit card.</p>
                      <p className="text-muted-foreground mt-1">Processing time: Immediate</p>
                    </CardContent>
                    <CardFooter>
                      <Button variant="outline" size="sm" className="w-full">
                        Pay Now
                      </Button>
                    </CardFooter>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="flex items-center text-base">
                        <DollarSign className="mr-2 h-5 w-5 text-primary" />
                        Bank Transfer
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="text-sm">
                      <p>Transfer directly to the school's bank account.</p>
                      <p className="text-muted-foreground mt-1">Processing time: 1-3 business days</p>
                    </CardContent>
                    <CardFooter>
                      <Button variant="outline" size="sm" className="w-full">
                        View Details
                      </Button>
                    </CardFooter>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="flex items-center text-base">
                        <Calendar className="mr-2 h-5 w-5 text-primary" />
                        Payment Plan
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="text-sm">
                      <p>Split your payments into manageable installments.</p>
                      <p className="text-muted-foreground mt-1">Available for next academic year</p>
                    </CardContent>
                    <CardFooter>
                      <Button variant="outline" size="sm" className="w-full">
                        Learn More
                      </Button>
                    </CardFooter>
                  </Card>
                </div>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Payment Instructions</CardTitle>
                  </CardHeader>
                  <CardContent className="text-sm space-y-4">
                    <div>
                      <h3 className="font-medium mb-1">Credit/Debit Card Payment</h3>
                      <ol className="list-decimal pl-5 space-y-1">
                        <li>Click on the "Pay Now" button above</li>
                        <li>Enter your card details in the secure payment gateway</li>
                        <li>Confirm the payment amount</li>
                        <li>Submit your payment</li>
                        <li>You will receive a confirmation email and receipt</li>
                      </ol>
                    </div>

                    <div>
                      <h3 className="font-medium mb-1">Bank Transfer</h3>
                      <p>Please use the following details for bank transfers:</p>
                      <div className="mt-2 p-3 bg-muted rounded-md">
                        <p>
                          <span className="font-medium">Bank Name:</span> Education First Bank
                        </p>
                        <p>
                          <span className="font-medium">Account Name:</span> Horizon Academy
                        </p>
                        <p>
                          <span className="font-medium">Account Number:</span> **********
                        </p>
                        <p>
                          <span className="font-medium">Routing Number:</span> *********
                        </p>
                        <p>
                          <span className="font-medium">Reference:</span> Student ID - {student.rollNumber}
                        </p>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium mb-1">Payment Plan</h3>
                      <p>
                        Payment plans allow you to split your tuition into multiple installments. For the next academic
                        year, you can choose from the following options:
                      </p>
                      <ul className="list-disc pl-5 mt-2 space-y-1">
                        <li>Annual payment (5% discount)</li>
                        <li>Semester payment (two equal installments)</li>
                        <li>Quarterly payment (four equal installments)</li>
                        <li>Monthly payment (ten equal installments, small service fee applies)</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
