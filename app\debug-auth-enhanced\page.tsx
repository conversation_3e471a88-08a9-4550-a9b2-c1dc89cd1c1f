"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { useAuthPersistence, useAuthStatus } from "@/hooks/use-auth-persistence"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

export default function DebugAuthEnhanced() {
  const auth = useAuth()
  const authStatus = useAuthStatus()
  const persistence = useAuthPersistence()
  const [testResults, setTestResults] = useState<any>({})
  const [isLoading, setIsLoading] = useState(false)

  const runAuthTests = async () => {
    setIsLoading(true)
    const results: any = {}

    try {
      // Test session endpoint
      console.log("Testing session endpoint...")
      const sessionResponse = await fetch('/api/auth/session', {
        credentials: 'include'
      })
      results.session = {
        status: sessionResponse.status,
        data: sessionResponse.ok ? await sessionResponse.json() : { error: 'Failed to fetch' }
      }

      // Test refresh endpoint
      console.log("Testing refresh endpoint...")
      const refreshResponse = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include'
      })
      results.refresh = {
        status: refreshResponse.status,
        data: refreshResponse.ok ? await refreshResponse.json() : { error: 'Failed to fetch' }
      }

      // Test a protected endpoint
      console.log("Testing protected endpoint...")
      const protectedResponse = await fetch('/api/subjects', {
        credentials: 'include'
      })
      results.protected = {
        status: protectedResponse.status,
        data: protectedResponse.ok ? await protectedResponse.json() : { error: 'Failed to fetch' }
      }

      // Check cookies
      results.cookies = {
        all: document.cookie,
        parsed: document.cookie.split(';').reduce((acc, cookie) => {
          const [key, value] = cookie.trim().split('=')
          if (key) acc[key] = value || ''
          return acc
        }, {} as Record<string, string>)
      }

    } catch (error) {
      results.error = error instanceof Error ? error.message : 'Unknown error'
    }

    setTestResults(results)
    setIsLoading(false)
  }

  const clearAllCookies = () => {
    // Clear all auth-related cookies
    const cookies = ['accessToken', 'refreshToken', 'session']
    cookies.forEach(cookie => {
      document.cookie = `${cookie}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
    })
    
    // Clear localStorage
    persistence.clearPersistenceState()
    
    setTimeout(() => {
      window.location.reload()
    }, 500)
  }

  useEffect(() => {
    runAuthTests()
  }, [])

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Enhanced Authentication Debug</h1>
        <div className="space-x-2">
          <Button onClick={runAuthTests} disabled={isLoading}>
            {isLoading ? "Testing..." : "Run Tests"}
          </Button>
          <Button onClick={clearAllCookies} variant="destructive">
            Clear All & Reload
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Auth Context State */}
        <Card>
          <CardHeader>
            <CardTitle>Auth Context State</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center gap-2">
              <span>Authenticated:</span>
              <Badge variant={auth.isAuthenticated ? "default" : "destructive"}>
                {auth.isAuthenticated ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span>Loading:</span>
              <Badge variant={auth.isLoading ? "secondary" : "outline"}>
                {auth.isLoading ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span>User:</span>
              <Badge variant={auth.user ? "default" : "destructive"}>
                {auth.user ? auth.user.email : "None"}
              </Badge>
            </div>
            {auth.authError && (
              <div className="flex items-center gap-2">
                <span>Error:</span>
                <Badge variant="destructive">{auth.authError}</Badge>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Auth Status Hook */}
        <Card>
          <CardHeader>
            <CardTitle>Auth Status Hook</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center gap-2">
              <span>Is Authenticated:</span>
              <Badge variant={authStatus.isAuthenticated ? "default" : "destructive"}>
                {authStatus.isAuthenticated ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span>Has Error:</span>
              <Badge variant={authStatus.hasError ? "destructive" : "default"}>
                {authStatus.hasError ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span>Loading:</span>
              <Badge variant={authStatus.isLoading ? "secondary" : "outline"}>
                {authStatus.isLoading ? "Yes" : "No"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Persistence State */}
        <Card>
          <CardHeader>
            <CardTitle>Persistence State</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center gap-2">
              <span>Auth Attempts:</span>
              <Badge variant={persistence.persistenceState.authAttempts > 0 ? "destructive" : "default"}>
                {persistence.persistenceState.authAttempts}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span>Rate Limited:</span>
              <Badge variant={persistence.isRateLimited ? "destructive" : "default"}>
                {persistence.isRateLimited ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span>Should Recover:</span>
              <Badge variant={persistence.shouldAttemptRecovery() ? "secondary" : "outline"}>
                {persistence.shouldAttemptRecovery() ? "Yes" : "No"}
              </Badge>
            </div>
            {persistence.persistenceState.lastAuthCheck && (
              <div className="text-sm text-muted-foreground">
                Last Check: {new Date(persistence.persistenceState.lastAuthCheck).toLocaleString()}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card>
          <CardHeader>
            <CardTitle>API Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-96">
              {JSON.stringify(testResults, null, 2)}
            </pre>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex flex-wrap gap-2">
            <Button onClick={auth.retryAuth} disabled={auth.isLoading}>
              Retry Auth
            </Button>
            <Button onClick={auth.clearError} disabled={!auth.authError}>
              Clear Error
            </Button>
            <Button onClick={persistence.clearPersistenceState}>
              Clear Persistence
            </Button>
            <Button onClick={() => window.location.href = '/auth/login'} variant="outline">
              Go to Login
            </Button>
            <Button onClick={() => window.location.href = '/dashboard'} variant="outline">
              Go to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
