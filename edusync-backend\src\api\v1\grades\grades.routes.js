const express = require('express');
const { body, param } = require('express-validator');
const router = express.Router();
const { 
  getGrades, 
  getGradeById, 
  createGradeRecord, 
  updateGradeRecord, 
  deleteGradeRecord, 
  getStudentGradeSummary, 
  getGradeStats 
} = require('./grades.controller');
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const { validateRequest } = require('../../../middleware/validateRequest');

// Apply authentication to all routes
router.use(authenticate);

/**
 * @route GET /api/v1/grades
 * @desc Get grades with filtering and pagination
 * @access Private (Admin, Teacher, Staff)
 */
router.get('/', getGrades);

/**
 * @route GET /api/v1/grades/stats
 * @desc Get grade statistics
 * @access Private (Admin, Teacher)
 */
router.get('/stats', restrictTo('SUPER_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'), getGradeStats);

/**
 * @route GET /api/v1/grades/student/:studentId/summary
 * @desc Get student grade summary with GPA calculation
 * @access Private (Admin, Teacher, Staff)
 */
router.get(
  '/student/:studentId/summary', 
  param('studentId').isUUID().withMessage('Invalid student ID'),
  validateRequest,
  getStudentGradeSummary
);

/**
 * @route GET /api/v1/grades/:id
 * @desc Get grade by ID
 * @access Private (Admin, Teacher, Staff)
 */
router.get(
  '/:id',
  param('id').isUUID().withMessage('Invalid grade ID'),
  validateRequest,
  getGradeById
);

/**
 * @route POST /api/v1/grades
 * @desc Create new grade record
 * @access Private (Admin, Teacher)
 */
router.post(
  '/',
  restrictTo('SUPER_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'),
  [
    body('studentId').isUUID().withMessage('Valid student ID is required'),
    body('subjectId').isUUID().withMessage('Valid subject ID is required'),
    body('marks').isNumeric().withMessage('Marks must be a valid number'),
    body('maxMarks').isNumeric().withMessage('Max marks must be a valid number'),
    body('academicYearId').isUUID().withMessage('Valid academic year ID is required'),
    body('recordedById').isUUID().withMessage('Valid recorded by ID is required'),
    body('academicTermId').optional().isUUID().withMessage('Academic term ID must be valid if provided'),
    body('assignmentId').optional().isUUID().withMessage('Assignment ID must be valid if provided'),
    body('grade').optional().isString().withMessage('Grade must be a string'),
    body('remarks').optional().isString().withMessage('Remarks must be a string'),
    validateRequest,
  ],
  createGradeRecord
);

/**
 * @route PUT /api/v1/grades/:id
 * @desc Update grade record
 * @access Private (Admin, Teacher)
 */
router.put(
  '/:id',
  restrictTo('SUPER_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'),
  param('id').isUUID().withMessage('Invalid grade ID'),
  [
    body('marks').optional().isNumeric().withMessage('Marks must be a valid number'),
    body('maxMarks').optional().isNumeric().withMessage('Max marks must be a valid number'),
    body('grade').optional().isString().withMessage('Grade must be a string'),
    body('remarks').optional().isString().withMessage('Remarks must be a string'),
    validateRequest,
  ],
  updateGradeRecord
);

/**
 * @route DELETE /api/v1/grades/:id
 * @desc Delete grade record
 * @access Private (Admin, Teacher)
 */
router.delete(
  '/:id',
  restrictTo('SUPER_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'),
  param('id').isUUID().withMessage('Invalid grade ID'),
  validateRequest,
  deleteGradeRecord
);

module.exports = router;
