"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, CheckCircle, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/contexts/auth-context"

export default function VerifyEmailPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { verifyEmail } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)

  useEffect(() => {
    const token = searchParams.get("token")
    if (token) {
      handleVerification(token)
    } else {
      setError("Invalid or missing verification token")
      setIsLoading(false)
    }
  }, [searchParams])

  const handleVerification = async (token: string) => {
    try {
      setIsLoading(true)
      const result = await verifyEmail(token)

      if (result.success) {
        setSuccess(true)
      } else {
        setError(result.error || "Email verification failed. The token may be invalid or expired.")
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again or contact support.")
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <div className="flex justify-center mb-4">
              <Loader2 className="h-12 w-12 text-emerald-600 animate-spin" />
            </div>
            <CardTitle className="text-2xl font-bold text-center">Verifying your email...</CardTitle>
            <CardDescription className="text-center">Please wait while we verify your email address.</CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <div className="flex justify-center mb-4">
              <CheckCircle className="h-12 w-12 text-emerald-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-center">Email Verified!</CardTitle>
            <CardDescription className="text-center">
              Your email address has been successfully verified. You can now log in to your institution admin account.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="mb-4 text-gray-600">
              Your institution account has been activated. You now have full access to manage your schools and users.
            </p>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={() => router.push("/auth/login")} className="w-full">
              Go to Login
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            <Link href="/" className="text-2xl font-bold text-emerald-600">
              EduManage
            </Link>
          </div>
          <CardTitle className="text-2xl font-bold text-center">Email Verification Failed</CardTitle>
          <CardDescription className="text-center">We couldn't verify your email address.</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button onClick={() => router.push("/auth/login")} className="w-full">
            Go to Login
          </Button>
          <Button variant="outline" onClick={() => router.push("/auth/register")} className="w-full">
            Register Again
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
