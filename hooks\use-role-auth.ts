import { useAuth } from "@/contexts/auth-context"

const roleBasedRoutes = {
  SUPER_ADMIN: "/super-admin",
  INSTITUTION_ADMIN: "/dashboard",
  SCHOOL_ADMIN: "/dashboard",
  TEACHER: "/dashboard",
  STUDENT: "/dashboard",
  PARENT: "/dashboard",
  STAFF: "/dashboard",
} as const

type UserRole = keyof typeof roleBasedRoutes

export function useRoleAuth(allowedRoles?: UserRole[]) {
  const { user, isLoading, isAuthenticated } = useAuth()

  return {
    user,
    isLoading,
    isAuthorized: !isLoading && isAuthenticated && user && (!allowedRoles || allowedRoles.includes(user.role as UserRole)),
  }
}