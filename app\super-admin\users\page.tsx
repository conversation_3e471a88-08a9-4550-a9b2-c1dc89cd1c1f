"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Plus, Search, MoreHorizontal, Download, X, CheckCircle, AlertCircle, Building } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { superAdminService, institutionService } from "@/lib/backend-api"

// Mock data for users
const mockUsers = [
  {
    id: "1",
    name: "Michael Banda",
    email: "<EMAIL>",
    role: "ADMIN",
    institution: {
      id: "1",
      name: "Lusaka Primary School",
    },
    status: "Active",
    lastLogin: "May 20, 2025 09:15 AM",
    createdAt: "January 15, 2023",
  },
  {
    id: "2",
    name: "Sarah Mwanza",
    email: "<EMAIL>",
    role: "ADMIN",
    institution: {
      id: "1",
      name: "Lusaka Primary School",
    },
    status: "Active",
    lastLogin: "May 19, 2025 02:30 PM",
    createdAt: "January 16, 2023",
  },
  {
    id: "3",
    name: "John Banda",
    email: "<EMAIL>",
    role: "ADMIN",
    institution: {
      id: "2",
      name: "Kitwe Secondary School",
    },
    status: "Active",
    lastLogin: "May 20, 2025 08:45 AM",
    createdAt: "August 22, 2023",
  },
  {
    id: "4",
    name: "Mary Zulu",
    email: "<EMAIL>",
    role: "ADMIN",
    institution: {
      id: "2",
      name: "Kitwe Secondary School",
    },
    status: "Active",
    lastLogin: "May 19, 2025 01:15 PM",
    createdAt: "August 23, 2023",
  },
  {
    id: "5",
    name: "David Mutale",
    email: "<EMAIL>",
    role: "ADMIN",
    institution: {
      id: "4",
      name: "Livingstone College",
    },
    status: "Pending",
    lastLogin: "Never",
    createdAt: "May 18, 2025",
  },
  {
    id: "6",
    name: "Super Admin",
    email: "<EMAIL>",
    role: "SUPER_ADMIN",
    institution: null,
    status: "Active",
    lastLogin: "May 21, 2025 08:30 AM",
    createdAt: "January 1, 2023",
  },
]

// Define interfaces for type safety
interface User {
  id: string
  name: string
  email: string
  role: string
  institution: {
    id: string
    name: string
  }
  status: string
  lastLogin: string
  createdAt: string
}

interface Institution {
  id: string
  name: string
}

interface PaginationState {
  currentPage: number
  totalPages: number
  totalUsers: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [roleFilter, setRoleFilter] = useState("")
  const [statusFilter, setStatusFilter] = useState("")
  const [institutionFilter, setInstitutionFilter] = useState("")
  const [showAddUserDialog, setShowAddUserDialog] = useState(false)
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    totalPages: 1,
    totalUsers: 0,
    hasNextPage: false,
    hasPrevPage: false,
  })
  const [institutions, setInstitutions] = useState<Institution[]>([])
  const [error, setError] = useState<string | null>(null)
  const [newUser, setNewUser] = useState({
    firstName: "",
    lastName: "",
    email: "",
    role: "",
    institutionId: "",
  })

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const params: any = {
          page: pagination.currentPage.toString(),
          limit: "10",
        }

        if (searchQuery) params.search = searchQuery
        if (roleFilter) params.role = roleFilter
        if (statusFilter) params.status = statusFilter
        if (institutionFilter) params.institutionId = institutionFilter

        const result = await superAdminService.getAllUsers(params)
        
        if (result.success) {
          setUsers(result.data.users)
          setPagination(result.data.pagination)
        } else {
          throw new Error(result.error || 'Failed to fetch users')
        }

      } catch (err: any) {
        console.error('Users API error:', err)
        setError(err?.message || 'Failed to fetch users')
      } finally {
        setIsLoading(false)
      }
    }

    fetchUsers()
  }, [searchQuery, roleFilter, statusFilter, institutionFilter, pagination.currentPage])

  useEffect(() => {
    // Fetch institutions for filter dropdown
    const fetchInstitutions = async () => {
      try {
        const result = await institutionService.getAllInstitutions()
        if (result.success) {
          setInstitutions(result.data.institutions || [])
        }
      } catch (err) {
        console.error('Failed to fetch institutions:', err)
      }
    }

    fetchInstitutions()
  }, [])

  const filteredUsers = users

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const result = await superAdminService.createUser(newUser)

      if (result.success) {
        setShowAddUserDialog(false)
        setNewUser({
          firstName: "",
          lastName: "",
          email: "",
          role: "",
          institutionId: "",
        })
        
        // Refresh the users list
        window.location.reload()
      } else {
        throw new Error(result.error || 'Failed to create user')
      }
    } catch (err: any) {
      console.error('Create user error:', err)
      setError(err?.message || 'Failed to create user')
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Users</h1>
          <p className="text-gray-500">Manage all system users across institutions</p>
        </div>
        <div className="flex gap-2">
          <Dialog open={showAddUserDialog} onOpenChange={setShowAddUserDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add User
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Add New User</DialogTitle>
                <DialogDescription>Enter the user details below to create a new user account.</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleAddUser}>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First Name</Label>
                      <Input
                        id="firstName"
                        value={newUser.firstName}
                        onChange={(e) => setNewUser({ ...newUser, firstName: e.target.value })}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input
                        id="lastName"
                        value={newUser.lastName}
                        onChange={(e) => setNewUser({ ...newUser, lastName: e.target.value })}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUser.email}
                      onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                      required
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="role">Role</Label>
                      <Select
                        value={newUser.role}
                        onValueChange={(value) => setNewUser({ ...newUser, role: value })}
                        required
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                          <SelectItem value="ADMIN">Institution Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="institution">Institution</Label>
                      <Select
                        value={newUser.institutionId}
                        onValueChange={(value) => setNewUser({ ...newUser, institutionId: value })}
                        disabled={newUser.role === "SUPER_ADMIN"}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select institution" />
                        </SelectTrigger>
                        <SelectContent>
                          {institutions.map((institution: any) => (
                            <SelectItem key={institution.id} value={institution.id}>
                              {institution.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setShowAddUserDialog(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Add User</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>

          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>User Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search by name or email..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select onValueChange={setRoleFilter} value={roleFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                  <SelectItem value="ADMIN">Institution Admin</SelectItem>
                </SelectContent>
              </Select>

              <Select onValueChange={setStatusFilter} value={statusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="Suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>

              <Select onValueChange={setInstitutionFilter} value={institutionFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by institution" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Institutions</SelectItem>
                  {institutions.map((institution: any) => (
                    <SelectItem key={institution.id} value={institution.id}>
                      {institution.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {(roleFilter || statusFilter || institutionFilter) && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    setRoleFilter("")
                    setStatusFilter("")
                    setInstitutionFilter("")
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <p>Loading users...</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Institution</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Login</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center h-24">
                        No users found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredUsers.map((user: any) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage src={`/placeholder.svg?height=32&width=32`} alt={user.name} />
                              <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{user.name}</p>
                              <p className="text-sm text-gray-500">{user.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {user.role === "SUPER_ADMIN" ? "Super Admin" : "Institution Admin"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {user.institution ? (
                            <Link
                              href={`/super-admin/institutions/${user.institution.id}`}
                              className="flex items-center text-emerald-600 hover:underline"
                            >
                              <Building className="h-4 w-4 mr-1" />
                              {user.institution.name}
                            </Link>
                          ) : (
                            <span className="text-gray-500">System-wide</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {user.status === "Active" ? (
                              <CheckCircle className="h-4 w-4 text-emerald-500 mr-1" />
                            ) : user.status === "Pending" ? (
                              <AlertCircle className="h-4 w-4 text-amber-500 mr-1" />
                            ) : (
                              <AlertCircle className="h-4 w-4 text-red-500 mr-1" />
                            )}
                            <Badge
                              variant={
                                user.status === "Active"
                                  ? "default"
                                  : user.status === "Pending"
                                    ? "outline"
                                    : "destructive"
                              }
                            >
                              {user.status}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>{user.lastLogin}</TableCell>
                        <TableCell>{user.createdAt}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Actions</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Edit User</DropdownMenuItem>
                              {user.status === "Pending" && <DropdownMenuItem>Approve</DropdownMenuItem>}
                              {user.status === "Active" && (
                                <DropdownMenuItem className="text-amber-500">Suspend</DropdownMenuItem>
                              )}
                              {user.status === "Suspended" && <DropdownMenuItem>Reactivate</DropdownMenuItem>}
                              <DropdownMenuItem className="text-red-500">Delete</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
