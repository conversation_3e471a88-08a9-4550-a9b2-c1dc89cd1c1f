import type { <PERSON>ada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"

import { LandingHeader } from "@/components/landing/landing-header"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export const metadata: Metadata = {
  title: "About Us | School Management System",
  description: "Learn about our mission, vision, and the team behind our school management system.",
}

export default function AboutPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <LandingHeader />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 py-20 md:py-28">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Our Story</h1>
                <p className="mx-auto max-w-[700px] text-slate-500 dark:text-slate-400 md:text-xl">
                  We're on a mission to transform education through innovative technology solutions.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Mission & Vision */}
        <section className="py-20">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="grid gap-10 lg:grid-cols-2 items-center">
              <div className="relative aspect-video overflow-hidden rounded-xl">
                <Image
                  src="/placeholder.svg?height=600&width=800"
                  alt="Our mission"
                  width={800}
                  height={600}
                  className="object-cover"
                />
              </div>
              <div className="space-y-4">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Our Mission</h2>
                <p className="text-slate-500 dark:text-slate-400 md:text-xl">
                  To empower educational institutions with innovative technology solutions that streamline
                  administrative processes, enhance communication, and improve learning outcomes.
                </p>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl pt-6">Our Vision</h2>
                <p className="text-slate-500 dark:text-slate-400 md:text-xl">
                  To be the leading provider of comprehensive school management solutions globally, transforming
                  education through technology and enabling institutions to focus on what matters most: teaching and
                  learning.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Our Journey */}
        <section className="bg-slate-50 dark:bg-slate-900 py-20">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Our Journey</h2>
              <p className="mx-auto max-w-[700px] text-slate-500 dark:text-slate-400 md:text-xl mt-4">
                From a small startup to a leading educational technology provider
              </p>
            </div>
            <div className="relative max-w-4xl mx-auto">
              <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-slate-200 dark:bg-slate-800"></div>
              <div className="space-y-12">
                {timeline.map((item, index) => (
                  <div
                    key={index}
                    className={`relative flex items-center ${index % 2 === 0 ? "flex-row-reverse" : ""}`}
                  >
                    <div className="flex-1"></div>
                    <div className="z-10 flex items-center justify-center w-10 h-10 rounded-full bg-primary text-white font-bold">
                      {item.year}
                    </div>
                    <div className="flex-1 p-6 bg-white dark:bg-slate-800 rounded-lg shadow-md ml-6 mr-6">
                      <h3 className="font-bold text-xl mb-2">{item.title}</h3>
                      <p className="text-slate-500 dark:text-slate-400">{item.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Meet Our Team</h2>
              <p className="mx-auto max-w-[700px] text-slate-500 dark:text-slate-400 md:text-xl mt-4">
                Passionate professionals dedicated to transforming education through technology
              </p>
            </div>
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 max-w-5xl mx-auto">
              {team.map((member, index) => (
                <Card key={index} className="overflow-hidden">
                  <div className="aspect-square relative">
                    <Image src={member.image || "/placeholder.svg"} alt={member.name} fill className="object-cover" />
                  </div>
                  <CardContent className="p-6">
                    <h3 className="font-bold text-xl">{member.name}</h3>
                    <p className="text-primary font-medium">{member.position}</p>
                    <p className="text-slate-500 dark:text-slate-400 mt-2">{member.bio}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-primary text-white py-20">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">Join Us in Transforming Education</h2>
                <p className="mx-auto max-w-[600px] text-primary-foreground/80 md:text-xl">
                  Partner with us to bring innovative solutions to your educational institution.
                </p>
              </div>
              <div className="space-x-4">
                <Button size="lg" variant="secondary" asChild>
                  <Link href="/contact">Contact Us</Link>
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="bg-transparent text-white hover:bg-white hover:text-primary"
                  asChild
                >
                  <Link href="/careers">Join Our Team</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}

const timeline = [
  {
    year: "2015",
    title: "The Beginning",
    description: "Founded with a vision to transform education through technology.",
  },
  {
    year: "2017",
    title: "First Major Release",
    description: "Launched our comprehensive school management system with core features.",
  },
  {
    year: "2019",
    title: "Global Expansion",
    description: "Expanded our services to international markets, serving schools across 20+ countries.",
  },
  {
    year: "2021",
    title: "Mobile App Launch",
    description: "Released mobile applications for iOS and Android, enhancing accessibility.",
  },
  {
    year: "2023",
    title: "AI Integration",
    description: "Incorporated AI-powered features for personalized learning and predictive analytics.",
  },
]

const team = [
  {
    name: "Sarah Johnson",
    position: "CEO & Founder",
    bio: "Former educator with 15+ years of experience, passionate about leveraging technology to improve education.",
    image: "/placeholder.svg?height=400&width=400",
  },
  {
    name: "Michael Chen",
    position: "CTO",
    bio: "Tech innovator with expertise in educational technology and AI, leading our product development.",
    image: "/placeholder.svg?height=400&width=400",
  },
  {
    name: "Priya Patel",
    position: "Head of Product",
    bio: "Product strategist focused on creating intuitive and impactful educational tools.",
    image: "/placeholder.svg?height=400&width=400",
  },
  {
    name: "David Wilson",
    position: "Head of Customer Success",
    bio: "Dedicated to ensuring our clients achieve their educational goals through our platform.",
    image: "/placeholder.svg?height=400&width=400",
  },
  {
    name: "Olivia Martinez",
    position: "Lead UX Designer",
    bio: "Creating user-centered designs that make complex educational processes simple and intuitive.",
    image: "/placeholder.svg?height=400&width=400",
  },
  {
    name: "James Taylor",
    position: "Head of Sales",
    bio: "Building partnerships with educational institutions to bring innovative solutions to classrooms worldwide.",
    image: "/placeholder.svg?height=400&width=400",
  },
]
