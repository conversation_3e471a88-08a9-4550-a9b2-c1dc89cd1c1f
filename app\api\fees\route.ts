import { NextRequest, NextResponse } from "next/server"
import { authenticateUser, createAuthHead<PERSON> } from "@/lib/auth"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request)
    
    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error || 'Unauthorized' },
        { status: auth.status }
      )
    }

    const { searchParams } = new URL(request.url)
    const queryString = searchParams.toString()
    
    // Determine the endpoint based on query parameters
    let endpoint = '/api/v1/fees'
    const studentId = searchParams.get('studentId')
    const classId = searchParams.get('classId')
    const stats = searchParams.get('stats')
    
    if (stats === 'true') {
      endpoint = '/api/v1/fees/stats'
    } else if (studentId) {
      endpoint = `/api/v1/fees/student/${studentId}`
    } else if (classId) {
      endpoint = `/api/v1/fees/class/${classId}`
    }
    
    const response = await fetch(`${BACKEND_URL}${endpoint}?${queryString}`, {
      method: 'GET',
      headers: createAuthHeaders(request),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || 'Failed to fetch fees' },
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Fees API error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request)
    
    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error || 'Unauthorized' },
        { status: auth.status }
      )
    }

    const body = await request.json()
    const { bulk, feeId, ...requestData } = body

    let endpoint = '/api/v1/fees'
    let method = 'POST'

    // Determine endpoint based on request type
    if (bulk) {
      endpoint = '/api/v1/fees/bulk'
    } else if (feeId && requestData.amount && requestData.paymentMethod) {
      // This is a payment request
      endpoint = `/api/v1/fees/${feeId}/payment`
    }

    const response = await fetch(`${BACKEND_URL}${endpoint}`, {
      method,
      headers: await createAuthHeaders(request),
      body: JSON.stringify(feeId ? requestData : body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Failed to process request" }, 
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Fees API error:", error)
    return NextResponse.json(
      { success: false, message: "Internal server error" }, 
      { status: 500 }
    )
  }
}

export async function PUT(request: Request) {
  try {
    const auth = await authenticateUser(request as NextRequest)
    
    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error },
        { status: auth.status }
      )
    }

    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Fee ID is required' },
        { status: 400 }
      )
    }

    const response = await fetch(`${BACKEND_URL}/api/v1/fees/${id}`, {
      method: "PUT",
      headers: createAuthHeaders(request as NextRequest),
      body: JSON.stringify(updateData),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Failed to update fee" }, 
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Update fee API error:", error)
    return NextResponse.json(
      { success: false, message: "Internal server error" }, 
      { status: 500 }
    )
  }
}

export async function DELETE(request: Request) {
  try {
    const auth = await authenticateUser(request as NextRequest)
    
    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error },
        { status: auth.status }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Fee ID is required' },
        { status: 400 }
      )
    }

    const response = await fetch(`${BACKEND_URL}/api/v1/fees/${id}`, {
      method: "DELETE",
      headers: createAuthHeaders(request as NextRequest),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Failed to delete fee" }, 
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Delete fee API error:", error)
    return NextResponse.json(
      { success: false, message: "Internal server error" }, 
      { status: 500 }
    )
  }
}
