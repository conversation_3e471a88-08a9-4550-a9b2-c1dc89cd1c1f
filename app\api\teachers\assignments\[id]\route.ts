import { NextRequest, NextResponse } from "next/server"
import { authenticateUser, createAuthHead<PERSON> } from "@/lib/auth"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await authenticateUser(request)

    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error },
        { status: auth.status }
      )
    }

    const { id } = await params
    const body = await request.json()

    const response = await fetch(`${BACKEND_URL}/api/v1/teachers/assignments/${id}`, {
      method: "PUT",
      headers: await createAuthHeaders(request),
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Failed to update teacher assignment" },
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Teacher assignment update API error:", error)
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await authenticateUser(request)

    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error },
        { status: auth.status }
      )
    }

    const { id } = await params

    const response = await fetch(`${BACKEND_URL}/api/v1/teachers/assignments/${id}`, {
      method: "DELETE",
      headers: await createAuthHeaders(request),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Failed to delete teacher assignment" },
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Teacher assignment deletion API error:", error)
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    )
  }
}
