import { NextResponse } from "next/server"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { action, ...data } = body

    let endpoint = ""

    switch (action) {
      case "setup":
        endpoint = "/api/v1/auth/mfa/setup"
        break
      case "enable":
        endpoint = "/api/v1/auth/mfa/enable"
        break
      case "disable":
        endpoint = "/api/v1/auth/mfa/disable"
        break
      default:
        return NextResponse.json({ error: "Invalid MFA action" }, { status: 400 })
    }

    const response = await fetch(`${BACKEND_URL}${endpoint}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: request.headers.get("authorization") || "",
        Cookie: request.headers.get("cookie") || "",
      },
      body: JSON.stringify(data),
    })

    const responseData = await response.json()

    if (!response.ok) {
      return NextResponse.json(responseData, { status: response.status })
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error("MFA API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
