import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, FileText, Award, TrendingUp, TrendingDown, Minus, Download, Printer } from "lucide-react"
import Link from "next/link"

export default function StudentGradesPage({ params }: { params: { id: string } }) {
  const studentId = params.id

  // This would normally come from a database
  const student = {
    id: studentId,
    name: "<PERSON>",
    grade: "10th Grade",
    section: "Section A",
    rollNumber: "2023-10-042",
    gpa: "3.8",
    currentTerm: "Fall 2023",
  }

  // Sample grades data
  const currentTermGrades = [
    {
      subject: "Mathematics",
      teacher: "Dr. <PERSON>",
      midterm: "A",
      final: "A-",
      overall: "A",
      credits: 4,
      comments: "Excellent problem-solving skills",
    },
    {
      subject: "Science",
      teacher: "Ms. <PERSON>",
      midterm: "B+",
      final: "A",
      overall: "A-",
      credits: 4,
      comments: "Great improvement throughout the term",
    },
    {
      subject: "English",
      teacher: "Mr. <PERSON>",
      midterm: "A",
      final: "A+",
      overall: "A+",
      credits: 4,
      comments: "Outstanding writing and analysis",
    },
    {
      subject: "History",
      teacher: "Mrs. Patricia Brown",
      midterm: "B",
      final: "B+",
      overall: "B+",
      credits: 3,
      comments: "Good understanding of historical concepts",
    },
    {
      subject: "Physical Education",
      teacher: "Mr. Michael Johnson",
      midterm: "A",
      final: "A",
      overall: "A",
      credits: 2,
      comments: "Excellent participation and effort",
    },
    {
      subject: "Art",
      teacher: "Ms. Emily Davis",
      midterm: "A-",
      final: "A",
      overall: "A-",
      credits: 2,
      comments: "Creative and dedicated student",
    },
  ]

  // Sample assessment data
  const assessments = [
    { name: "Mathematics Quiz 1", date: "Sep 15, 2023", score: "92%", grade: "A", type: "Quiz", weight: "10%" },
    { name: "Science Lab Report", date: "Sep 20, 2023", score: "88%", grade: "B+", type: "Assignment", weight: "15%" },
    { name: "English Essay", date: "Sep 25, 2023", score: "95%", grade: "A", type: "Assignment", weight: "20%" },
    { name: "Mathematics Midterm", date: "Oct 5, 2023", score: "90%", grade: "A-", type: "Exam", weight: "30%" },
    { name: "History Presentation", date: "Oct 10, 2023", score: "85%", grade: "B", type: "Project", weight: "25%" },
    { name: "Science Quiz 2", date: "Oct 12, 2023", score: "94%", grade: "A", type: "Quiz", weight: "10%" },
  ]

  // Sample historical grades data
  const historicalGrades = [
    { term: "Spring 2023", gpa: "3.7", standing: "Honor Roll" },
    { term: "Winter 2023", gpa: "3.6", standing: "Honor Roll" },
    { term: "Fall 2022", gpa: "3.5", standing: "Honor Roll" },
    { term: "Spring 2022", gpa: "3.4", standing: "Good Standing" },
  ]

  // Helper function to get grade color
  const getGradeColor = (grade: string) => {
    if (grade.startsWith("A")) return "text-green-600 dark:text-green-400"
    if (grade.startsWith("B")) return "text-blue-600 dark:text-blue-400"
    if (grade.startsWith("C")) return "text-amber-600 dark:text-amber-400"
    if (grade.startsWith("D")) return "text-orange-600 dark:text-orange-400"
    return "text-red-600 dark:text-red-400"
  }

  // Helper function to get trend icon
  const getTrendIcon = (current: string, previous: string) => {
    const gradeValues: Record<string, number> = {
      "A+": 4.3,
      A: 4.0,
      "A-": 3.7,
      "B+": 3.3,
      B: 3.0,
      "B-": 2.7,
      "C+": 2.3,
      C: 2.0,
      "C-": 1.7,
      "D+": 1.3,
      D: 1.0,
      "D-": 0.7,
      F: 0,
    }

    const currentValue = gradeValues[current] || 0
    const previousValue = gradeValues[previous] || 0

    if (currentValue > previousValue) {
      return <TrendingUp className="h-4 w-4 text-green-500" />
    } else if (currentValue < previousValue) {
      return <TrendingDown className="h-4 w-4 text-red-500" />
    }
    return <Minus className="h-4 w-4 text-gray-500" />
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <Link href={`/dashboard/students/${studentId}`}>
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Back to student profile</span>
              </Link>
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">Academic Grades</h1>
          </div>
          <p className="text-muted-foreground ml-10">
            Grade report for {student.name} ({student.grade}, {student.section})
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Printer className="mr-2 h-4 w-4" />
            Print Report
          </Button>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Download Report Card
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Current GPA</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{student.gpa}</div>
            <p className="text-xs text-muted-foreground">Out of 4.0</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Current Term</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{student.currentTerm}</div>
            <p className="text-xs text-muted-foreground">Academic Year 2023-24</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Academic Standing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-500">Honor Roll</div>
            <p className="text-xs text-muted-foreground">Top 10% of class</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="current" className="space-y-4">
        <TabsList>
          <TabsTrigger value="current">Current Term</TabsTrigger>
          <TabsTrigger value="assessments">Recent Assessments</TabsTrigger>
          <TabsTrigger value="history">Grade History</TabsTrigger>
          <TabsTrigger value="transcript">Transcript</TabsTrigger>
        </TabsList>

        <TabsContent value="current" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Current Term Grades - {student.currentTerm}</CardTitle>
              <CardDescription>Subject-wise grade breakdown for the current term</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-7 bg-muted/50 p-3 text-sm font-medium">
                  <div className="col-span-2">Subject</div>
                  <div>Midterm</div>
                  <div>Final</div>
                  <div>Overall</div>
                  <div>Credits</div>
                  <div>Trend</div>
                </div>
                <div className="divide-y">
                  {currentTermGrades.map((record, index) => (
                    <div key={index} className="grid grid-cols-7 p-3 text-sm">
                      <div className="col-span-2">
                        <p className="font-medium">{record.subject}</p>
                        <p className="text-xs text-muted-foreground">Teacher: {record.teacher}</p>
                      </div>
                      <div className={getGradeColor(record.midterm)}>{record.midterm}</div>
                      <div className={getGradeColor(record.final)}>{record.final}</div>
                      <div className={getGradeColor(record.overall)}>{record.overall}</div>
                      <div>{record.credits}</div>
                      <div>{getTrendIcon(record.final, record.midterm)}</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                <FileText className="mr-2 h-4 w-4" />
                View Detailed Report
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Teacher Comments</CardTitle>
              <CardDescription>Subject-wise feedback from teachers</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {currentTermGrades.map((record, index) => (
                  <div key={index} className="pb-4 border-b last:border-0 last:pb-0">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{record.subject}</h3>
                      <span className={`${getGradeColor(record.overall)}`}>{record.overall}</span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">{record.teacher}</p>
                    <p className="text-sm mt-2">{record.comments}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assessments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Assessments</CardTitle>
              <CardDescription>Latest quizzes, assignments, and exams</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-6 bg-muted/50 p-3 text-sm font-medium">
                  <div className="col-span-2">Assessment</div>
                  <div>Date</div>
                  <div>Score</div>
                  <div>Grade</div>
                  <div>Weight</div>
                </div>
                <div className="divide-y">
                  {assessments.map((assessment, index) => (
                    <div key={index} className="grid grid-cols-6 p-3 text-sm">
                      <div className="col-span-2">
                        <p className="font-medium">{assessment.name}</p>
                        <p className="text-xs text-muted-foreground">Type: {assessment.type}</p>
                      </div>
                      <div>{assessment.date}</div>
                      <div>{assessment.score}</div>
                      <div className={getGradeColor(assessment.grade)}>{assessment.grade}</div>
                      <div>{assessment.weight}</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                View All Assessments
              </Button>
            </CardFooter>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Assessment Distribution</CardTitle>
                <CardDescription>Breakdown by assessment type</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-60 w-full">
                  {/* This would be a chart in a real implementation */}
                  <div className="h-full w-full flex flex-col">
                    <div className="flex-1 grid grid-cols-4 gap-8 items-end pb-4">
                      {[
                        { type: "Quizzes", height: "70%", color: "bg-blue-500" },
                        { type: "Assignments", height: "85%", color: "bg-green-500" },
                        { type: "Projects", height: "65%", color: "bg-purple-500" },
                        { type: "Exams", height: "90%", color: "bg-amber-500" },
                      ].map((item, index) => (
                        <div key={index} className="relative w-full">
                          <div className={`w-full ${item.color} rounded-t-sm`} style={{ height: item.height }}></div>
                        </div>
                      ))}
                    </div>
                    <div className="grid grid-cols-4 gap-8 text-xs text-center text-muted-foreground">
                      <div>Quizzes</div>
                      <div>Assignments</div>
                      <div>Projects</div>
                      <div>Exams</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Grade Distribution</CardTitle>
                <CardDescription>Breakdown by letter grade</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { grade: "A", percentage: "65%" },
                    { grade: "B", percentage: "25%" },
                    { grade: "C", percentage: "10%" },
                    { grade: "D", percentage: "0%" },
                    { grade: "F", percentage: "0%" },
                  ].map((item, index) => (
                    <div key={index}>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">{item.grade}</span>
                        <span className="text-sm font-medium">{item.percentage}</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            item.grade === "A"
                              ? "bg-green-500"
                              : item.grade === "B"
                                ? "bg-blue-500"
                                : item.grade === "C"
                                  ? "bg-amber-500"
                                  : item.grade === "D"
                                    ? "bg-orange-500"
                                    : "bg-red-500"
                          }`}
                          style={{ width: item.percentage }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Grade History</CardTitle>
              <CardDescription>Academic performance over previous terms</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-3 bg-muted/50 p-3 text-sm font-medium">
                  <div>Term</div>
                  <div>GPA</div>
                  <div>Academic Standing</div>
                </div>
                <div className="divide-y">
                  {historicalGrades.map((term, index) => (
                    <div key={index} className="grid grid-cols-3 p-3 text-sm">
                      <div className="font-medium">{term.term}</div>
                      <div>{term.gpa}</div>
                      <div>
                        <span
                          className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                            term.standing === "Honor Roll"
                              ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                              : "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                          }`}
                        >
                          <Award className="mr-1 h-3 w-3" />
                          {term.standing}
                        </span>
                      </div>
                    </div>
                  ))}
                  <div className="grid grid-cols-3 p-3 text-sm font-medium bg-muted/30">
                    <div>Current ({student.currentTerm})</div>
                    <div>{student.gpa}</div>
                    <div>
                      <span className="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400">
                        <Award className="mr-1 h-3 w-3" />
                        Honor Roll
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>GPA Trend</CardTitle>
              <CardDescription>GPA progression over academic terms</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80 w-full">
                {/* This would be a chart in a real implementation */}
                <div className="h-full w-full flex flex-col">
                  <div className="flex-1 relative">
                    {/* Y-axis labels */}
                    <div className="absolute left-0 top-0 bottom-0 w-10 flex flex-col justify-between text-xs text-muted-foreground">
                      <div>4.0</div>
                      <div>3.5</div>
                      <div>3.0</div>
                      <div>2.5</div>
                      <div>2.0</div>
                    </div>

                    {/* Grid lines */}
                    <div className="absolute left-10 right-0 top-0 bottom-0 flex flex-col justify-between">
                      {[0, 1, 2, 3, 4].map((_, index) => (
                        <div key={index} className="border-b border-muted w-full"></div>
                      ))}
                    </div>

                    {/* Line chart */}
                    <div className="absolute left-12 right-2 top-2 bottom-2">
                      <svg className="w-full h-full">
                        <polyline
                          points="0,60 100,80 200,100 300,120 400,40"
                          fill="none"
                          stroke="hsl(var(--primary))"
                          strokeWidth="2"
                        />
                        {/* Data points */}
                        {[
                          { x: 0, y: 60 },
                          { x: 100, y: 80 },
                          { x: 200, y: 100 },
                          { x: 300, y: 120 },
                          { x: 400, y: 40 },
                        ].map((point, index) => (
                          <circle key={index} cx={point.x} cy={point.y} r="4" fill="hsl(var(--primary))" />
                        ))}
                      </svg>
                    </div>
                  </div>

                  {/* X-axis labels */}
                  <div className="grid grid-cols-5 text-xs text-center text-muted-foreground mt-2">
                    <div>Fall 2022</div>
                    <div>Winter 2023</div>
                    <div>Spring 2023</div>
                    <div>Summer 2023</div>
                    <div>Fall 2023</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transcript" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Official Transcript</CardTitle>
              <CardDescription>Complete academic record</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-6 space-y-6">
                <div className="text-center space-y-2">
                  <h2 className="text-2xl font-bold">Horizon Academy</h2>
                  <p className="text-muted-foreground">123 Education Street, Academic District, New York, NY 10001</p>
                  <h3 className="text-xl font-semibold mt-4">Official Transcript</h3>
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div>
                    <p className="text-sm text-muted-foreground">Student Name</p>
                    <p className="font-medium">{student.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Student ID</p>
                    <p className="font-medium">{student.rollNumber}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Current Grade</p>
                    <p className="font-medium">{student.grade}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Cumulative GPA</p>
                    <p className="font-medium">{student.gpa}/4.0</p>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-semibold mb-3">Fall 2023 (Current)</h4>
                  <div className="rounded-md border">
                    <div className="grid grid-cols-4 bg-muted/50 p-2 text-xs font-medium">
                      <div className="col-span-2">Course</div>
                      <div>Grade</div>
                      <div>Credits</div>
                    </div>
                    <div className="divide-y">
                      {currentTermGrades.map((course, index) => (
                        <div key={index} className="grid grid-cols-4 p-2 text-xs">
                          <div className="col-span-2 font-medium">{course.subject}</div>
                          <div className={getGradeColor(course.overall)}>{course.overall}</div>
                          <div>{course.credits}</div>
                        </div>
                      ))}
                      <div className="grid grid-cols-4 p-2 text-xs font-medium bg-muted/30">
                        <div className="col-span-2">Term GPA</div>
                        <div className="col-span-2">{student.gpa}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {historicalGrades.map((term, index) => (
                  <div key={index} className="pt-4 border-t">
                    <h4 className="font-semibold mb-3">{term.term}</h4>
                    <div className="rounded-md border">
                      <div className="grid grid-cols-4 bg-muted/50 p-2 text-xs font-medium">
                        <div className="col-span-2">Course</div>
                        <div>Grade</div>
                        <div>Credits</div>
                      </div>
                      <div className="divide-y">
                        {/* Sample courses for previous terms */}
                        {[1, 2, 3, 4, 5].map((_, courseIndex) => (
                          <div key={courseIndex} className="grid grid-cols-4 p-2 text-xs">
                            <div className="col-span-2 font-medium">Course {courseIndex + 1}</div>
                            <div>A/B/C</div>
                            <div>3-4</div>
                          </div>
                        ))}
                        <div className="grid grid-cols-4 p-2 text-xs font-medium bg-muted/30">
                          <div className="col-span-2">Term GPA</div>
                          <div className="col-span-2">{term.gpa}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                <div className="pt-4 border-t text-center">
                  <p className="text-sm text-muted-foreground">
                    This transcript is official only if it bears the seal of Horizon Academy.
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">Generated on October 15, 2023</p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">
                <Printer className="mr-2 h-4 w-4" />
                Print Transcript
              </Button>
              <Button>
                <Download className="mr-2 h-4 w-4" />
                Download Official Copy
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
