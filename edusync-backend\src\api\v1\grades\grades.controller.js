const { PrismaClient } = require('@prisma/client');
const { logger } = require('../../../utils/logger');

const prisma = new PrismaClient();

/**
 * Get grades with filtering and pagination
 */
const getGrades = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      studentId,
      subjectId,
      academicYearId,
      academicTermId,
      teacherId,
      classId,
      schoolId,
      type = 'ALL', // GRADE_RECORD, EXAM_RESULT, or ALL
      sortBy = 'recordedAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    let gradeRecords = [];
    let examResults = [];
    let totalCount = 0;

    // Build where clauses
    let gradeRecordWhere = {};
    let examResultWhere = {};

    if (schoolId) {
      gradeRecordWhere.student = { schoolId };
      examResultWhere.student = { schoolId };
    }

    if (studentId) {
      gradeRecordWhere.studentId = studentId;
      examResultWhere.studentId = studentId;
    }

    if (subjectId) {
      gradeRecordWhere.subjectId = subjectId;
      examResultWhere.subjectId = subjectId;
    }

    if (academicYearId) {
      gradeRecordWhere.academicYearId = academicYearId;
      examResultWhere.exam = { academicYearId };
    }

    if (academicTermId) {
      gradeRecordWhere.academicTermId = academicTermId;
      examResultWhere.exam = { 
        ...examResultWhere.exam,
        academicTermId 
      };
    }

    if (teacherId) {
      gradeRecordWhere.recordedById = teacherId;
    }

    if (classId) {
      gradeRecordWhere.student = {
        ...gradeRecordWhere.student,
        studentEnrollments: {
          some: {
            classId,
            isActive: true
          }
        }
      };
      examResultWhere.student = {
        ...examResultWhere.student,
        studentEnrollments: {
          some: {
            classId,
            isActive: true
          }
        }
      };
    }

    // Fetch grade records if requested
    if (type === 'ALL' || type === 'GRADE_RECORD') {
      const [records, recordCount] = await Promise.all([
        prisma.gradeRecord.findMany({
          where: gradeRecordWhere,
          skip: type === 'GRADE_RECORD' ? skip : 0,
          take: type === 'GRADE_RECORD' ? take : undefined,
          orderBy: {
            [sortBy]: sortOrder
          },
          include: {
            student: {
              select: {
                id: true,
                admissionNumber: true,
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            },
            subject: {
              select: {
                id: true,
                name: true,
                code: true
              }
            },
            academicYear: {
              select: {
                id: true,
                name: true,
                year: true
              }
            },
            academicTerm: {
              select: {
                id: true,
                name: true
              }
            },
            assignment: {
              select: {
                id: true,
                title: true,
                maxMarks: true
              }
            },
            recordedBy: {
              select: {
                id: true,
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            }
          }
        }),
        prisma.gradeRecord.count({ where: gradeRecordWhere })
      ]);

      gradeRecords = records.map(record => ({
        id: record.id,
        type: 'GRADE_RECORD',
        marks: record.marks,
        maxMarks: record.maxMarks,
        grade: record.grade,
        percentage: record.percentage,
        remarks: record.remarks,
        recordedAt: record.recordedAt,
        student: {
          id: record.student.id,
          admissionNumber: record.student.admissionNumber,
          name: `${record.student.user.firstName} ${record.student.user.lastName}`
        },
        subject: record.subject,
        academicYear: record.academicYear,
        academicTerm: record.academicTerm,
        assignment: record.assignment,
        recordedBy: record.recordedBy ? {
          id: record.recordedBy.id,
          name: `${record.recordedBy.user.firstName} ${record.recordedBy.user.lastName}`
        } : null
      }));

      if (type === 'GRADE_RECORD') {
        totalCount = recordCount;
      }
    }

    // Fetch exam results if requested
    if (type === 'ALL' || type === 'EXAM_RESULT') {
      const [results, resultCount] = await Promise.all([
        prisma.examResult.findMany({
          where: examResultWhere,
          skip: type === 'EXAM_RESULT' ? skip : 0,
          take: type === 'EXAM_RESULT' ? take : undefined,
          orderBy: {
            submittedAt: sortOrder
          },
          include: {
            student: {
              select: {
                id: true,
                admissionNumber: true,
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            },
            subject: {
              select: {
                id: true,
                name: true,
                code: true
              }
            },
            exam: {
              select: {
                id: true,
                name: true,
                type: true,
                totalMarks: true,
                startDate: true,
                endDate: true,
                academicYear: {
                  select: {
                    id: true,
                    name: true,
                    year: true
                  }
                },
                academicTerm: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }
          }
        }),
        prisma.examResult.count({ where: examResultWhere })
      ]);

      examResults = results.map(result => ({
        id: result.id,
        type: 'EXAM_RESULT',
        marks: result.marksObtained,
        maxMarks: result.exam?.totalMarks || 100,
        grade: result.grade,
        percentage: result.exam?.totalMarks ? 
          (result.marksObtained / result.exam.totalMarks * 100).toFixed(2) : null,
        remarks: result.remarks,
        isAbsent: result.isAbsent,
        recordedAt: result.submittedAt,
        student: {
          id: result.student.id,
          admissionNumber: result.student.admissionNumber,
          name: `${result.student.user.firstName} ${result.student.user.lastName}`
        },
        subject: result.subject,
        exam: result.exam,
        academicYear: result.exam?.academicYear,
        academicTerm: result.exam?.academicTerm
      }));

      if (type === 'EXAM_RESULT') {
        totalCount = resultCount;
      }
    }

    // Combine results if fetching all
    let combinedResults = [];
    if (type === 'ALL') {
      combinedResults = [...gradeRecords, ...examResults]
        .sort((a, b) => {
          const aDate = new Date(a.recordedAt);
          const bDate = new Date(b.recordedAt);
          return sortOrder === 'desc' ? bDate - aDate : aDate - bDate;
        })
        .slice(skip, skip + take);
      
      totalCount = gradeRecords.length + examResults.length;
    } else {
      combinedResults = type === 'GRADE_RECORD' ? gradeRecords : examResults;
    }

    const totalPages = Math.ceil(totalCount / take);

    res.json({
      success: true,
      data: combinedResults,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });

  } catch (error) {
    logger.error('Error fetching grades:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch grades',
      error: error.message
    });
  }
};

/**
 * Get grade by ID
 */
const getGradeById = async (req, res) => {
  try {
    const { id } = req.params;
    const { type } = req.query; // GRADE_RECORD or EXAM_RESULT

    let grade = null;

    if (type === 'EXAM_RESULT') {
      grade = await prisma.examResult.findUnique({
        where: { id },
        include: {
          student: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            }
          },
          subject: true,
          exam: {
            include: {
              academicYear: true,
              academicTerm: true
            }
          }
        }
      });

      if (grade) {
        grade = {
          id: grade.id,
          type: 'EXAM_RESULT',
          marks: grade.marksObtained,
          maxMarks: grade.exam?.totalMarks || 100,
          grade: grade.grade,
          percentage: grade.exam?.totalMarks ? 
            (grade.marksObtained / grade.exam.totalMarks * 100).toFixed(2) : null,
          remarks: grade.remarks,
          isAbsent: grade.isAbsent,
          recordedAt: grade.submittedAt,
          student: grade.student,
          subject: grade.subject,
          exam: grade.exam
        };
      }
    } else {
      grade = await prisma.gradeRecord.findUnique({
        where: { id },
        include: {
          student: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            }
          },
          subject: true,
          academicYear: true,
          academicTerm: true,
          assignment: true,
          recordedBy: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true
                }
              }
            }
          }
        }
      });

      if (grade) {
        grade = {
          id: grade.id,
          type: 'GRADE_RECORD',
          marks: grade.marks,
          maxMarks: grade.maxMarks,
          grade: grade.grade,
          percentage: grade.percentage,
          remarks: grade.remarks,
          recordedAt: grade.recordedAt,
          student: grade.student,
          subject: grade.subject,
          academicYear: grade.academicYear,
          academicTerm: grade.academicTerm,
          assignment: grade.assignment,
          recordedBy: grade.recordedBy
        };
      }
    }

    if (!grade) {
      return res.status(404).json({
        success: false,
        message: 'Grade record not found'
      });
    }

    res.json({
      success: true,
      data: grade
    });

  } catch (error) {
    logger.error('Error fetching grade by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch grade',
      error: error.message
    });
  }
};

/**
 * Create new grade record
 */
const createGradeRecord = async (req, res) => {
  try {
    const {
      studentId,
      subjectId,
      marks,
      maxMarks,
      grade,
      remarks,
      academicYearId,
      academicTermId,
      assignmentId,
      recordedById
    } = req.body;

    // Validate required fields
    if (!studentId || !subjectId || marks === undefined || !maxMarks || !academicYearId || !recordedById) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: studentId, subjectId, marks, maxMarks, academicYearId, recordedById'
      });
    }

    // Validate marks
    if (marks < 0 || marks > maxMarks) {
      return res.status(400).json({
        success: false,
        message: 'Marks must be between 0 and maxMarks'
      });
    }

    // Calculate percentage if not provided
    const percentage = (marks / maxMarks * 100).toFixed(2);

    // Auto-generate grade if not provided
    let finalGrade = grade;
    if (!finalGrade) {
      const percent = parseFloat(percentage);
      if (percent >= 97) finalGrade = 'A+';
      else if (percent >= 93) finalGrade = 'A';
      else if (percent >= 90) finalGrade = 'A-';
      else if (percent >= 87) finalGrade = 'B+';
      else if (percent >= 83) finalGrade = 'B';
      else if (percent >= 80) finalGrade = 'B-';
      else if (percent >= 77) finalGrade = 'C+';
      else if (percent >= 73) finalGrade = 'C';
      else if (percent >= 70) finalGrade = 'C-';
      else if (percent >= 67) finalGrade = 'D+';
      else if (percent >= 63) finalGrade = 'D';
      else if (percent >= 60) finalGrade = 'D-';
      else finalGrade = 'F';
    }

    // Create grade record
    const gradeRecord = await prisma.gradeRecord.create({
      data: {
        studentId,
        subjectId,
        marks: parseFloat(marks),
        maxMarks: parseFloat(maxMarks),
        grade: finalGrade,
        percentage: parseFloat(percentage),
        remarks,
        academicYearId,
        academicTermId,
        assignmentId,
        recordedById
      },
      include: {
        student: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        },
        academicYear: {
          select: {
            id: true,
            name: true,
            year: true
          }
        },
        academicTerm: {
          select: {
            id: true,
            name: true
          }
        },
        assignment: {
          select: {
            id: true,
            title: true
          }
        },
        recordedBy: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: gradeRecord,
      message: 'Grade record created successfully'
    });

  } catch (error) {
    logger.error('Error creating grade record:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create grade record',
      error: error.message
    });
  }
};

/**
 * Update grade record
 */
const updateGradeRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Check if grade record exists
    const existingRecord = await prisma.gradeRecord.findUnique({
      where: { id }
    });

    if (!existingRecord) {
      return res.status(404).json({
        success: false,
        message: 'Grade record not found'
      });
    }

    // Validate marks if being updated
    if (updateData.marks !== undefined && updateData.maxMarks !== undefined) {
      if (updateData.marks < 0 || updateData.marks > updateData.maxMarks) {
        return res.status(400).json({
          success: false,
          message: 'Marks must be between 0 and maxMarks'
        });
      }
    }

    // Recalculate percentage and grade if marks or maxMarks are updated
    if (updateData.marks !== undefined || updateData.maxMarks !== undefined) {
      const marks = updateData.marks !== undefined ? updateData.marks : existingRecord.marks;
      const maxMarks = updateData.maxMarks !== undefined ? updateData.maxMarks : existingRecord.maxMarks;
      
      updateData.percentage = (marks / maxMarks * 100).toFixed(2);
      
      if (!updateData.grade) {
        const percent = parseFloat(updateData.percentage);
        if (percent >= 97) updateData.grade = 'A+';
        else if (percent >= 93) updateData.grade = 'A';
        else if (percent >= 90) updateData.grade = 'A-';
        else if (percent >= 87) updateData.grade = 'B+';
        else if (percent >= 83) updateData.grade = 'B';
        else if (percent >= 80) updateData.grade = 'B-';
        else if (percent >= 77) updateData.grade = 'C+';
        else if (percent >= 73) updateData.grade = 'C';
        else if (percent >= 70) updateData.grade = 'C-';
        else if (percent >= 67) updateData.grade = 'D+';
        else if (percent >= 63) updateData.grade = 'D';
        else if (percent >= 60) updateData.grade = 'D-';
        else updateData.grade = 'F';
      }
    }

    // Update grade record
    const updatedRecord = await prisma.gradeRecord.update({
      where: { id },
      data: {
        ...(updateData.marks !== undefined && { marks: parseFloat(updateData.marks) }),
        ...(updateData.maxMarks !== undefined && { maxMarks: parseFloat(updateData.maxMarks) }),
        ...(updateData.grade && { grade: updateData.grade }),
        ...(updateData.percentage !== undefined && { percentage: parseFloat(updateData.percentage) }),
        ...(updateData.remarks !== undefined && { remarks: updateData.remarks }),
        ...(updateData.academicTermId !== undefined && { academicTermId: updateData.academicTermId }),
        ...(updateData.assignmentId !== undefined && { assignmentId: updateData.assignmentId })
      },
      include: {
        student: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        },
        academicYear: {
          select: {
            id: true,
            name: true,
            year: true
          }
        },
        academicTerm: {
          select: {
            id: true,
            name: true
          }
        },
        assignment: {
          select: {
            id: true,
            title: true
          }
        },
        recordedBy: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    });

    res.json({
      success: true,
      data: updatedRecord,
      message: 'Grade record updated successfully'
    });

  } catch (error) {
    logger.error('Error updating grade record:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update grade record',
      error: error.message
    });
  }
};

/**
 * Delete grade record
 */
const deleteGradeRecord = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if grade record exists
    const gradeRecord = await prisma.gradeRecord.findUnique({
      where: { id }
    });

    if (!gradeRecord) {
      return res.status(404).json({
        success: false,
        message: 'Grade record not found'
      });
    }

    // Delete grade record
    await prisma.gradeRecord.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Grade record deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting grade record:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete grade record',
      error: error.message
    });
  }
};

/**
 * Get student grade summary
 */
const getStudentGradeSummary = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { academicYearId, academicTermId } = req.query;

    let where = {
      studentId
    };

    if (academicYearId) {
      where.academicYearId = academicYearId;
    }

    if (academicTermId) {
      where.academicTermId = academicTermId;
    }

    // Get grade records
    const gradeRecords = await prisma.gradeRecord.findMany({
      where,
      include: {
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        },
        assignment: {
          select: {
            id: true,
            title: true
          }
        }
      },
      orderBy: {
        recordedAt: 'desc'
      }
    });

    // Get exam results
    const examResults = await prisma.examResult.findMany({
      where: {
        studentId,
        ...(academicYearId && {
          exam: {
            academicYearId
          }
        }),
        ...(academicTermId && {
          exam: {
            academicTermId
          }
        })
      },
      include: {
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        },
        exam: {
          select: {
            id: true,
            name: true,
            type: true,
            totalMarks: true
          }
        }
      },
      orderBy: {
        submittedAt: 'desc'
      }
    });

    // Calculate subject-wise summary
    const subjectSummary = {};
    
    // Process grade records
    gradeRecords.forEach(record => {
      const subjectId = record.subject.id;
      if (!subjectSummary[subjectId]) {
        subjectSummary[subjectId] = {
          subject: record.subject,
          gradeRecords: [],
          examResults: [],
          totalMarks: 0,
          totalMaxMarks: 0,
          averagePercentage: 0,
          letterGrade: null
        };
      }
      subjectSummary[subjectId].gradeRecords.push(record);
      subjectSummary[subjectId].totalMarks += record.marks;
      subjectSummary[subjectId].totalMaxMarks += record.maxMarks;
    });

    // Process exam results
    examResults.forEach(result => {
      const subjectId = result.subject.id;
      if (!subjectSummary[subjectId]) {
        subjectSummary[subjectId] = {
          subject: result.subject,
          gradeRecords: [],
          examResults: [],
          totalMarks: 0,
          totalMaxMarks: 0,
          averagePercentage: 0,
          letterGrade: null
        };
      }
      subjectSummary[subjectId].examResults.push(result);
      subjectSummary[subjectId].totalMarks += result.marksObtained;
      subjectSummary[subjectId].totalMaxMarks += (result.exam?.totalMarks || 100);
    });

    // Calculate averages and letter grades
    Object.keys(subjectSummary).forEach(subjectId => {
      const summary = subjectSummary[subjectId];
      if (summary.totalMaxMarks > 0) {
        summary.averagePercentage = (summary.totalMarks / summary.totalMaxMarks * 100).toFixed(2);
        const percent = parseFloat(summary.averagePercentage);
        if (percent >= 97) summary.letterGrade = 'A+';
        else if (percent >= 93) summary.letterGrade = 'A';
        else if (percent >= 90) summary.letterGrade = 'A-';
        else if (percent >= 87) summary.letterGrade = 'B+';
        else if (percent >= 83) summary.letterGrade = 'B';
        else if (percent >= 80) summary.letterGrade = 'B-';
        else if (percent >= 77) summary.letterGrade = 'C+';
        else if (percent >= 73) summary.letterGrade = 'C';
        else if (percent >= 70) summary.letterGrade = 'C-';
        else if (percent >= 67) summary.letterGrade = 'D+';
        else if (percent >= 63) summary.letterGrade = 'D';
        else if (percent >= 60) summary.letterGrade = 'D-';
        else summary.letterGrade = 'F';
      }
    });

    // Calculate overall GPA
    const subjects = Object.values(subjectSummary);
    const overallPercentage = subjects.length > 0 ? 
      subjects.reduce((sum, subj) => sum + parseFloat(subj.averagePercentage), 0) / subjects.length : 0;
    
    // Convert percentage to GPA (4.0 scale)
    let gpa = 0;
    if (overallPercentage >= 97) gpa = 4.3;
    else if (overallPercentage >= 93) gpa = 4.0;
    else if (overallPercentage >= 90) gpa = 3.7;
    else if (overallPercentage >= 87) gpa = 3.3;
    else if (overallPercentage >= 83) gpa = 3.0;
    else if (overallPercentage >= 80) gpa = 2.7;
    else if (overallPercentage >= 77) gpa = 2.3;
    else if (overallPercentage >= 73) gpa = 2.0;
    else if (overallPercentage >= 70) gpa = 1.7;
    else if (overallPercentage >= 67) gpa = 1.3;
    else if (overallPercentage >= 63) gpa = 1.0;
    else if (overallPercentage >= 60) gpa = 0.7;

    res.json({
      success: true,
      data: {
        studentId,
        academicYearId,
        academicTermId,
        overallPercentage: overallPercentage.toFixed(2),
        gpa: gpa.toFixed(2),
        subjectSummary: Object.values(subjectSummary),
        totalSubjects: subjects.length
      }
    });

  } catch (error) {
    logger.error('Error fetching student grade summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student grade summary',
      error: error.message
    });
  }
};

/**
 * Get grade statistics
 */
const getGradeStats = async (req, res) => {
  try {
    const { schoolId, academicYearId, classId, subjectId } = req.query;

    let baseWhere = {};
    let examWhere = {};

    if (schoolId) {
      baseWhere.student = { schoolId };
      examWhere.student = { schoolId };
    }

    if (academicYearId) {
      baseWhere.academicYearId = academicYearId;
      examWhere.exam = { academicYearId };
    }

    if (classId) {
      baseWhere.student = {
        ...baseWhere.student,
        studentEnrollments: {
          some: {
            classId,
            isActive: true
          }
        }
      };
      examWhere.student = {
        ...examWhere.student,
        studentEnrollments: {
          some: {
            classId,
            isActive: true
          }
        }
      };
    }

    if (subjectId) {
      baseWhere.subjectId = subjectId;
      examWhere.subjectId = subjectId;
    }

    const [
      totalGradeRecords,
      totalExamResults,
      gradeDistribution,
      examDistribution,
      averagePerformance
    ] = await Promise.all([
      // Total grade records
      prisma.gradeRecord.count({ where: baseWhere }),
      
      // Total exam results
      prisma.examResult.count({ where: examWhere }),
      
      // Grade distribution for grade records
      prisma.gradeRecord.groupBy({
        by: ['grade'],
        where: baseWhere,
        _count: {
          grade: true
        }
      }),
      
      // Grade distribution for exam results
      prisma.examResult.groupBy({
        by: ['grade'],
        where: examWhere,
        _count: {
          grade: true
        }
      }),
      
      // Average performance
      prisma.gradeRecord.aggregate({
        where: baseWhere,
        _avg: {
          percentage: true,
          marks: true
        }
      })
    ]);

    res.json({
      success: true,
      data: {
        totalGradeRecords,
        totalExamResults,
        gradeDistribution: {
          gradeRecords: gradeDistribution,
          examResults: examDistribution
        },
        averagePerformance: {
          averagePercentage: averagePerformance._avg.percentage?.toFixed(2) || '0.00',
          averageMarks: averagePerformance._avg.marks?.toFixed(2) || '0.00'
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching grade statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch grade statistics',
      error: error.message
    });
  }
};

module.exports = {
  getGrades,
  getGradeById,
  createGradeRecord,
  updateGradeRecord,
  deleteGradeRecord,
  getStudentGradeSummary,
  getGradeStats
};
