const { PrismaClient } = require('@prisma/client');
const asyncHandler = require('../../../utils/asyncHandler');
const crypto = require('crypto');

const prisma = new PrismaClient();

/**
 * Get security settings
 */
const getSecuritySettings = asyncHandler(async (req, res) => {
  // Get security settings from database or return defaults
  const settings = await prisma.systemSettings.findFirst({
    where: { category: 'security' }
  }) || {
    settings: {
      twoFactorAuth: false,
      passwordExpiry: 90,
      minPasswordLength: 8,
      requireSpecialChars: true,
      requireNumbers: true,
      requireUppercase: true,
      maxLoginAttempts: 5,
      sessionTimeout: 30,
      ipRestriction: false,
      allowedIPs: ""
    }
  };

  res.status(200).json({
    success: true,
    data: settings.settings
  });
});

/**
 * Update security settings
 */
const updateSecuritySettings = asyncHandler(async (req, res) => {
  const {
    twoFactorAuth,
    passwordExpiry,
    minPasswordLength,
    requireSpecialChars,
    requireNumbers,
    requireUppercase,
    maxLoginAttempts,
    sessionTimeout,
    ipRestriction,
    allowedIPs
  } = req.body;

  const settingsData = {
    twoFactorAuth: Boolean(twoFactorAuth),
    passwordExpiry: parseInt(passwordExpiry) || 90,
    minPasswordLength: parseInt(minPasswordLength) || 8,
    requireSpecialChars: Boolean(requireSpecialChars),
    requireNumbers: Boolean(requireNumbers),
    requireUppercase: Boolean(requireUppercase),
    maxLoginAttempts: parseInt(maxLoginAttempts) || 5,
    sessionTimeout: parseInt(sessionTimeout) || 30,
    ipRestriction: Boolean(ipRestriction),
    allowedIPs: allowedIPs || ""
  };

  // Upsert security settings
  const settings = await prisma.systemSettings.upsert({
    where: { category: 'security' },
    update: { 
      settings: settingsData,
      updatedAt: new Date()
    },
    create: {
      category: 'security',
      settings: settingsData
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Security Settings Updated',
      details: 'System security settings have been modified',
      category: 'Security',
      severity: 'Important',
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Security settings updated successfully',
    data: settings.settings
  });
});

/**
 * Get security logs
 */
const getSecurityLogs = asyncHandler(async (req, res) => {
  const { page = 1, limit = 50, search } = req.query;
  const skip = (page - 1) * limit;

  const where = {
    OR: [
      { category: 'Security' },
      { category: 'Authentication' },
      { action: { contains: 'login', mode: 'insensitive' } },
      { action: { contains: 'password', mode: 'insensitive' } },
      { action: { contains: 'access', mode: 'insensitive' } }
    ]
  };

  if (search) {
    where.AND = [
      where,
      {
        OR: [
          { action: { contains: search, mode: 'insensitive' } },
          { details: { contains: search, mode: 'insensitive' } },
          { userEmail: { contains: search, mode: 'insensitive' } }
        ]
      }
    ];
  }

  const securityLogs = await prisma.auditLog.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    },
    orderBy: {
      timestamp: 'desc'
    },
    skip: parseInt(skip),
    take: parseInt(limit)
  });

  const totalCount = await prisma.auditLog.count({ where });

  res.status(200).json({
    success: true,
    data: {
      logs: securityLogs,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalCount / limit),
        totalItems: totalCount
      }
    }
  });
});

/**
 * Get API keys
 */
const getApiKeys = asyncHandler(async (req, res) => {
  const apiKeys = await prisma.apiKey.findMany({
    where: { isActive: true },
    select: {
      id: true,
      name: true,
      keyPrefix: true,
      permissions: true,
      lastUsed: true,
      createdAt: true,
      expiresAt: true,
      isActive: true
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  res.status(200).json({
    success: true,
    data: apiKeys
  });
});

/**
 * Create API key
 */
const createApiKey = asyncHandler(async (req, res) => {
  const { name, permissions = [], expiresIn = 365 } = req.body;

  // Generate API key
  const keyData = crypto.randomBytes(32).toString('hex');
  const keyPrefix = keyData.substring(0, 8);
  const hashedKey = crypto.createHash('sha256').update(keyData).digest('hex');

  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + parseInt(expiresIn));

  const apiKey = await prisma.apiKey.create({
    data: {
      name,
      keyPrefix,
      hashedKey,
      permissions,
      expiresAt,
      createdBy: req.user.id,
      isActive: true
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'API Key Created',
      details: `New API key created: ${name}`,
      category: 'Security',
      severity: 'Important',
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(201).json({
    success: true,
    message: 'API key created successfully',
    data: {
      id: apiKey.id,
      name: apiKey.name,
      key: keyData, // Only returned once
      keyPrefix: apiKey.keyPrefix,
      permissions: apiKey.permissions,
      expiresAt: apiKey.expiresAt
    }
  });
});

/**
 * Revoke API key
 */
const revokeApiKey = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const apiKey = await prisma.apiKey.findUnique({
    where: { id }
  });

  if (!apiKey) {
    return res.status(404).json({
      success: false,
      message: 'API key not found'
    });
  }

  await prisma.apiKey.update({
    where: { id },
    data: { 
      isActive: false,
      revokedAt: new Date(),
      revokedBy: req.user.id
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'API Key Revoked',
      details: `API key revoked: ${apiKey.name}`,
      category: 'Security',
      severity: 'Important',
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'API key revoked successfully'
  });
});

/**
 * Rotate API key
 */
const rotateApiKey = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const existingKey = await prisma.apiKey.findUnique({
    where: { id }
  });

  if (!existingKey) {
    return res.status(404).json({
      success: false,
      message: 'API key not found'
    });
  }

  // Generate new API key
  const keyData = crypto.randomBytes(32).toString('hex');
  const keyPrefix = keyData.substring(0, 8);
  const hashedKey = crypto.createHash('sha256').update(keyData).digest('hex');

  const updatedKey = await prisma.apiKey.update({
    where: { id },
    data: {
      keyPrefix,
      hashedKey,
      lastRotated: new Date()
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'API Key Rotated',
      details: `API key rotated: ${existingKey.name}`,
      category: 'Security',
      severity: 'Important',
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'API key rotated successfully',
    data: {
      id: updatedKey.id,
      name: updatedKey.name,
      key: keyData, // Only returned once
      keyPrefix: updatedKey.keyPrefix,
      permissions: updatedKey.permissions,
      expiresAt: updatedKey.expiresAt
    }
  });
});

/**
 * Get security dashboard stats
 */
const getSecurityStats = asyncHandler(async (req, res) => {
  const last24Hours = new Date();
  last24Hours.setHours(last24Hours.getHours() - 24);

  const last7Days = new Date();
  last7Days.setDate(last7Days.getDate() - 7);

  // Get failed login attempts in last 24 hours
  const failedLogins = await prisma.auditLog.count({
    where: {
      action: { contains: 'Failed Login', mode: 'insensitive' },
      timestamp: { gte: last24Hours }
    }
  });

  // Get security events in last 7 days
  const securityEvents = await prisma.auditLog.count({
    where: {
      category: 'Security',
      timestamp: { gte: last7Days }
    }
  });

  // Get active API keys
  const activeApiKeys = await prisma.apiKey.count({
    where: { isActive: true }
  });

  // Get users with 2FA enabled (mock for now)
  const users2FA = Math.floor(Math.random() * 50) + 20;

  res.status(200).json({
    success: true,
    data: {
      failedLoginsLast24h: failedLogins,
      securityEventsLast7d: securityEvents,
      activeApiKeys,
      users2FAEnabled: users2FA,
      systemSecurityScore: 85 // Mock security score
    }
  });
});

module.exports = {
  getSecuritySettings,
  updateSecuritySettings,
  getSecurityLogs,
  getApiKeys,
  createApiKey,
  revokeApiKey,
  rotateApiKey,
  getSecurityStats
};
