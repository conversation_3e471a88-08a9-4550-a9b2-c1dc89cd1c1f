"use client"

import { useAuth } from "@/contexts/auth-context"

export function RoleDebug() {
  const { user, isLoading } = useAuth()

  if (isLoading) {
    return null
  }

  if (!user) {
    return (
      <div className="fixed bottom-4 right-4 bg-red-100 text-red-800 px-4 py-2 rounded-lg shadow-lg">
        Not logged in
      </div>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 bg-green-100 text-green-800 px-4 py-2 rounded-lg shadow-lg">
      <div>Role: {user.role}</div>
      <div className="text-sm">Email: {user.email}</div>
    </div>
  )
} 