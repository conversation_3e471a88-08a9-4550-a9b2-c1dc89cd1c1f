"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useTheme } from "next-themes"
import {
  Users,
  Settings,
  Home,
  Menu,
  Building,
  LogOut,
  CreditCard,
  BarChart,
  Shield,
  HelpCircle,
  Bell,
  Globe,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { She<PERSON>, <PERSON>etContent, SheetTrigger } from "@/components/ui/sheet"
import { useMobile } from "@/hooks/use-mobile"
import { LogoutButton } from "@/components/auth/logout-button"

interface SuperAdminSidebarProps {
  className?: string
}

export function SuperAdminSidebar({ className }: SuperAdminSidebarProps) {
  const pathname = usePathname()
  const isMobile = useMobile()
  const [open, setOpen] = useState(false)
  const { theme } = useTheme()

  const routes = [
    {
      label: "Dashboard",
      icon: Home,
      href: "/super-admin",
      active: pathname === "/super-admin",
    },
    {
      label: "Institutions",
      icon: Building,
      href: "/super-admin/institutions",
      active: pathname.startsWith("/super-admin/institutions"),
    },
    {
      label: "Users",
      icon: Users,
      href: "/super-admin/users",
      active: pathname.startsWith("/super-admin/users"),
    },
    {
      label: "Subscriptions",
      icon: CreditCard,
      href: "/super-admin/subscriptions",
      active: pathname.startsWith("/super-admin/subscriptions"),
    },
    {
      label: "Analytics",
      icon: BarChart,
      href: "/super-admin/analytics",
      active: pathname.startsWith("/super-admin/analytics"),
    },
    {
      label: "Security",
      icon: Shield,
      href: "/super-admin/security",
      active: pathname.startsWith("/super-admin/security"),
    },
    {
      label: "Notifications",
      icon: Bell,
      href: "/super-admin/notifications",
      active: pathname.startsWith("/super-admin/notifications"),
    },
    {
      label: "System Settings",
      icon: Settings,
      href: "/super-admin/settings",
      active: pathname.startsWith("/super-admin/settings"),
    },
    {
      label: "Support",
      icon: HelpCircle,
      href: "/super-admin/support",
      active: pathname.startsWith("/super-admin/support"),
    },
  ]

  const SidebarContent = (
    <div className={cn(
      "h-full flex flex-col border-r", 
      "bg-white dark:bg-gray-950 dark:border-gray-800",
      className
    )}>
      <div className="h-16 flex items-center px-6 border-b dark:border-gray-800">
        <Link href="/super-admin" className="flex items-center">
          <span className="text-xl font-bold text-emerald-600 dark:text-emerald-500">Edusync</span>
          <span className="ml-2 text-xs bg-emerald-100 dark:bg-emerald-950 text-emerald-800 dark:text-emerald-400 px-2 py-0.5 rounded-full">Super Admin</span>
        </Link>
      </div>
      <ScrollArea className="flex-1 py-4">
        <nav className="grid gap-1 px-2">
          {routes.map((route) => (
            <Link
              key={route.href}
              href={route.href}
              onClick={() => setOpen(false)}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                route.active 
                  ? "bg-emerald-50 text-emerald-700 dark:bg-emerald-950/50 dark:text-emerald-400" 
                  : "text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-200",
              )}
            >
              <route.icon className={cn("h-5 w-5", route.active ? "text-emerald-700 dark:text-emerald-400" : "text-gray-400 dark:text-gray-500")} />
              {route.label}
            </Link>
          ))}
        </nav>
      </ScrollArea>
      <div className="mt-auto p-4 border-t dark:border-gray-800">
        <Link href="/dashboard" className="block mb-4">
          <Button variant="outline" className="w-full justify-start dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-800">
            <Globe className="mr-2 h-4 w-4" />
            Switch to Institution
          </Button>
        </Link>
        <LogoutButton
          variant="outline"
          className="w-full justify-start dark:border-gray-700"
          showConfirmation={true}
        />
      </div>
    </div>
  )

  if (isMobile) {
    return (
      <>
        <Sheet open={open} onOpenChange={setOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="md:hidden">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle Menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-72">
            {SidebarContent}
          </SheetContent>
        </Sheet>
      </>
    )
  }

  return SidebarContent
}
