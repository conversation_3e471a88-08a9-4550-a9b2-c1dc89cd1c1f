const express = require('express');
const router = express.Router();
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword,
  getUserStats,
  exportUsers
} = require('./users.controller');

// Apply authentication and super admin role requirement to all routes
router.use(authenticate);
router.use(restrictTo('SUPER_ADMIN'));

// Users routes
router.get('/', getAllUsers);
router.get('/stats', getUserStats);
router.get('/export', exportUsers);
router.get('/:id', getUserById);
router.post('/', createUser);
router.put('/:id', updateUser);
router.delete('/:id', deleteUser);
router.post('/:id/reset-password', resetUserPassword);

module.exports = router;
