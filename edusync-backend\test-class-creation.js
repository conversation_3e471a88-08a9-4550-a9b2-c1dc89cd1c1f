const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testClassCreation() {
  try {
    console.log('🔍 Testing class creation...');
    
    // First, let's check what's in the database
    const existingClasses = await prisma.class.findMany({
      include: {
        school: { select: { id: true, name: true } },
        academicYear: { select: { id: true, name: true } }
      }
    });
    
    console.log(`📊 Current classes in database: ${existingClasses.length}`);
    existingClasses.forEach((cls, index) => {
      console.log(`  ${index + 1}. ${cls.name} (Grade: ${cls.gradeLevel}, School: ${cls.school.name}, Year: ${cls.academicYear.name})`);
    });
    
    // Get available schools and academic years
    const schools = await prisma.school.findMany({ where: { isActive: true } });
    const academicYears = await prisma.academicYear.findMany({ where: { isActive: true } });
    
    console.log(`📚 Available schools: ${schools.length}`);
    schools.forEach((school, index) => {
      console.log(`  ${index + 1}. ${school.name} (ID: ${school.id})`);
    });
    
    console.log(`📅 Available academic years: ${academicYears.length}`);
    academicYears.forEach((year, index) => {
      console.log(`  ${index + 1}. ${year.name} (ID: ${year.id})`);
    });
    
    if (schools.length > 0 && academicYears.length > 0) {
      console.log('\n✅ Prerequisites met for class creation!');
      console.log(`   - School ID to use: ${schools[0].id}`);
      console.log(`   - Academic Year ID to use: ${academicYears[0].id}`);
    } else {
      console.log('\n❌ Missing prerequisites for class creation');
      if (schools.length === 0) console.log('   - No active schools found');
      if (academicYears.length === 0) console.log('   - No active academic years found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testClassCreation();
