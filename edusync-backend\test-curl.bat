@echo off
echo Testing registration endpoint...

curl -X POST http://localhost:4000/api/v1/institutions/register ^
  -F "institutionName=Test School" ^
  -F "institutionType=educational" ^
  -F "address=123 Test Street" ^
  -F "city=Test City" ^
  -F "state=Test State" ^
  -F "country=Zambia" ^
  -F "postalCode=12345" ^
  -F "phone=+260123456789" ^
  -F "institutionEmail=<EMAIL>" ^
  -F "website=https://testschool.edu.zm" ^
  -F "schoolName=Test Primary School" ^
  -F "schoolType=PRIMARY" ^
  -F "schoolEmail=<EMAIL>" ^
  -F "adminFirstName=Test" ^
  -F "adminLastName=Admin" ^
  -F "adminEmail=<EMAIL>" ^
  -F "adminPhone=+260987654321" ^
  -F "adminPassword=TestPassword123!" ^
  -F "studentCount=100" ^
  -F "teacherCount=10" ^
  -F "subscriptionPlan=basic" ^
  -F "referralSource=test" ^
  -F "specialRequirements=None"

echo.
echo Test completed.
