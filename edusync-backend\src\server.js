// Load environment variables FIRST
const dotenv = require('dotenv');
const path = require('path');
dotenv.config({ path: path.join(__dirname, '../.env') });

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const cookieParser = require('cookie-parser');
const { errorHandler } = require('./middleware/errorHandler');
const { logger } = require('./utils/logger');
const { notFound } = require('./middleware/notFound');
const config = require('./config');

// Import API routes
const v1Routes = require('./api/v1');

// Create Express app
const app = express();
const port = config.port;

// Middleware
app.use(helmet());
app.use(cors({
  origin: config.frontend_url,
  credentials: true,
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(morgan('dev'));

// Serve static files from uploads directory
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// API routes
app.use('/api/v1', v1Routes);

// Health check route
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'Server is running' });
});

// API health check
app.get('/api/v1/health', (req, res) => {
  res.status(200).json({ 
    status: 'success', 
    message: 'API is operational',
    version: '1.0.0',
    timestamp: new Date()
  });
});

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Start server
app.listen(port, () => {
  logger.info(`Server running on port ${port}`);
});

module.exports = app;
