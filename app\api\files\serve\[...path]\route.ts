import { type NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest, { params }: { params: { path: string[] } }) {
  try {
    const filePath = params.path.join("/")

    // Forward the request to Flask backend
    const response = await fetch(`${process.env.BACKEND_URL}/api/v1/files/serve/${filePath}`, {
      method: "GET",
    })

    if (!response.ok) {
      return NextResponse.json({ error: "File not found" }, { status: 404 })
    }

    // Get the file content and headers
    const fileBuffer = await response.arrayBuffer()
    const contentType = response.headers.get("content-type") || "application/octet-stream"
    const contentLength = response.headers.get("content-length")

    // Create response with file content
    const fileResponse = new NextResponse(fileBuffer)

    // Set appropriate headers
    fileResponse.headers.set("Content-Type", contentType)
    if (contentLength) {
      fileResponse.headers.set("Content-Length", contentLength)
    }

    // Set cache headers for better performance
    fileResponse.headers.set("Cache-Control", "public, max-age=31536000, immutable")

    return fileResponse
  } catch (error) {
    console.error("File serving API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
