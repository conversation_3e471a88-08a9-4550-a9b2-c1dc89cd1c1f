"use client"

import { HeroS<PERSON><PERSON> } from "@/components/landing/hero-section"
import { FeaturesSection } from "@/components/landing/features-section"
import { PricingSection } from "@/components/landing/pricing-section"
import { TestimonialsSection } from "@/components/landing/testimonials-section"
import { Footer } from "@/components/landing/footer"
import { LandingHeader } from "@/components/landing/landing-header"
import { StatisticsSection } from "@/components/landing/statistics-section"
import { HowItWorksSection } from "@/components/landing/how-it-works-section"
import { FaqSection } from "@/components/landing/faq-section"
import { CtaSection } from "@/components/landing/cta-section"
import { PartnersSection } from "@/components/landing/partners-section"
import { useAuth } from "@/contexts/auth-context"
import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function Home() {
  
  const router = useRouter()




  return (
    <div className="min-h-screen flex flex-col">
      <LandingHeader />
      <main className="flex-grow">
        <HeroSection />
        <PartnersSection />
        <StatisticsSection />
        <FeaturesSection />
        <HowItWorksSection />
        <TestimonialsSection />
        <PricingSection />
        <FaqSection />
        <CtaSection />
      </main>
      <Footer />

    </div>
  )
}
