"use client"

import { useState, useEffect } from "react"
import { Save, Upload, Loader2, <PERSON>ertCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useTenant } from "@/contexts/tenant-context"
import { backendApi } from "@/lib/backend-api"
import { useToast } from "@/hooks/use-toast"

interface InstitutionSettings {
  general: {
    name: string
    email: string
    phone: string
    address: string
    website: string
    logo: string
    description: string
  }
  academic: {
    academicYear: string
    gradeSystem: string
    attendanceTracking: boolean
    examSettings: {
      passingGrade: number
      maxRetakes: number
      enableOnlineExams: boolean
    }
  }
  notifications: {
    smsNotifications: boolean
    emailNotifications: boolean
    pushNotifications: boolean
    notificationEvents: {
      attendance: { email: boolean; sms: boolean }
      grades: { email: boolean; sms: boolean }
      fees: { email: boolean; sms: boolean }
      announcements: { email: boolean; sms: boolean }
      parentMeetings: { email: boolean; sms: boolean }
    }
  }
  localization: {
    language: string
    timezone: string
    dateFormat: string
    currency: string
    currencySymbol: string
  }
  security: {
    twoFactorAuth: boolean
    passwordPolicy: {
      minLength: number
      requireSpecialChars: boolean
      requireNumbers: boolean
      requireUppercase: boolean
    }
    sessionTimeout: number
    ipRestriction: boolean
  }
  features: {
    enabledModules: string[]
    maxStudents: number
    maxTeachers: number
    storageLimit: number
  }
}

export default function SettingsPage() {
  const { currentInstitution } = useTenant()
  const { toast } = useToast()
  const [settings, setSettings] = useState<InstitutionSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState<string | null>(null)
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)

  // Load settings on component mount
  useEffect(() => {
    loadSettings()
  }, [currentInstitution])

  const loadSettings = async () => {
    if (!currentInstitution?.id) return

    try {
      setLoading(true)
      const response = await backendApi.settings.getInstitutionSettings(currentInstitution.id)
      
      if (response.success) {
        setSettings(response.data)
        if (response.data.general.logo) {
          setLogoPreview(response.data.general.logo)
        }
      } else {
        toast({
          title: "Error",
          description: "Failed to load settings",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error loading settings:", error)
      toast({
        title: "Error",
        description: "Failed to load settings",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setLogoFile(file)
      
      // Create preview URL
      const reader = new FileReader()
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const updateSettingsField = (category: keyof InstitutionSettings, field: string, value: any) => {
    if (!settings) return
    
    setSettings(prev => ({
      ...prev!,
      [category]: {
        ...prev![category],
        [field]: value
      }
    }))
  }

  const updateNestedSettingsField = (category: keyof InstitutionSettings, section: string, field: string, value: any) => {
    if (!settings) return
    
    setSettings(prev => ({
      ...prev!,
      [category]: {
        ...prev![category],
        [section]: {
          ...(prev![category] as any)[section],
          [field]: value
        }
      }
    }))
  }

  const saveSettings = async (category: string) => {
    if (!currentInstitution?.id || !settings) return

    try {
      setSaving(category)
      
      const response = await backendApi.settings.updateInstitutionSettings(
        currentInstitution.id,
        category,
        settings[category as keyof InstitutionSettings]
      )
      
      if (response.success) {
        toast({
          title: "Success",
          description: `${category} settings saved successfully`,
        })
      } else {
        throw new Error(response.message || "Failed to save settings")
      }
    } catch (error) {
      console.error("Error saving settings:", error)
      toast({
        title: "Error",
        description: `Failed to save ${category} settings`,
        variant: "destructive",
      })
    } finally {
      setSaving(null)
    }
  }

  const uploadLogo = async () => {
    if (!currentInstitution?.id || !logoFile) return

    try {
      setSaving("logo")
      
      const response = await backendApi.settings.uploadInstitutionLogo(currentInstitution.id, logoFile)
      
      if (response.success) {
        toast({
          title: "Success",
          description: "Logo uploaded successfully",
        })
        // Reload settings to get updated logo URL
        await loadSettings()
      } else {
        throw new Error(response.message || "Failed to upload logo")
      }
    } catch (error) {
      console.error("Error uploading logo:", error)
      toast({
        title: "Error",
        description: "Failed to upload logo",
        variant: "destructive",
      })
    } finally {
      setSaving(null)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading settings...</span>
      </div>
    )
  }

  if (!settings) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load institution settings. Please try refreshing the page.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Institution Settings</h2>
          <p className="text-muted-foreground">Manage your institution's settings and preferences</p>
        </div>
      </div>

      <Tabs defaultValue="general">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="academic">Academic</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="localization">Localization</TabsTrigger>
        </TabsList>
        
        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>General Information</CardTitle>
              <CardDescription>Update your institution's basic information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="institution-name">Institution Name</Label>
                <Input
                  id="institution-name"
                  value={settings.general.name}
                  onChange={(e) => updateSettingsField('general', 'name', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input 
                  id="email" 
                  type="email" 
                  value={settings.general.email} 
                  onChange={(e) => updateSettingsField('general', 'email', e.target.value)} 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input 
                  id="phone" 
                  value={settings.general.phone} 
                  onChange={(e) => updateSettingsField('general', 'phone', e.target.value)} 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="address">Address</Label>
                <Textarea 
                  id="address" 
                  value={settings.general.address} 
                  onChange={(e) => updateSettingsField('general', 'address', e.target.value)} 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input 
                  id="website" 
                  type="url"
                  value={settings.general.website} 
                  onChange={(e) => updateSettingsField('general', 'website', e.target.value)} 
                  placeholder="https://example.com"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea 
                  id="description" 
                  value={settings.general.description} 
                  onChange={(e) => updateSettingsField('general', 'description', e.target.value)} 
                  placeholder="Brief description of your institution"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="logo">Institution Logo</Label>
                <div className="flex items-center gap-4">
                  <div className="h-16 w-16 rounded-md border flex items-center justify-center overflow-hidden">
                    {logoPreview ? (
                      <img
                        src={logoPreview}
                        alt="Logo preview"
                        className="h-full w-full object-contain"
                      />
                    ) : (
                      <div className="text-2xl font-bold text-gray-300">{settings.general.name.charAt(0)}</div>
                    )}
                  </div>
                  <div className="flex-1 space-y-2">
                    <Input id="logo" type="file" accept="image/*" onChange={handleLogoChange} className="hidden" />
                    <div className="flex gap-2">
                      <Button variant="outline" onClick={() => document.getElementById("logo")?.click()}>
                        <Upload className="mr-2 h-4 w-4" />
                        Choose Logo
                      </Button>
                      {logoFile && (
                        <Button 
                          onClick={uploadLogo} 
                          disabled={saving === "logo"}
                        >
                          {saving === "logo" ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                          Upload
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={() => saveSettings('general')} 
                disabled={saving === 'general'}
              >
                {saving === 'general' ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="academic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Academic Settings</CardTitle>
              <CardDescription>Configure academic year and grading system</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="academic-year">Current Academic Year</Label>
                <Select 
                  value={settings.academic.academicYear} 
                  onValueChange={(value) => updateSettingsField('academic', 'academicYear', value)}
                >
                  <SelectTrigger id="academic-year">
                    <SelectValue placeholder="Select academic year" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2022-2023">2022-2023</SelectItem>
                    <SelectItem value="2023-2024">2023-2024</SelectItem>
                    <SelectItem value="2024-2025">2024-2025</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="grade-system">Grading System</Label>
                <RadioGroup 
                  value={settings.academic.gradeSystem} 
                  onValueChange={(value) => updateSettingsField('academic', 'gradeSystem', value)} 
                  className="flex flex-col space-y-1"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="percentage" id="percentage" />
                    <Label htmlFor="percentage">Percentage (0-100%)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="letter" id="letter" />
                    <Label htmlFor="letter">Letter Grades (A, B, C, D, F)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="gpa" id="gpa" />
                    <Label htmlFor="gpa">GPA (0.0-4.0)</Label>
                  </div>
                </RadioGroup>
              </div>
              <div className="flex items-center space-x-2">
                <Switch 
                  id="attendance" 
                  checked={settings.academic.attendanceTracking} 
                  onCheckedChange={(checked) => updateSettingsField('academic', 'attendanceTracking', checked)} 
                />
                <Label htmlFor="attendance">Enable Attendance Tracking</Label>
              </div>
              <div className="space-y-4 pt-4 border-t">
                <h3 className="text-sm font-medium">Exam Settings</h3>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="passing-grade">Passing Grade (%)</Label>
                    <Input
                      id="passing-grade"
                      type="number"
                      min="0"
                      max="100"
                      value={settings.academic.examSettings.passingGrade}
                      onChange={(e) => updateNestedSettingsField('academic', 'examSettings', 'passingGrade', parseInt(e.target.value))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="max-retakes">Maximum Retakes</Label>
                    <Input
                      id="max-retakes"
                      type="number"
                      min="0"
                      max="10"
                      value={settings.academic.examSettings.maxRetakes}
                      onChange={(e) => updateNestedSettingsField('academic', 'examSettings', 'maxRetakes', parseInt(e.target.value))}
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch 
                      id="online-exams" 
                      checked={settings.academic.examSettings.enableOnlineExams} 
                      onCheckedChange={(checked) => updateNestedSettingsField('academic', 'examSettings', 'enableOnlineExams', checked)} 
                    />
                    <Label htmlFor="online-exams">Enable Online Exams</Label>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={() => saveSettings('academic')} 
                disabled={saving === 'academic'}
              >
                {saving === 'academic' ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>Configure how notifications are sent to users</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch 
                  id="sms-notifications" 
                  checked={settings.notifications.smsNotifications} 
                  onCheckedChange={(checked) => updateSettingsField('notifications', 'smsNotifications', checked)} 
                />
                <Label htmlFor="sms-notifications">Enable SMS Notifications</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch 
                  id="email-notifications" 
                  checked={settings.notifications.emailNotifications} 
                  onCheckedChange={(checked) => updateSettingsField('notifications', 'emailNotifications', checked)} 
                />
                <Label htmlFor="email-notifications">Enable Email Notifications</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch 
                  id="push-notifications" 
                  checked={settings.notifications.pushNotifications} 
                  onCheckedChange={(checked) => updateSettingsField('notifications', 'pushNotifications', checked)} 
                />
                <Label htmlFor="push-notifications">Enable Push Notifications</Label>
              </div>
              <div className="space-y-2 pt-4">
                <h3 className="text-sm font-medium">Notification Events</h3>
                <div className="space-y-2">
                  {Object.entries(settings.notifications.notificationEvents).map(([eventKey, eventSettings]) => (
                    <div key={eventKey} className="flex items-center justify-between rounded-md border p-3">
                      <Label className="capitalize">
                        {eventKey === 'parentMeetings' ? 'Parent-Teacher Meetings' : eventKey}
                      </Label>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center space-x-1">
                          <Switch 
                            id={`event-${eventKey}-email`} 
                            checked={eventSettings.email}
                            onCheckedChange={(checked) => {
                              const updatedEvents = {
                                ...settings.notifications.notificationEvents,
                                [eventKey]: { ...eventSettings, email: checked }
                              }
                              updateSettingsField('notifications', 'notificationEvents', updatedEvents)
                            }}
                          />
                          <Label htmlFor={`event-${eventKey}-email`} className="text-xs">
                            Email
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Switch 
                            id={`event-${eventKey}-sms`} 
                            checked={eventSettings.sms}
                            onCheckedChange={(checked) => {
                              const updatedEvents = {
                                ...settings.notifications.notificationEvents,
                                [eventKey]: { ...eventSettings, sms: checked }
                              }
                              updateSettingsField('notifications', 'notificationEvents', updatedEvents)
                            }}
                          />
                          <Label htmlFor={`event-${eventKey}-sms`} className="text-xs">
                            SMS
                          </Label>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={() => saveSettings('notifications')} 
                disabled={saving === 'notifications'}
              >
                {saving === 'notifications' ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="localization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Localization Settings</CardTitle>
              <CardDescription>Configure language, timezone, and date format</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="language">Default Language</Label>
                <Select 
                  value={settings.localization.language} 
                  onValueChange={(value) => updateSettingsField('localization', 'language', value)}
                >
                  <SelectTrigger id="language">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="english">English</SelectItem>
                    <SelectItem value="nyanja">Nyanja</SelectItem>
                    <SelectItem value="bemba">Bemba</SelectItem>
                    <SelectItem value="tonga">Tonga</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="timezone">Timezone</Label>
                <Select 
                  value={settings.localization.timezone} 
                  onValueChange={(value) => updateSettingsField('localization', 'timezone', value)}
                >
                  <SelectTrigger id="timezone">
                    <SelectValue placeholder="Select timezone" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Africa/Lusaka">Africa/Lusaka (GMT+2)</SelectItem>
                    <SelectItem value="Africa/Johannesburg">Africa/Johannesburg (GMT+2)</SelectItem>
                    <SelectItem value="UTC">UTC</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="date-format">Date Format</Label>
                <RadioGroup 
                  value={settings.localization.dateFormat} 
                  onValueChange={(value) => updateSettingsField('localization', 'dateFormat', value)} 
                  className="flex flex-col space-y-1"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="dd/mm/yyyy" id="dd/mm/yyyy" />
                    <Label htmlFor="dd/mm/yyyy">DD/MM/YYYY (31/12/2023)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="mm/dd/yyyy" id="mm/dd/yyyy" />
                    <Label htmlFor="mm/dd/yyyy">MM/DD/YYYY (12/31/2023)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="yyyy-mm-dd" id="yyyy-mm-dd" />
                    <Label htmlFor="yyyy-mm-dd">YYYY-MM-DD (2023-12-31)</Label>
                  </div>
                </RadioGroup>
              </div>
              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Input 
                  id="currency" 
                  value={settings.localization.currency} 
                  onChange={(e) => updateSettingsField('localization', 'currency', e.target.value)} 
                  placeholder="e.g., USD, ZMW, GBP"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="currency-symbol">Currency Symbol</Label>
                <Input 
                  id="currency-symbol" 
                  value={settings.localization.currencySymbol} 
                  onChange={(e) => updateSettingsField('localization', 'currencySymbol', e.target.value)} 
                  placeholder="e.g., $, K, £"
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={() => saveSettings('localization')} 
                disabled={saving === 'localization'}
              >
                {saving === 'localization' ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
