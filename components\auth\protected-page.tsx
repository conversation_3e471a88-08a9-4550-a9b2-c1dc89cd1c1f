"use client"

import { <PERSON>actNode } from "react"
import { useAuthCheck } from "@/hooks/use-auth-check"
import { Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface ProtectedPageProps {
  children: ReactNode
  requiredRoles?: string[]
  requireEmailVerified?: boolean
  redirectTo?: string
  fallback?: ReactNode
}

export function ProtectedPage({
  children,
  requiredRoles = [],
  requireEmailVerified = false,
  redirectTo = "/auth/login",
  fallback
}: ProtectedPageProps) {
  const { authenticated, user, loading, error, sessionValid } = useAuthCheck({
    requiredRoles,
    requireEmailVerified,
    redirectTo
  })

  // Show loading state
  if (loading) {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Checking authentication...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Alert className="max-w-md">
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  // Show warning if session is not fully valid
  if (authenticated && !sessionValid) {
    console.warn("Session validation warning - user may need to re-authenticate soon")
  }

  // If authenticated and authorized, render children
  if (authenticated && user) {
    return <>{children}</>
  }

  // Fallback - should not reach here if redirects are working
  return fallback || (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <p className="text-muted-foreground">Redirecting...</p>
      </div>
    </div>
  )
}

// Convenience components for specific roles
export function AdminProtectedPage({ children, ...props }: Omit<ProtectedPageProps, 'requiredRoles'>) {
  return (
    <ProtectedPage 
      {...props} 
      requiredRoles={["SUPER_ADMIN", "INSTITUTION_ADMIN", "SCHOOL_ADMIN", "ADMIN"]}
    >
      {children}
    </ProtectedPage>
  )
}

export function TeacherProtectedPage({ children, ...props }: Omit<ProtectedPageProps, 'requiredRoles'>) {
  return (
    <ProtectedPage 
      {...props} 
      requiredRoles={["SUPER_ADMIN", "INSTITUTION_ADMIN", "SCHOOL_ADMIN", "ADMIN", "TEACHER"]}
    >
      {children}
    </ProtectedPage>
  )
}

export function SuperAdminProtectedPage({ children, ...props }: Omit<ProtectedPageProps, 'requiredRoles' | 'redirectTo'>) {
  return (
    <ProtectedPage 
      {...props} 
      requiredRoles={["SUPER_ADMIN"]}
      redirectTo="/dashboard"
    >
      {children}
    </ProtectedPage>
  )
}

export function InstitutionProtectedPage({ children, ...props }: Omit<ProtectedPageProps, 'requiredRoles'>) {
  return (
    <ProtectedPage 
      {...props} 
      requiredRoles={["SUPER_ADMIN", "INSTITUTION_ADMIN", "SCHOOL_ADMIN", "ADMIN", "TEACHER", "STAFF"]}
    >
      {children}
    </ProtectedPage>
  )
}
