"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Plus, Search, MoreHorizontal, Download, X, Loader2 } from "lucide-react"
import { teacherService, subjectService, classService } from "@/lib/backend-api"
import { useTenant } from "@/contexts/tenant-context"
import { toast } from "sonner"

interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  phoneNumber?: string
  isActive: boolean
  role: string
  dateOfBirth?: string
  gender?: string
  address?: string
  emergencyContact?: string
  createdAt: string
  updatedAt?: string
}

interface Assignment {
  id: string
  isClassTeacher: boolean
  startDate: string
  endDate?: string
  isActive: boolean
  subject?: {
    id: string
    name: string
    code: string
    description?: string
  }
  department?: {
    id: string
    name: string
    description?: string
  }
}

interface Teacher {
  id: string
  userId: string
  user: User
  employeeId?: string
  qualification?: string
  experience?: number
  specialization?: string
  salary?: number
  contractType?: string
  hireDate?: string
  departmentId?: string
  assignments?: Assignment[]
  department?: {
    id: string
    name: string
    description?: string
  }
  _count?: {
    assignments: number
    timetableSlots: number
    createdAssignments: number
  }
}

interface Subject {
  id: string
  name: string
  code: string
}

interface Class {
  id: string
  name: string
}

interface TeachersResponse {
  success: boolean
  data: Teacher[]
  pagination?: {
    currentPage: number
    totalPages: number
    totalRecords: number
    hasNext: boolean
    hasPrev: boolean
  }
  message?: string
}

interface Department {
  id: string
  name: string
  description?: string
}

export default function TeachersPage() {
  const { currentInstitution } = useTenant()
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isDepartmentDialogOpen, setIsDepartmentDialogOpen] = useState(false)
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null)
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [classes, setClasses] = useState<Class[]>([])

  // Form state for creating teacher
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    phone: "",
    dateOfBirth: "",
    gender: "",
    address: "",
    emergencyContact: "",
    employeeId: "",
    qualification: "",
    experience: "",
    specialization: "",
    salary: "",
    contractType: "FULL_TIME",
    hireDate: "",
  })

  // Fetch teachers
  const fetchTeachers = async () => {
    if (!currentInstitution?.id) return

    try {
      setIsLoading(true)
      const params = {
        page: currentPage.toString(),
        limit: "10",
        search: searchQuery,
        institutionId: currentInstitution.id,
        ...(departmentFilter && departmentFilter !== "all" && { department: departmentFilter }),
        ...(statusFilter && statusFilter !== "all" && { status: statusFilter }),
      }

     

      const response = await fetch(`/api/teachers?${new URLSearchParams(params).toString()}`, {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json() as TeachersResponse

      if (data.success) {
        setTeachers(data.data)
        if (data.pagination) {
          setTotalPages(data.pagination.totalPages)
        }
      } else {
        toast.error(data.message || "Failed to fetch teachers")
      }
    } catch (error) {
      console.error("Error fetching teachers:", error)
      toast.error("Failed to fetch teachers")
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch departments for filter and assignment
  const fetchDepartments = async () => {
    try {
      if (!currentInstitution?.id) return

      const response = await fetch(`/api/departments?institutionId=${currentInstitution.id}`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.data) {
          setDepartments(data.data)
        }
      }
    } catch (error) {
      console.error("Error fetching departments:", error)
    }
  }

  // Fetch subjects and classes for dropdowns
  const fetchFormData = async () => {
    if (!currentInstitution?.id) return

    try {
      const [subjectsResponse, classesResponse] = await Promise.all([
        subjectService.getSubjects({ institutionId: currentInstitution.id }),
        classService.getClasses({ institutionId: currentInstitution.id })
      ])

      if (subjectsResponse.success && subjectsResponse.data) {
        setSubjects(subjectsResponse.data)
      }
      if (classesResponse.success && classesResponse.data) {
        setClasses(classesResponse.data)
      }
    } catch (error) {
      console.error("Error fetching form data:", error)
    }
  }

  useEffect(() => {
    fetchTeachers()
  }, [currentPage, searchQuery, departmentFilter, statusFilter, currentInstitution?.id])

  useEffect(() => {
    if (currentInstitution?.id) {
      fetchFormData()
      fetchDepartments()
    }
  }, [currentInstitution?.id])

  const handleCreateTeacher = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!currentInstitution?.id) {
      toast.error("No institution selected")
      return
    }

    // Validate required fields
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
      toast.error("Please fill in all required fields")
      return
    }

    // Validate password
    if (formData.password.length < 8) {
      toast.error("Password must be at least 8 characters long")
      return
    }

    try {
      const teacherData = {
        ...formData,
        institutionId: currentInstitution.id,
        experience: formData.experience ? parseInt(formData.experience) : undefined,
        salary: formData.salary ? parseFloat(formData.salary) : undefined,
      }

      const response = await fetch('/api/teachers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(teacherData),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        toast.success("Teacher created successfully")
        setIsCreateDialogOpen(false)
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          password: "",
          phone: "",
          dateOfBirth: "",
          gender: "",
          address: "",
          emergencyContact: "",
          employeeId: "",
          qualification: "",
          experience: "",
          specialization: "",
          salary: "",
          contractType: "FULL_TIME",
          hireDate: "",
        })
        fetchTeachers()
      } else {
        toast.error(data.message || "Failed to create teacher")
      }
    } catch (error) {
      console.error("Error creating teacher:", error)
      toast.error("Failed to create teacher")
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Handle department assignment
  const handleAssignDepartment = (teacher: Teacher) => {
    setSelectedTeacher(teacher)
    setIsDepartmentDialogOpen(true)
  }

  const handleDepartmentAssignment = async (departmentId: string) => {
    if (!selectedTeacher) return

    try {
      const response = await fetch('/api/teachers/department', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          teacherId: selectedTeacher.id,
          departmentId: departmentId
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Department assigned to ${selectedTeacher.user.firstName} ${selectedTeacher.user.lastName}`)
        setIsDepartmentDialogOpen(false)
        setSelectedTeacher(null)
        fetchTeachers() // Refresh the list
      } else {
        toast.error(data.message || 'Failed to assign department')
      }
    } catch (error) {
      console.error('Error assigning department:', error)
      toast.error('Failed to assign department')
    }
  }

  const handleRemoveDepartment = async (teacher: Teacher) => {
    try {
      const response = await fetch(`/api/teachers/${teacher.id}/department`, {
        method: 'DELETE',
        credentials: 'include',
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Department removed from ${teacher.user.firstName} ${teacher.user.lastName}`)
        fetchTeachers() // Refresh the list
      } else {
        toast.error(data.message || 'Failed to remove department')
      }
    } catch (error) {
      console.error('Error removing department:', error)
      toast.error('Failed to remove department')
    }
  }

  // Client-side filtering as fallback
  const filteredTeachers = teachers.filter(teacher => {
    // Department filter
    if (departmentFilter !== "all") {
      const matchesDepartment =
        teacher.department?.name === departmentFilter ||
        teacher.department?.name.toLowerCase().includes(departmentFilter.toLowerCase()) ||
        teacher.specialization?.toLowerCase().includes(departmentFilter.toLowerCase())

      if (!matchesDepartment) return false
    }

    // Status filter
    if (statusFilter !== "all") {
      switch (statusFilter.toLowerCase()) {
        case 'active':
          if (!teacher.user.isActive) return false
          break
        case 'inactive':
          if (teacher.user.isActive) return false
          break
        case 'on_leave':
          // For now, treat as inactive
          if (teacher.user.isActive) return false
          break
      }
    }

    return true
  })

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A"
    return new Date(dateString).toLocaleDateString()
  }

  const getStatusBadgeVariant = (isActive: boolean | undefined) => {
    if (isActive === undefined || isActive === null) {
      return "secondary"
    }
    return isActive ? "default" : "destructive"
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Teachers</h1>
          <p className="text-gray-500">Manage teacher records and assignments</p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Teacher
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Add New Teacher</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleCreateTeacher} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange("firstName", e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange("lastName", e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="password">Password *</Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={(e) => handleInputChange("password", e.target.value)}
                    placeholder="Minimum 8 characters"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange("phone", e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="employeeId">Employee ID</Label>
                    <Input
                      id="employeeId"
                      value={formData.employeeId}
                      onChange={(e) => handleInputChange("employeeId", e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="dateOfBirth">Date of Birth</Label>
                    <Input
                      id="dateOfBirth"
                      type="date"
                      value={formData.dateOfBirth}
                      onChange={(e) => handleInputChange("dateOfBirth", e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="gender">Gender</Label>
                    <Select onValueChange={(value) => handleInputChange("gender", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="MALE">Male</SelectItem>
                        <SelectItem value="FEMALE">Female</SelectItem>
                        <SelectItem value="OTHER">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange("address", e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="emergencyContact">Emergency Contact</Label>
                  <Input
                    id="emergencyContact"
                    value={formData.emergencyContact}
                    onChange={(e) => handleInputChange("emergencyContact", e.target.value)}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="qualification">Qualification</Label>
                    <Input
                      id="qualification"
                      value={formData.qualification}
                      onChange={(e) => handleInputChange("qualification", e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="specialization">Specialization/Department</Label>
                    <Input
                      id="specialization"
                      value={formData.specialization}
                      onChange={(e) => handleInputChange("specialization", e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="experience">Experience (Years)</Label>
                    <Input
                      id="experience"
                      type="number"
                      value={formData.experience}
                      onChange={(e) => handleInputChange("experience", e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="salary">Salary</Label>
                    <Input
                      id="salary"
                      type="number"
                      step="0.01"
                      value={formData.salary}
                      onChange={(e) => handleInputChange("salary", e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="contractType">Contract Type</Label>
                    <Select 
                      value={formData.contractType}
                      onValueChange={(value) => handleInputChange("contractType", value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="FULL_TIME">Full Time</SelectItem>
                        <SelectItem value="PART_TIME">Part Time</SelectItem>
                        <SelectItem value="CONTRACT">Contract</SelectItem>
                        <SelectItem value="TEMPORARY">Temporary</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="hireDate">Hire Date</Label>
                    <Input
                      id="hireDate"
                      type="date"
                      value={formData.hireDate}
                      onChange={(e) => handleInputChange("hireDate", e.target.value)}
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Create Teacher</Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>

        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Teacher Records</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search by name, email or employee number..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              {/* <Select onValueChange={setDepartmentFilter} value={departmentFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  {departments.length > 0 ? (
                    departments.map((department) => (
                      <SelectItem key={department.id} value={department.name}>
                        {department.name}
                      </SelectItem>
                    ))
                  ) : (
                    // Fallback options based on common specializations
                    <>
                      
                    </>
                  )}
                </SelectContent>
              </Select> */}

              <Select onValueChange={setStatusFilter} value={statusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="ON_LEAVE">On Leave</SelectItem>
                  <SelectItem value="INACTIVE">Inactive</SelectItem>
                </SelectContent>
              </Select>

              {(departmentFilter !== "all" || statusFilter !== "all") && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    setDepartmentFilter("all")
                    setStatusFilter("all")
                  }}
                  title="Clear filters"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <p>Loading teachers...</p>
              </div>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employee #</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                     
                      <TableHead>Qualification</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTeachers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center h-24">
                          No teachers found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredTeachers.map((teacher) => (
                        <TableRow key={teacher.id}>
                          <TableCell>{teacher.employeeId || "N/A"}</TableCell>
                          <TableCell>
                            <Link
                              href={`/dashboard/teachers/${teacher.id}`}
                              className="font-medium text-emerald-600 hover:underline"
                            >
                              {teacher.user.firstName} {teacher.user.lastName}
                            </Link>
                          </TableCell>
                          <TableCell>{teacher.user.email}</TableCell>

                          <TableCell>{teacher.qualification || "N/A"}</TableCell>
                          <TableCell>{teacher.user.phoneNumber || "N/A"}</TableCell>
                          <TableCell>
                            <Badge variant={getStatusBadgeVariant(teacher.user.isActive)}>
                              {teacher.user.isActive ? "Active" : "Inactive"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Actions</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Link href={`/dashboard/teachers/${teacher.id}`} className="flex w-full">
                                    View Details
                                  </Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem>Edit</DropdownMenuItem>
                               
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between space-x-2 py-4">
                  <div className="text-sm text-gray-500">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Department Assignment Dialog */}
      <Dialog open={isDepartmentDialogOpen} onOpenChange={setIsDepartmentDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              Assign Department to {selectedTeacher?.user.firstName} {selectedTeacher?.user.lastName}
            </DialogTitle>
            <DialogDescription>
              Select a department to assign to this teacher. This will help organize teachers by their specialization areas.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {selectedTeacher?.department && (
              <div className="p-4 bg-muted rounded-lg">
                <p className="text-sm font-medium">Currently Assigned:</p>
                <p className="text-sm text-muted-foreground">{selectedTeacher.department.name}</p>
              </div>
            )}
            <div className="space-y-2">
              <Label>Select Department</Label>
              <div className="grid gap-2">
                {departments.map((department) => (
                  <Button
                    key={department.id}
                    variant={selectedTeacher?.department?.id === department.id ? "default" : "outline"}
                    className="justify-start"
                    onClick={() => handleDepartmentAssignment(department.id)}
                  >
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        selectedTeacher?.department?.id === department.id ? 'bg-white' : 'bg-green-500'
                      }`}></div>
                      <span>{department.name}</span>
                    </div>
                  </Button>
                ))}
                {departments.length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No departments available. Please create departments first.
                  </p>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsDepartmentDialogOpen(false)
                setSelectedTeacher(null)
              }}
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
