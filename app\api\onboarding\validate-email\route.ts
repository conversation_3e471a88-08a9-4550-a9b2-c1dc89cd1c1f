import { NextResponse } from "next/server"

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Forward the request to our Flask backend
    const response = await fetch("http://localhost:4000/api/v1/onboarding/validate-email", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: data.error || "Validation failed" }, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Email validation API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
