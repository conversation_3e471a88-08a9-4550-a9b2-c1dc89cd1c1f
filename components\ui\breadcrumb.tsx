import * as React from "react"
import Link from "next/link"
import { ChevronRight, Home } from "lucide-react"
import { cn } from "@/lib/utils"

interface BreadcrumbProps extends React.HTMLAttributes<HTMLDivElement> {
  segments: {
    title: string
    href: string
  }[]
  homeHref?: string
  separator?: React.ReactNode
}

export function Breadcrumb({
  segments,
  homeHref = "/dashboard",
  separator = <ChevronRight className="h-4 w-4 text-muted-foreground" />,
  className,
  ...props
}: BreadcrumbProps) {
  return (
    <nav
      className={cn("flex items-center text-sm text-muted-foreground", className)}
      aria-label="Breadcrumb"
      {...props}
    >
      <ol className="flex items-center space-x-2">
        <li>
          <Link href={homeHref} className="flex items-center hover:text-foreground transition-colors" aria-label="Home">
            <Home className="h-4 w-4" />
          </Link>
        </li>
        {segments.map((segment, index) => (
          <React.Fragment key={segment.href}>
            <li className="flex items-center space-x-2">
              {separator}
              <Link
                href={segment.href}
                className={cn(
                  "hover:text-foreground transition-colors",
                  index === segments.length - 1 && "text-foreground font-medium",
                )}
              >
                {segment.title}
              </Link>
            </li>
          </React.Fragment>
        ))}
      </ol>
    </nav>
  )
}
