const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

// Get all fees for a specific student
const getStudentFees = async (req, res) => {
  try {
    const { studentId } = req.params
    const { status, academicYear } = req.query

    let whereClause = { studentId: parseInt(studentId) }

    if (status) {
      whereClause.status = status
    }

    if (academicYear) {
      whereClause.academicYear = academicYear
    }

    const fees = await prisma.fee.findMany({
      where: whereClause,
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            rollNumber: true,
            class: {
              select: {
                name: true,
                grade: true
              }
            }
          }
        },
        payments: {
          orderBy: { createdAt: 'desc' }
        }
      },
      orderBy: { dueDate: 'asc' }
    })

    res.status(200).json({
      success: true,
      message: 'Student fees retrieved successfully',
      data: fees
    })
  } catch (error) {
    console.error('Get student fees error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve student fees',
      error: error.message
    })
  }
}

// Get all fees for a specific class
const getClassFees = async (req, res) => {
  try {
    const { classId } = req.params
    const { status, feeType, academicYear } = req.query

    let whereClause = {
      student: {
        classId: parseInt(classId)
      }
    }

    if (status) {
      whereClause.status = status
    }

    if (feeType) {
      whereClause.type = feeType
    }

    if (academicYear) {
      whereClause.academicYear = academicYear
    }

    const fees = await prisma.fee.findMany({
      where: whereClause,
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            rollNumber: true
          }
        },
        payments: true
      },
      orderBy: [
        { dueDate: 'asc' },
        { student: { rollNumber: 'asc' } }
      ]
    })

    res.status(200).json({
      success: true,
      message: 'Class fees retrieved successfully',
      data: fees
    })
  } catch (error) {
    console.error('Get class fees error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve class fees',
      error: error.message
    })
  }
}

// Create a new fee record
const createFee = async (req, res) => {
  try {
    const {
      studentId,
      type,
      amount,
      dueDate,
      description,
      academicYear,
      term
    } = req.body

    // Validate required fields
    if (!studentId || !type || !amount || !dueDate) {
      return res.status(400).json({
        success: false,
        message: 'Student ID, type, amount, and due date are required'
      })
    }

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: parseInt(studentId) }
    })

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      })
    }

    // Create fee record
    const fee = await prisma.fee.create({
      data: {
        studentId: parseInt(studentId),
        type,
        amount: parseFloat(amount),
        dueDate: new Date(dueDate),
        description: description || null,
        academicYear: academicYear || new Date().getFullYear().toString(),
        term: term || null,
        status: 'PENDING'
      },
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            rollNumber: true
          }
        }
      }
    })

    res.status(201).json({
      success: true,
      message: 'Fee record created successfully',
      data: fee
    })
  } catch (error) {
    console.error('Create fee error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create fee record',
      error: error.message
    })
  }
}

// Update fee record
const updateFee = async (req, res) => {
  try {
    const { id } = req.params
    const {
      type,
      amount,
      dueDate,
      description,
      status,
      academicYear,
      term
    } = req.body

    // Check if fee exists
    const existingFee = await prisma.fee.findUnique({
      where: { id: parseInt(id) }
    })

    if (!existingFee) {
      return res.status(404).json({
        success: false,
        message: 'Fee record not found'
      })
    }

    // Prepare update data
    const updateData = {}
    if (type) updateData.type = type
    if (amount) updateData.amount = parseFloat(amount)
    if (dueDate) updateData.dueDate = new Date(dueDate)
    if (description !== undefined) updateData.description = description
    if (status) updateData.status = status
    if (academicYear) updateData.academicYear = academicYear
    if (term !== undefined) updateData.term = term

    // Update fee record
    const updatedFee = await prisma.fee.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            rollNumber: true
          }
        },
        payments: true
      }
    })

    res.status(200).json({
      success: true,
      message: 'Fee record updated successfully',
      data: updatedFee
    })
  } catch (error) {
    console.error('Update fee error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update fee record',
      error: error.message
    })
  }
}

// Record a payment for a fee
const recordPayment = async (req, res) => {
  try {
    const { feeId } = req.params
    const {
      amount,
      paymentMethod,
      transactionId,
      paymentDate,
      remarks
    } = req.body

    // Validate required fields
    if (!amount || !paymentMethod) {
      return res.status(400).json({
        success: false,
        message: 'Amount and payment method are required'
      })
    }

    // Check if fee exists
    const fee = await prisma.fee.findUnique({
      where: { id: parseInt(feeId) },
      include: {
        payments: true
      }
    })

    if (!fee) {
      return res.status(404).json({
        success: false,
        message: 'Fee record not found'
      })
    }

    // Calculate total paid amount
    const totalPaid = fee.payments.reduce((sum, payment) => sum + payment.amount, 0)
    const newTotalPaid = totalPaid + parseFloat(amount)

    // Check if payment amount is valid
    if (newTotalPaid > fee.amount) {
      return res.status(400).json({
        success: false,
        message: 'Payment amount exceeds the fee amount'
      })
    }

    // Create payment record
    const payment = await prisma.payment.create({
      data: {
        feeId: parseInt(feeId),
        amount: parseFloat(amount),
        paymentMethod,
        transactionId: transactionId || null,
        paymentDate: paymentDate ? new Date(paymentDate) : new Date(),
        remarks: remarks || null
      }
    })

    // Update fee status based on payment
    let newStatus = 'PARTIAL'
    if (newTotalPaid >= fee.amount) {
      newStatus = 'PAID'
    }

    await prisma.fee.update({
      where: { id: parseInt(feeId) },
      data: { status: newStatus }
    })

    res.status(201).json({
      success: true,
      message: 'Payment recorded successfully',
      data: {
        payment,
        feeStatus: newStatus,
        totalPaid: newTotalPaid,
        remainingAmount: fee.amount - newTotalPaid
      }
    })
  } catch (error) {
    console.error('Record payment error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to record payment',
      error: error.message
    })
  }
}

// Get fee statistics
const getFeeStats = async (req, res) => {
  try {
    const { classId, academicYear } = req.query

    let whereClause = {}

    if (classId) {
      whereClause.student = {
        classId: parseInt(classId)
      }
    }

    if (academicYear) {
      whereClause.academicYear = academicYear
    }

    // Get total fees by status
    const feesByStatus = await prisma.fee.groupBy({
      by: ['status'],
      where: whereClause,
      _count: {
        status: true
      },
      _sum: {
        amount: true
      }
    })

    // Get overdue fees
    const overdueFees = await prisma.fee.count({
      where: {
        ...whereClause,
        status: {
          in: ['PENDING', 'PARTIAL']
        },
        dueDate: {
          lt: new Date()
        }
      }
    })

    // Get fees by type
    const feesByType = await prisma.fee.groupBy({
      by: ['type'],
      where: whereClause,
      _count: {
        type: true
      },
      _sum: {
        amount: true
      }
    })

    // Calculate collection rate
    const totalFees = await prisma.fee.aggregate({
      where: whereClause,
      _sum: {
        amount: true
      },
      _count: true
    })

    const paidFees = await prisma.fee.aggregate({
      where: {
        ...whereClause,
        status: 'PAID'
      },
      _sum: {
        amount: true
      }
    })

    const collectionRate = totalFees._sum.amount > 0 
      ? ((paidFees._sum.amount || 0) / totalFees._sum.amount * 100).toFixed(2)
      : 0

    res.status(200).json({
      success: true,
      message: 'Fee statistics retrieved successfully',
      data: {
        feesByStatus,
        feesByType,
        overdueFees,
        totalFees: totalFees._count,
        totalAmount: totalFees._sum.amount || 0,
        paidAmount: paidFees._sum.amount || 0,
        collectionRate: parseFloat(collectionRate)
      }
    })
  } catch (error) {
    console.error('Get fee stats error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve fee statistics',
      error: error.message
    })
  }
}

// Delete fee record
const deleteFee = async (req, res) => {
  try {
    const { id } = req.params

    // Check if fee exists
    const existingFee = await prisma.fee.findUnique({
      where: { id: parseInt(id) },
      include: {
        payments: true
      }
    })

    if (!existingFee) {
      return res.status(404).json({
        success: false,
        message: 'Fee record not found'
      })
    }

    // Check if fee has payments
    if (existingFee.payments.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete fee record with existing payments'
      })
    }

    // Delete fee record
    await prisma.fee.delete({
      where: { id: parseInt(id) }
    })

    res.status(200).json({
      success: true,
      message: 'Fee record deleted successfully'
    })
  } catch (error) {
    console.error('Delete fee error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to delete fee record',
      error: error.message
    })
  }
}

// Create bulk fees for a class
const createBulkFees = async (req, res) => {
  try {
    const {
      classId,
      type,
      amount,
      dueDate,
      description,
      academicYear,
      term
    } = req.body

    // Validate required fields
    if (!classId || !type || !amount || !dueDate) {
      return res.status(400).json({
        success: false,
        message: 'Class ID, type, amount, and due date are required'
      })
    }

    // Get all students in the class
    const students = await prisma.student.findMany({
      where: { classId: parseInt(classId) },
      select: { id: true }
    })

    if (students.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No students found in the specified class'
      })
    }

    // Prepare fee data for all students
    const feesData = students.map(student => ({
      studentId: student.id,
      type,
      amount: parseFloat(amount),
      dueDate: new Date(dueDate),
      description: description || null,
      academicYear: academicYear || new Date().getFullYear().toString(),
      term: term || null,
      status: 'PENDING'
    }))

    // Create fees for all students
    const createdFees = await prisma.fee.createMany({
      data: feesData
    })

    res.status(201).json({
      success: true,
      message: 'Bulk fees created successfully',
      data: {
        feesCreated: createdFees.count,
        classId: parseInt(classId),
        studentsAffected: students.length
      }
    })
  } catch (error) {
    console.error('Create bulk fees error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create bulk fees',
      error: error.message
    })
  }
}

module.exports = {
  getStudentFees,
  getClassFees,
  createFee,
  updateFee,
  recordPayment,
  getFeeStats,
  deleteFee,
  createBulkFees
}
