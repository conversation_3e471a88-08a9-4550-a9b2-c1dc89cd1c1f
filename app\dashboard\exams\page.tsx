"use client"

import { useState } from "react"
import { format } from "date-fns"
import { CalendarIcon, Plus, <PERSON>cil, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"

// Mock data for exams
const initialExams = [
  {
    id: 1,
    name: "Mid-Term Examination",
    type: "Mid-Term",
    startDate: new Date(2023, 9, 15),
    endDate: new Date(2023, 9, 20),
    classes: ["Class 10A", "Class 9B", "Class 8C"],
    subjects: ["Mathematics", "Science", "English", "History"],
    status: "Completed",
  },
  {
    id: 2,
    name: "Final Examination",
    type: "Final",
    startDate: new Date(2023, 11, 10),
    endDate: new Date(2023, 11, 20),
    classes: ["Class 10A", "Class 9B", "Class 8C", "Class 7D"],
    subjects: ["Mathematics", "Science", "English", "History", "Geography"],
    status: "Scheduled",
  },
  {
    id: 3,
    name: "Unit Test - October",
    type: "Unit Test",
    startDate: new Date(2023, 9, 5),
    endDate: new Date(2023, 9, 7),
    classes: ["Class 10A", "Class 9B"],
    subjects: ["Mathematics", "Science"],
    status: "Completed",
  },
  {
    id: 4,
    name: "Annual Examination",
    type: "Annual",
    startDate: new Date(2024, 2, 15),
    endDate: new Date(2024, 2, 30),
    classes: ["Class 10A", "Class 9B", "Class 8C", "Class 7D"],
    subjects: ["Mathematics", "Science", "English", "History", "Geography", "Computer Science"],
    status: "Upcoming",
  },
]

// Mock data for exam schedule
const examSchedule = [
  {
    id: 1,
    examId: 2,
    subject: "Mathematics",
    date: new Date(2023, 11, 10),
    startTime: "09:00 AM",
    endTime: "12:00 PM",
    venue: "Main Hall",
    classes: ["Class 10A", "Class 9B"],
  },
  {
    id: 2,
    examId: 2,
    subject: "Science",
    date: new Date(2023, 11, 12),
    startTime: "09:00 AM",
    endTime: "12:00 PM",
    venue: "Science Lab",
    classes: ["Class 10A", "Class 9B"],
  },
  {
    id: 3,
    examId: 2,
    subject: "English",
    date: new Date(2023, 11, 14),
    startTime: "09:00 AM",
    endTime: "12:00 PM",
    venue: "Main Hall",
    classes: ["Class 10A", "Class 9B"],
  },
  {
    id: 4,
    examId: 2,
    subject: "History",
    date: new Date(2023, 11, 16),
    startTime: "09:00 AM",
    endTime: "12:00 PM",
    venue: "Room 101",
    classes: ["Class 10A", "Class 9B"],
  },
  {
    id: 5,
    examId: 2,
    subject: "Geography",
    date: new Date(2023, 11, 18),
    startTime: "09:00 AM",
    endTime: "12:00 PM",
    venue: "Room 102",
    classes: ["Class 10A", "Class 9B"],
  },
]

// Mock data for classes
const classes = [
  { id: 1, name: "Class 10A" },
  { id: 2, name: "Class 9B" },
  { id: 3, name: "Class 8C" },
  { id: 4, name: "Class 7D" },
]

// Mock data for subjects
const subjects = [
  { id: 1, name: "Mathematics" },
  { id: 2, name: "Science" },
  { id: 3, name: "English" },
  { id: 4, name: "History" },
  { id: 5, name: "Geography" },
  { id: 6, name: "Computer Science" },
]

export default function ExamsPage() {
  const [exams, setExams] = useState(initialExams)
  const [isAddExamOpen, setIsAddExamOpen] = useState(false)
  const [isAddScheduleOpen, setIsAddScheduleOpen] = useState(false)
  const [selectedExam, setSelectedExam] = useState<number | null>(null)
  const [newExam, setNewExam] = useState({
    name: "",
    type: "",
    startDate: new Date(),
    endDate: new Date(),
    classes: [] as string[],
    subjects: [] as string[],
  })
  const [newSchedule, setNewSchedule] = useState({
    examId: 0,
    subject: "",
    date: new Date(),
    startTime: "",
    endTime: "",
    venue: "",
    classes: [] as string[],
  })

  const handleAddExam = () => {
    const newExamWithId = {
      ...newExam,
      id: exams.length + 1,
      status: "Upcoming",
    }
    setExams([...exams, newExamWithId])
    setIsAddExamOpen(false)
    setNewExam({
      name: "",
      type: "",
      startDate: new Date(),
      endDate: new Date(),
      classes: [],
      subjects: [],
    })
  }

  const handleDeleteExam = (id: number) => {
    setExams(exams.filter((exam) => exam.id !== id))
  }

  const filteredSchedule = selectedExam
    ? examSchedule.filter((schedule) => schedule.examId === selectedExam)
    : examSchedule

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Exams Management</h2>
          <p className="text-muted-foreground">Create and manage examinations and schedules</p>
        </div>
        <Dialog open={isAddExamOpen} onOpenChange={setIsAddExamOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add New Exam
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Add New Examination</DialogTitle>
              <DialogDescription>Enter the details for the new examination.</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="exam-name" className="text-right">
                  Exam Name
                </Label>
                <Input
                  id="exam-name"
                  value={newExam.name}
                  onChange={(e) => setNewExam({ ...newExam, name: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="exam-type" className="text-right">
                  Exam Type
                </Label>
                <Select value={newExam.type} onValueChange={(value) => setNewExam({ ...newExam, type: value })}>
                  <SelectTrigger id="exam-type" className="col-span-3">
                    <SelectValue placeholder="Select exam type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Mid-Term">Mid-Term</SelectItem>
                    <SelectItem value="Final">Final</SelectItem>
                    <SelectItem value="Unit Test">Unit Test</SelectItem>
                    <SelectItem value="Annual">Annual</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "col-span-3 justify-start text-left font-normal",
                        !newExam.startDate && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {newExam.startDate ? format(newExam.startDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={newExam.startDate}
                      onSelect={(date) => date && setNewExam({ ...newExam, startDate: date })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "col-span-3 justify-start text-left font-normal",
                        !newExam.endDate && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {newExam.endDate ? format(newExam.endDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={newExam.endDate}
                      onSelect={(date) => date && setNewExam({ ...newExam, endDate: date })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="exam-classes" className="text-right">
                  Classes
                </Label>
                <Select
                  value={newExam.classes.join(",")}
                  onValueChange={(value) => setNewExam({ ...newExam, classes: value ? value.split(",") : [] })}
                >
                  <SelectTrigger id="exam-classes" className="col-span-3">
                    <SelectValue placeholder="Select classes" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Class 10A,Class 9B,Class 8C,Class 7D">All Classes</SelectItem>
                    <SelectItem value="Class 10A,Class 9B">Senior Classes</SelectItem>
                    <SelectItem value="Class 8C,Class 7D">Junior Classes</SelectItem>
                    <SelectItem value="Class 10A">Class 10A</SelectItem>
                    <SelectItem value="Class 9B">Class 9B</SelectItem>
                    <SelectItem value="Class 8C">Class 8C</SelectItem>
                    <SelectItem value="Class 7D">Class 7D</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="exam-subjects" className="text-right">
                  Subjects
                </Label>
                <Select
                  value={newExam.subjects.join(",")}
                  onValueChange={(value) => setNewExam({ ...newExam, subjects: value ? value.split(",") : [] })}
                >
                  <SelectTrigger id="exam-subjects" className="col-span-3">
                    <SelectValue placeholder="Select subjects" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Mathematics,Science,English,History,Geography,Computer Science">
                      All Subjects
                    </SelectItem>
                    <SelectItem value="Mathematics,Science">STEM Subjects</SelectItem>
                    <SelectItem value="English,History,Geography">Humanities</SelectItem>
                    <SelectItem value="Mathematics">Mathematics</SelectItem>
                    <SelectItem value="Science">Science</SelectItem>
                    <SelectItem value="English">English</SelectItem>
                    <SelectItem value="History">History</SelectItem>
                    <SelectItem value="Geography">Geography</SelectItem>
                    <SelectItem value="Computer Science">Computer Science</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddExamOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddExam}>Save Exam</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="exams">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="exams">Exams</TabsTrigger>
          <TabsTrigger value="schedule">Exam Schedule</TabsTrigger>
        </TabsList>
        <TabsContent value="exams" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Examinations</CardTitle>
              <CardDescription>List of all examinations in the current academic year</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Exam Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>End Date</TableHead>
                    <TableHead>Classes</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {exams.map((exam) => (
                    <TableRow key={exam.id}>
                      <TableCell className="font-medium">{exam.name}</TableCell>
                      <TableCell>{exam.type}</TableCell>
                      <TableCell>{format(exam.startDate, "MMM dd, yyyy")}</TableCell>
                      <TableCell>{format(exam.endDate, "MMM dd, yyyy")}</TableCell>
                      <TableCell>{exam.classes.length} Classes</TableCell>
                      <TableCell>
                        <span
                          className={cn(
                            "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
                            exam.status === "Completed" && "bg-green-100 text-green-800",
                            exam.status === "Scheduled" && "bg-blue-100 text-blue-800",
                            exam.status === "Upcoming" && "bg-yellow-100 text-yellow-800",
                          )}
                        >
                          {exam.status}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button variant="outline" size="sm">
                            <Pencil className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleDeleteExam(exam.id)}>
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Exam Schedule</CardTitle>
                <CardDescription>View and manage examination schedules</CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Select
                  value={selectedExam ? selectedExam.toString() : ""}
                  onValueChange={(value) => setSelectedExam(value ? Number.parseInt(value) : null)}
                >
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Filter by exam" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Exams</SelectItem>
                    {exams.map((exam) => (
                      <SelectItem key={exam.id} value={exam.id.toString()}>
                        {exam.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Dialog open={isAddScheduleOpen} onOpenChange={setIsAddScheduleOpen}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Add Schedule
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                      <DialogTitle>Add Exam Schedule</DialogTitle>
                      <DialogDescription>Create a new exam schedule entry.</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="schedule-exam" className="text-right">
                          Exam
                        </Label>
                        <Select
                          value={newSchedule.examId.toString()}
                          onValueChange={(value) => setNewSchedule({ ...newSchedule, examId: Number.parseInt(value) })}
                        >
                          <SelectTrigger id="schedule-exam" className="col-span-3">
                            <SelectValue placeholder="Select exam" />
                          </SelectTrigger>
                          <SelectContent>
                            {exams.map((exam) => (
                              <SelectItem key={exam.id} value={exam.id.toString()}>
                                {exam.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="schedule-subject" className="text-right">
                          Subject
                        </Label>
                        <Select
                          value={newSchedule.subject}
                          onValueChange={(value) => setNewSchedule({ ...newSchedule, subject: value })}
                        >
                          <SelectTrigger id="schedule-subject" className="col-span-3">
                            <SelectValue placeholder="Select subject" />
                          </SelectTrigger>
                          <SelectContent>
                            {subjects.map((subject) => (
                              <SelectItem key={subject.id} value={subject.name}>
                                {subject.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label className="text-right">Date</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "col-span-3 justify-start text-left font-normal",
                                !newSchedule.date && "text-muted-foreground",
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {newSchedule.date ? format(newSchedule.date, "PPP") : <span>Pick a date</span>}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={newSchedule.date}
                              onSelect={(date) => date && setNewSchedule({ ...newSchedule, date })}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="start-time" className="text-right">
                          Start Time
                        </Label>
                        <Input
                          id="start-time"
                          type="time"
                          value={newSchedule.startTime}
                          onChange={(e) => setNewSchedule({ ...newSchedule, startTime: e.target.value })}
                          className="col-span-3"
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="end-time" className="text-right">
                          End Time
                        </Label>
                        <Input
                          id="end-time"
                          type="time"
                          value={newSchedule.endTime}
                          onChange={(e) => setNewSchedule({ ...newSchedule, endTime: e.target.value })}
                          className="col-span-3"
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="venue" className="text-right">
                          Venue
                        </Label>
                        <Input
                          id="venue"
                          value={newSchedule.venue}
                          onChange={(e) => setNewSchedule({ ...newSchedule, venue: e.target.value })}
                          className="col-span-3"
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="schedule-classes" className="text-right">
                          Classes
                        </Label>
                        <Select
                          value={newSchedule.classes.join(",")}
                          onValueChange={(value) =>
                            setNewSchedule({ ...newSchedule, classes: value ? value.split(",") : [] })
                          }
                        >
                          <SelectTrigger id="schedule-classes" className="col-span-3">
                            <SelectValue placeholder="Select classes" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Class 10A,Class 9B,Class 8C,Class 7D">All Classes</SelectItem>
                            <SelectItem value="Class 10A,Class 9B">Senior Classes</SelectItem>
                            <SelectItem value="Class 8C,Class 7D">Junior Classes</SelectItem>
                            <SelectItem value="Class 10A">Class 10A</SelectItem>
                            <SelectItem value="Class 9B">Class 9B</SelectItem>
                            <SelectItem value="Class 8C">Class 8C</SelectItem>
                            <SelectItem value="Class 7D">Class 7D</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsAddScheduleOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={() => setIsAddScheduleOpen(false)}>Save Schedule</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Subject</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Venue</TableHead>
                    <TableHead>Classes</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSchedule.map((schedule) => (
                    <TableRow key={schedule.id}>
                      <TableCell className="font-medium">{schedule.subject}</TableCell>
                      <TableCell>{format(schedule.date, "MMM dd, yyyy")}</TableCell>
                      <TableCell>{`${schedule.startTime} - ${schedule.endTime}`}</TableCell>
                      <TableCell>{schedule.venue}</TableCell>
                      <TableCell>{schedule.classes.join(", ")}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button variant="outline" size="sm">
                            <Pencil className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
