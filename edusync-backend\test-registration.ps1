Write-Host "Testing registration endpoint..."

$body = @{
    institutionName = "Test School"
    institutionType = "educational"
    address = "123 Test Street"
    city = "Test City"
    state = "Test State"
    country = "Zambia"
    postalCode = "12345"
    phone = "+260123456789"
    institutionEmail = "<EMAIL>"
    website = "https://testschool.edu.zm"
    schoolName = "Test Primary School"
    schoolType = "PRIMARY"
    schoolEmail = "<EMAIL>"
    adminFirstName = "Test"
    adminLastName = "Admin"
    adminEmail = "<EMAIL>"
    adminPhone = "+260987654321"
    adminPassword = "TestPassword123!"
    studentCount = "100"
    teacherCount = "10"
    subscriptionPlan = "basic"
    referralSource = "test"
    specialRequirements = "None"
}

try {
    $response = Invoke-RestMethod -Uri "http://localhost:4000/api/v1/institutions/register" -Method Post -Body $body
    Write-Host "✅ Registration successful!" -ForegroundColor Green
    Write-Host "Institution ID: $($response.data.institutionId)"
    Write-Host "School ID: $($response.data.school.id)"
    Write-Host "Verification token: $($response.data.verificationToken)"
} catch {
    Write-Host "❌ Registration failed:" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody"
    }
}
