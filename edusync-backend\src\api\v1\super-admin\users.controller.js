const { PrismaClient } = require('@prisma/client');
const asyncHandler = require('../../../utils/asyncHandler');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

/**
 * Get all users with pagination and filtering
 */
const getAllUsers = asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    search, 
    role, 
    status, 
    institutionId 
  } = req.query;

  const skip = (page - 1) * limit;
  const where = {};

  // Add search filter
  if (search) {
    where.OR = [
      { firstName: { contains: search, mode: 'insensitive' } },
      { lastName: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } }
    ];
  }

  // Add role filter
  if (role) {
    where.role = role;
  }

  // Add status filter
  if (status) {
    where.isActive = status === 'active';
  }

  // Add institution filter
  if (institutionId) {
    where.institutionId = institutionId;
  }

  const [users, totalUsers] = await Promise.all([
    prisma.user.findMany({
      where,
      skip: parseInt(skip),
      take: parseInt(limit),
      include: {
        institution: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.user.count({ where })
  ]);

  res.status(200).json({
    success: true,
    data: {
      users: users.map(user => ({
        id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        role: user.role,
        institution: user.institution,
        status: user.isActive ? 'Active' : user.emailVerified ? 'Inactive' : 'Pending',
        lastLogin: user.lastLoginAt || 'Never',
        createdAt: user.createdAt
      })),
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalUsers / limit),
        totalUsers,
        hasNextPage: skip + users.length < totalUsers,
        hasPrevPage: page > 1
      }
    }
  });
});

/**
 * Get user by ID
 */
const getUserById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await prisma.user.findUnique({
    where: { id },
    include: {
      institution: true,
      auditLogs: {
        take: 10,
        orderBy: { createdAt: 'desc' }
      }
    }
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  res.status(200).json({
    success: true,
    data: {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
      emailVerified: user.emailVerified,
      institution: user.institution,
      createdAt: user.createdAt,
      lastLoginAt: user.lastLoginAt,
      recentActivity: user.auditLogs
    }
  });
});

/**
 * Create new user
 */
const createUser = asyncHandler(async (req, res) => {
  const { 
    firstName, 
    lastName, 
    email, 
    password, 
    role, 
    institutionId 
  } = req.body;

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email }
  });

  if (existingUser) {
    return res.status(400).json({
      success: false,
      error: 'User with this email already exists'
    });
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(password, 12);

  // Create user
  const user = await prisma.user.create({
    data: {
      firstName,
      lastName,
      email,
      password: hashedPassword,
      role,
      institutionId: institutionId || null,
      isActive: true,
      emailVerified: true // Super admin created users are pre-verified
    },
    include: {
      institution: {
        select: {
          id: true,
          name: true
        }
      }
    }
  });

  // Log the creation
  await prisma.auditLog.create({
    data: {
      userId: req.user.id,
      action: 'USER_CREATED',
      details: `New user created: ${email} with role ${role}`,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    }
  });

  res.status(201).json({
    success: true,
    message: 'User created successfully',
    data: {
      id: user.id,
      name: `${user.firstName} ${user.lastName}`,
      email: user.email,
      role: user.role,
      institution: user.institution
    }
  });
});

/**
 * Update user
 */
const updateUser = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { 
    firstName, 
    lastName, 
    email, 
    role, 
    institutionId, 
    isActive 
  } = req.body;

  const user = await prisma.user.findUnique({
    where: { id }
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Check if email is being changed and if it already exists
  if (email && email !== user.email) {
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'Email already exists'
      });
    }
  }

  const updatedUser = await prisma.user.update({
    where: { id },
    data: {
      firstName: firstName || user.firstName,
      lastName: lastName || user.lastName,
      email: email || user.email,
      role: role || user.role,
      institutionId: institutionId !== undefined ? institutionId : user.institutionId,
      isActive: isActive !== undefined ? isActive : user.isActive
    },
    include: {
      institution: {
        select: {
          id: true,
          name: true
        }
      }
    }
  });

  // Log the update
  await prisma.auditLog.create({
    data: {
      userId: req.user.id,
      action: 'USER_UPDATED',
      details: `User updated: ${updatedUser.email}`,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    }
  });

  res.status(200).json({
    success: true,
    message: 'User updated successfully',
    data: {
      id: updatedUser.id,
      name: `${updatedUser.firstName} ${updatedUser.lastName}`,
      email: updatedUser.email,
      role: updatedUser.role,
      institution: updatedUser.institution,
      isActive: updatedUser.isActive
    }
  });
});

/**
 * Delete user
 */
const deleteUser = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await prisma.user.findUnique({
    where: { id }
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Prevent deletion of super admin users
  if (user.role === 'SUPER_ADMIN') {
    return res.status(403).json({
      success: false,
      error: 'Cannot delete super admin users'
    });
  }

  await prisma.user.delete({
    where: { id }
  });

  // Log the deletion
  await prisma.auditLog.create({
    data: {
      userId: req.user.id,
      action: 'USER_DELETED',
      details: `User deleted: ${user.email}`,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    }
  });

  res.status(200).json({
    success: true,
    message: 'User deleted successfully'
  });
});

/**
 * Reset user password
 */
const resetUserPassword = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { newPassword } = req.body;

  const user = await prisma.user.findUnique({
    where: { id }
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  const hashedPassword = await bcrypt.hash(newPassword, 12);

  await prisma.user.update({
    where: { id },
    data: { password: hashedPassword }
  });

  // Log the password reset
  await prisma.auditLog.create({
    data: {
      userId: req.user.id,
      action: 'PASSWORD_RESET',
      details: `Password reset for user: ${user.email}`,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    }
  });

  res.status(200).json({
    success: true,
    message: 'Password reset successfully'
  });
});

/**
 * Get user statistics
 */
const getUserStats = asyncHandler(async (req, res) => {
  const totalUsers = await prisma.user.count();
  const activeUsers = await prisma.user.count({
    where: { isActive: true }
  });
  const pendingUsers = await prisma.user.count({
    where: { emailVerified: false }
  });

  const usersByRole = await prisma.user.groupBy({
    by: ['role'],
    _count: {
      id: true
    }
  });

  const recentUsers = await prisma.user.findMany({
    take: 5,
    orderBy: { createdAt: 'desc' },
    include: {
      institution: {
        select: { name: true }
      }
    }
  });

  res.status(200).json({
    success: true,
    data: {
      stats: {
        total: totalUsers,
        active: activeUsers,
        pending: pendingUsers
      },
      roleDistribution: usersByRole.reduce((acc, item) => {
        acc[item.role] = item._count.id;
        return acc;
      }, {}),
      recentUsers: recentUsers.map(user => ({
        id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        role: user.role,
        institution: user.institution?.name || 'No Institution',
        createdAt: user.createdAt
      }))
    }
  });
});

/**
 * Export users data
 */
const exportUsers = asyncHandler(async (req, res) => {
  const { format = 'csv' } = req.query;

  // This would generate actual export file
  // For now, return a success message
  res.status(200).json({
    success: true,
    message: `Users data exported successfully in ${format} format`,
    downloadUrl: `/exports/users-${Date.now()}.${format}`
  });
});

module.exports = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword,
  getUserStats,
  exportUsers
};
