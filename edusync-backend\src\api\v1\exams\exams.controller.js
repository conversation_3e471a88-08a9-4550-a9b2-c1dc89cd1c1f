const { PrismaClient } = require('@prisma/client');
const { logger } = require('../../../utils/logger');

const prisma = new PrismaClient();

/**
 * Get all exams with filtering and pagination
 */
const getExams = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      type,
      status = 'SCHEDULED',
      schoolId,
      academicYearId,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Build where clause
    let where = {};

    if (schoolId) {
      where.schoolId = schoolId;
    }

    if (academicYearId) {
      where.academicYearId = academicYearId;
    }

    if (status && status !== 'ALL') {
      where.status = status;
    }

    if (type) {
      where.type = type;
    }

    // Add search functionality
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { type: { contains: search, mode: 'insensitive' } }
      ];
    }

    // Get exams with relations
    const [exams, totalCount] = await Promise.all([
      prisma.exam.findMany({
        where,
        skip,
        take,
        orderBy: {
          [sortBy]: sortOrder
        },
        include: {
          school: {
            select: {
              id: true,
              name: true
            }
          },
          academicYear: {
            select: {
              id: true,
              name: true,
              year: true
            }
          },
          academicTerm: {
            select: {
              id: true,
              name: true
            }
          },
          classes: {
            include: {
              class: {
                select: {
                  id: true,
                  name: true,
                  gradeLevel: true,
                  section: true
                }
              }
            }
          },
          subjects: {
            include: {
              subject: {
                select: {
                  id: true,
                  name: true,
                  code: true
                }
              }
            }
          },
          schedules: {
            include: {
              subject: {
                select: {
                  id: true,
                  name: true,
                  code: true
                }
              }
            },
            orderBy: [
              { date: 'asc' },
              { startTime: 'asc' }
            ]
          },
          _count: {
            select: {
              results: true,
              classes: true,
              subjects: true
            }
          }
        }
      }),
      prisma.exam.count({ where })
    ]);

    // Format response data
    const formattedExams = exams.map(exam => ({
      id: exam.id,
      name: exam.name,
      type: exam.type,
      description: exam.description,
      startDate: exam.startDate,
      endDate: exam.endDate,
      totalMarks: exam.totalMarks,
      passingMarks: exam.passingMarks,
      status: exam.status,
      instructions: exam.instructions,
      school: exam.school,
      academicYear: exam.academicYear,
      academicTerm: exam.academicTerm,
      classes: exam.classes.map(ec => ec.class),
      subjects: exam.subjects.map(es => ({
        ...es.subject,
        maxMarks: es.maxMarks,
        duration: es.duration
      })),
      schedules: exam.schedules,
      stats: {
        totalResults: exam._count.results,
        totalClasses: exam._count.classes,
        totalSubjects: exam._count.subjects
      },
      createdAt: exam.createdAt,
      updatedAt: exam.updatedAt
    }));

    const totalPages = Math.ceil(totalCount / take);

    res.json({
      success: true,
      data: formattedExams,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });

  } catch (error) {
    logger.error('Error fetching exams:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch exams',
      error: error.message
    });
  }
};

/**
 * Get exam by ID
 */
const getExamById = async (req, res) => {
  try {
    const { id } = req.params;

    const exam = await prisma.exam.findUnique({
      where: { id },
      include: {
        school: {
          select: {
            id: true,
            name: true
          }
        },
        academicYear: {
          select: {
            id: true,
            name: true,
            year: true
          }
        },
        academicTerm: {
          select: {
            id: true,
            name: true
          }
        },
        classes: {
          include: {
            class: {
              include: {
                studentEnrollments: {
                  where: { isActive: true },
                  include: {
                    student: {
                      select: {
                        id: true,
                        admissionNumber: true,
                        user: {
                          select: {
                            firstName: true,
                            lastName: true
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        subjects: {
          include: {
            subject: true
          }
        },
        schedules: {
          include: {
            subject: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          },
          orderBy: [
            { date: 'asc' },
            { startTime: 'asc' }
          ]
        },
        results: {
          include: {
            student: {
              select: {
                id: true,
                admissionNumber: true,
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            },
            subject: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          }
        }
      }
    });

    if (!exam) {
      return res.status(404).json({
        success: false,
        message: 'Exam not found'
      });
    }

    // Format response
    const formattedExam = {
      id: exam.id,
      name: exam.name,
      type: exam.type,
      description: exam.description,
      startDate: exam.startDate,
      endDate: exam.endDate,
      totalMarks: exam.totalMarks,
      passingMarks: exam.passingMarks,
      status: exam.status,
      instructions: exam.instructions,
      school: exam.school,
      academicYear: exam.academicYear,
      academicTerm: exam.academicTerm,
      classes: exam.classes.map(ec => ({
        ...ec.class,
        students: ec.class.studentEnrollments.map(se => ({
          id: se.student.id,
          admissionNumber: se.student.admissionNumber,
          name: `${se.student.user.firstName} ${se.student.user.lastName}`
        }))
      })),
      subjects: exam.subjects.map(es => ({
        ...es.subject,
        maxMarks: es.maxMarks,
        duration: es.duration
      })),
      schedules: exam.schedules,
      results: exam.results.map(result => ({
        id: result.id,
        marks: result.marks,
        grade: result.grade,
        percentage: result.percentage,
        submittedAt: result.submittedAt,
        student: {
          id: result.student.id,
          admissionNumber: result.student.admissionNumber,
          name: `${result.student.user.firstName} ${result.student.user.lastName}`
        },
        subject: result.subject
      })),
      createdAt: exam.createdAt,
      updatedAt: exam.updatedAt
    };

    res.json({
      success: true,
      data: formattedExam
    });

  } catch (error) {
    logger.error('Error fetching exam by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch exam',
      error: error.message
    });
  }
};

/**
 * Create new exam
 */
const createExam = async (req, res) => {
  try {
    const {
      name,
      type,
      description,
      startDate,
      endDate,
      totalMarks,
      passingMarks,
      instructions,
      schoolId,
      academicYearId,
      academicTermId,
      classes = [],
      subjects = []
    } = req.body;

    // Validate required fields
    if (!name || !type || !startDate || !endDate || !schoolId || !academicYearId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: name, type, startDate, endDate, schoolId, academicYearId'
      });
    }

    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start >= end) {
      return res.status(400).json({
        success: false,
        message: 'End date must be after start date'
      });
    }

    // Check if exam name already exists for the same academic year
    const existingExam = await prisma.exam.findFirst({
      where: {
        name,
        schoolId,
        academicYearId
      }
    });

    if (existingExam) {
      return res.status(400).json({
        success: false,
        message: 'Exam with this name already exists for this academic year'
      });
    }

    // Create exam in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the exam
      const exam = await tx.exam.create({
        data: {
          name,
          type,
          description,
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          totalMarks: totalMarks ? parseInt(totalMarks) : null,
          passingMarks: passingMarks ? parseInt(passingMarks) : null,
          instructions,
          schoolId,
          academicYearId,
          academicTermId: academicTermId || null,
          status: 'SCHEDULED'
        }
      });

      // Add classes to exam
      if (classes.length > 0) {
        await tx.examClass.createMany({
          data: classes.map(classId => ({
            examId: exam.id,
            classId
          }))
        });
      }

      // Add subjects to exam
      if (subjects.length > 0) {
        await tx.examSubject.createMany({
          data: subjects.map(subject => ({
            examId: exam.id,
            subjectId: subject.subjectId,
            maxMarks: subject.maxMarks || totalMarks || 100,
            duration: subject.duration || 180 // default 3 hours
          }))
        });
      }

      return exam;
    });

    // Fetch the complete exam data
    const completeExam = await prisma.exam.findUnique({
      where: { id: result.id },
      include: {
        school: {
          select: {
            id: true,
            name: true
          }
        },
        academicYear: {
          select: {
            id: true,
            name: true,
            year: true
          }
        },
        classes: {
          include: {
            class: {
              select: {
                id: true,
                name: true,
                gradeLevel: true,
                section: true
              }
            }
          }
        },
        subjects: {
          include: {
            subject: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: completeExam,
      message: 'Exam created successfully'
    });

  } catch (error) {
    logger.error('Error creating exam:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create exam',
      error: error.message
    });
  }
};

/**
 * Update exam
 */
const updateExam = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Check if exam exists
    const existingExam = await prisma.exam.findUnique({
      where: { id },
      include: {
        classes: true,
        subjects: true
      }
    });

    if (!existingExam) {
      return res.status(404).json({
        success: false,
        message: 'Exam not found'
      });
    }

    // Check if name is being changed and if new name already exists
    if (updateData.name && updateData.name !== existingExam.name) {
      const duplicateExam = await prisma.exam.findFirst({
        where: {
          name: updateData.name,
          schoolId: existingExam.schoolId,
          academicYearId: existingExam.academicYearId,
          NOT: {
            id: id
          }
        }
      });

      if (duplicateExam) {
        return res.status(400).json({
          success: false,
          message: 'Exam with this name already exists for this academic year'
        });
      }
    }

    // Validate dates if being updated
    if (updateData.startDate || updateData.endDate) {
      const startDate = updateData.startDate ? new Date(updateData.startDate) : existingExam.startDate;
      const endDate = updateData.endDate ? new Date(updateData.endDate) : existingExam.endDate;
      
      if (startDate >= endDate) {
        return res.status(400).json({
          success: false,
          message: 'End date must be after start date'
        });
      }
    }

    // Update exam in a transaction
    const updatedExam = await prisma.$transaction(async (tx) => {
      // Update the exam
      const exam = await tx.exam.update({
        where: { id },
        data: {
          ...(updateData.name && { name: updateData.name }),
          ...(updateData.type && { type: updateData.type }),
          ...(updateData.description !== undefined && { description: updateData.description }),
          ...(updateData.startDate && { startDate: new Date(updateData.startDate) }),
          ...(updateData.endDate && { endDate: new Date(updateData.endDate) }),
          ...(updateData.totalMarks !== undefined && { totalMarks: updateData.totalMarks ? parseInt(updateData.totalMarks) : null }),
          ...(updateData.passingMarks !== undefined && { passingMarks: updateData.passingMarks ? parseInt(updateData.passingMarks) : null }),
          ...(updateData.instructions !== undefined && { instructions: updateData.instructions }),
          ...(updateData.status && { status: updateData.status }),
          ...(updateData.academicTermId !== undefined && { academicTermId: updateData.academicTermId })
        }
      });

      // Update classes if provided
      if (updateData.classes) {
        // Remove existing classes
        await tx.examClass.deleteMany({
          where: { examId: id }
        });

        // Add new classes
        if (updateData.classes.length > 0) {
          await tx.examClass.createMany({
            data: updateData.classes.map(classId => ({
              examId: id,
              classId
            }))
          });
        }
      }

      // Update subjects if provided
      if (updateData.subjects) {
        // Remove existing subjects
        await tx.examSubject.deleteMany({
          where: { examId: id }
        });

        // Add new subjects
        if (updateData.subjects.length > 0) {
          await tx.examSubject.createMany({
            data: updateData.subjects.map(subject => ({
              examId: id,
              subjectId: subject.subjectId,
              maxMarks: subject.maxMarks || updateData.totalMarks || 100,
              duration: subject.duration || 180
            }))
          });
        }
      }

      return exam;
    });

    // Fetch the complete updated exam data
    const completeExam = await prisma.exam.findUnique({
      where: { id },
      include: {
        school: {
          select: {
            id: true,
            name: true
          }
        },
        academicYear: {
          select: {
            id: true,
            name: true,
            year: true
          }
        },
        classes: {
          include: {
            class: {
              select: {
                id: true,
                name: true,
                gradeLevel: true,
                section: true
              }
            }
          }
        },
        subjects: {
          include: {
            subject: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          }
        }
      }
    });

    res.json({
      success: true,
      data: completeExam,
      message: 'Exam updated successfully'
    });

  } catch (error) {
    logger.error('Error updating exam:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update exam',
      error: error.message
    });
  }
};

/**
 * Delete exam
 */
const deleteExam = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if exam exists
    const exam = await prisma.exam.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            results: true,
            schedules: true
          }
        }
      }
    });

    if (!exam) {
      return res.status(404).json({
        success: false,
        message: 'Exam not found'
      });
    }

    // Check if exam has results or schedules
    if (exam._count.results > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete exam with existing results. Please delete results first.'
      });
    }

    // Delete exam (cascade will handle related data)
    await prisma.exam.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Exam deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting exam:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete exam',
      error: error.message
    });
  }
};

/**
 * Get exam schedules
 */
const getExamSchedules = async (req, res) => {
  try {
    const { examId } = req.params;
    const { date, subjectId } = req.query;

    let where = {
      examId
    };

    if (date) {
      where.date = new Date(date);
    }

    if (subjectId) {
      where.subjectId = subjectId;
    }

    const schedules = await prisma.examSchedule.findMany({
      where,
      include: {
        exam: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        }
      },
      orderBy: [
        { date: 'asc' },
        { startTime: 'asc' }
      ]
    });

    res.json({
      success: true,
      data: schedules
    });

  } catch (error) {
    logger.error('Error fetching exam schedules:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch exam schedules',
      error: error.message
    });
  }
};

/**
 * Create exam schedule
 */
const createExamSchedule = async (req, res) => {
  try {
    const { examId } = req.params;
    const {
      subjectId,
      date,
      startTime,
      endTime,
      venue
    } = req.body;

    // Validate required fields
    if (!subjectId || !date || !startTime || !endTime) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: subjectId, date, startTime, endTime'
      });
    }

    // Check if exam exists
    const exam = await prisma.exam.findUnique({
      where: { id: examId }
    });

    if (!exam) {
      return res.status(404).json({
        success: false,
        message: 'Exam not found'
      });
    }

    // Check if schedule already exists for this exam, subject, and date
    const existingSchedule = await prisma.examSchedule.findFirst({
      where: {
        examId,
        subjectId,
        date: new Date(date)
      }
    });

    if (existingSchedule) {
      return res.status(400).json({
        success: false,
        message: 'Schedule already exists for this exam, subject, and date'
      });
    }

    // Create schedule
    const schedule = await prisma.examSchedule.create({
      data: {
        examId,
        subjectId,
        date: new Date(date),
        startTime,
        endTime,
        venue
      },
      include: {
        exam: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: schedule,
      message: 'Exam schedule created successfully'
    });

  } catch (error) {
    logger.error('Error creating exam schedule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create exam schedule',
      error: error.message
    });
  }
};

/**
 * Get exam statistics
 */
const getExamStats = async (req, res) => {
  try {
    const { schoolId, academicYearId } = req.query;

    let where = {};
    if (schoolId) where.schoolId = schoolId;
    if (academicYearId) where.academicYearId = academicYearId;

    const [
      totalExams,
      scheduledExams,
      completedExams,
      ongoingExams,
      upcomingExams
    ] = await Promise.all([
      // Total exams
      prisma.exam.count({ where }),
      
      // Scheduled exams
      prisma.exam.count({
        where: {
          ...where,
          status: 'SCHEDULED'
        }
      }),
      
      // Completed exams
      prisma.exam.count({
        where: {
          ...where,
          status: 'COMPLETED'
        }
      }),
      
      // Ongoing exams
      prisma.exam.count({
        where: {
          ...where,
          status: 'ONGOING'
        }
      }),
      
      // Upcoming exams (next 7 days)
      prisma.exam.count({
        where: {
          ...where,
          startDate: {
            gte: new Date(),
            lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
          }
        }
      })
    ]);

    res.json({
      success: true,
      data: {
        totalExams,
        scheduledExams,
        completedExams,
        ongoingExams,
        upcomingExams
      }
    });

  } catch (error) {
    logger.error('Error fetching exam statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch exam statistics',
      error: error.message
    });
  }
};

module.exports = {
  getExams,
  getExamById,
  createExam,
  updateExam,
  deleteExam,
  getExamSchedules,
  createExamSchedule,
  getExamStats
};
