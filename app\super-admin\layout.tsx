"use client"

import type React from "react"
import { SuperAdminHeader } from "@/components/super-admin/header"
import { SuperAdminSidebar } from "@/components/super-admin/sidebar"
import { ToastContainer } from "@/components/notifications/toast-container"
import { SuperAdminBreadcrumb } from "@/components/super-admin/breadcrumb"
import { useRoleAuth } from "@/hooks/use-role-auth"
import { RoleDebug } from "@/components/auth/role-debug"
import { ProtectedRoute } from "@/components/auth/protected-route"

export default function SuperAdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { isLoading, isAuthorized } = useRoleAuth(["SUPER_ADMIN"])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading Super Admin...</p>
        </div>
      </div>
    )
  }

  if (!isAuthorized) {
    return null
  }

  return (
  
      <div className="flex h-screen bg-gray-100">
        <SuperAdminSidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <SuperAdminHeader />
          <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
            <div className="container mx-auto px-6 py-8">
              <SuperAdminBreadcrumb />
              {children}
            </div>
          </main>
        </div>
        <ToastContainer />
        {process.env.NODE_ENV === "development" && <RoleDebug />}
      </div>
  
  )
}
