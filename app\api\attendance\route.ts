import { NextRequest, NextResponse } from "next/server"
import { authenticateUser, createAuthHeaders } from "@/lib/auth"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request)
    
    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error || 'Unauthorized' },
        { status: auth.status }
      )
    }

    const { searchParams } = new URL(request.url)
    const queryString = searchParams.toString()
    
    const response = await fetch(`${BACKEND_URL}/api/v1/attendance?${queryString}`, {
      method: 'GET',
      headers: createAuthHeaders(request),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || 'Failed to fetch attendance' },
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Attendance API error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request)
    
    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error || 'Unauthorized' },
        { status: auth.status }
      )
    }

    const body = await request.json()

    const response = await fetch(`${BACKEND_URL}/api/v1/attendance`, {
      method: "POST",
      headers: await createAuthHeaders(request),
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Failed to record attendance" }, 
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Record attendance API error:", error)
    return NextResponse.json(
      { success: false, message: "Internal server error" }, 
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const auth = await authenticateUser(request)
    
    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error || 'Unauthorized' },
        { status: auth.status }
      )
    }

    const body = await request.json()

    const response = await fetch(`${BACKEND_URL}/api/v1/attendance`, {
      method: "PUT",
      headers: await createAuthHeaders(request),
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Failed to update attendance" }, 
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Update attendance API error:", error)
    return NextResponse.json(
      { success: false, message: "Internal server error" }, 
      { status: 500 }
    )
  }
}
