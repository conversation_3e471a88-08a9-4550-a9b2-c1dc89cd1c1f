const { PrismaClient } = require('@prisma/client');
const asyncHandler = require('../../../utils/asyncHandler');

const prisma = new PrismaClient();

/**
 * Get all audit logs with filtering
 */
const getAuditLogs = asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 50, 
    category, 
    severity, 
    user, 
    startDate, 
    endDate,
    search 
  } = req.query;

  const skip = (page - 1) * limit;
  const where = {};

  // Apply filters
  if (category && category !== 'all') {
    where.category = category;
  }

  if (severity && severity !== 'all') {
    where.severity = severity;
  }

  if (user) {
    where.OR = [
      { userId: user },
      { userEmail: { contains: user, mode: 'insensitive' } }
    ];
  }

  if (startDate && endDate) {
    where.timestamp = {
      gte: new Date(startDate),
      lte: new Date(endDate)
    };
  }

  if (search) {
    where.OR = [
      { action: { contains: search, mode: 'insensitive' } },
      { details: { contains: search, mode: 'insensitive' } },
      { userEmail: { contains: search, mode: 'insensitive' } }
    ];
  }

  // Get audit logs from database
  const auditLogs = await prisma.auditLog.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    },
    orderBy: {
      timestamp: 'desc'
    },
    skip: parseInt(skip),
    take: parseInt(limit)
  });

  const totalCount = await prisma.auditLog.count({ where });

  res.status(200).json({
    success: true,
    data: {
      logs: auditLogs,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalCount / limit),
        totalItems: totalCount,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

/**
 * Get audit log statistics
 */
const getAuditStats = asyncHandler(async (req, res) => {
  const { period = '30' } = req.query;
  const daysAgo = new Date();
  daysAgo.setDate(daysAgo.getDate() - parseInt(period));

  // Get counts by category
  const categoryStats = await prisma.auditLog.groupBy({
    by: ['category'],
    _count: {
      id: true
    },
    where: {
      timestamp: {
        gte: daysAgo
      }
    }
  });

  // Get counts by severity
  const severityStats = await prisma.auditLog.groupBy({
    by: ['severity'],
    _count: {
      id: true
    },
    where: {
      timestamp: {
        gte: daysAgo
      }
    }
  });

  // Get daily activity for the period
  const dailyActivity = await prisma.auditLog.findMany({
    where: {
      timestamp: {
        gte: daysAgo
      }
    },
    select: {
      timestamp: true
    }
  });

  // Group daily activity by date
  const dailyGrouped = dailyActivity.reduce((acc, log) => {
    const date = log.timestamp.toISOString().split('T')[0];
    acc[date] = (acc[date] || 0) + 1;
    return acc;
  }, {});

  // Get top users by activity
  const topUsers = await prisma.auditLog.groupBy({
    by: ['userId'],
    _count: {
      id: true
    },
    where: {
      timestamp: {
        gte: daysAgo
      }
    },
    orderBy: {
      _count: {
        id: 'desc'
      }
    },
    take: 10
  });

  // Get user details for top users
  const userIds = topUsers.map(u => u.userId).filter(Boolean);
  const users = await prisma.user.findMany({
    where: {
      id: {
        in: userIds
      }
    },
    select: {
      id: true,
      name: true,
      email: true
    }
  });

  const topUsersWithDetails = topUsers.map(stat => {
    const user = users.find(u => u.id === stat.userId);
    return {
      user: user || { id: stat.userId, name: 'Unknown', email: 'Unknown' },
      activityCount: stat._count.id
    };
  });

  res.status(200).json({
    success: true,
    data: {
      period: parseInt(period),
      totalLogs: dailyActivity.length,
      categoryBreakdown: categoryStats.reduce((acc, item) => {
        acc[item.category] = item._count.id;
        return acc;
      }, {}),
      severityBreakdown: severityStats.reduce((acc, item) => {
        acc[item.severity] = item._count.id;
        return acc;
      }, {}),
      dailyActivity: dailyGrouped,
      topUsers: topUsersWithDetails
    }
  });
});

/**
 * Create audit log entry
 */
const createAuditLog = asyncHandler(async (req, res) => {
  const {
    action,
    details,
    category,
    severity = 'Info',
    entityType,
    entityId,
    entityName,
    userAgent,
    ip
  } = req.body;

  const auditLog = await prisma.auditLog.create({
    data: {
      action,
      details,
      category,
      severity,
      entityType,
      entityId,
      entityName,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: userAgent || req.get('User-Agent'),
      ip: ip || req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(201).json({
    success: true,
    data: auditLog
  });
});

/**
 * Export audit logs
 */
const exportAuditLogs = asyncHandler(async (req, res) => {
  const { format = 'csv', ...filters } = req.query;

  // Apply same filtering as getAuditLogs
  const where = {};
  
  if (filters.category && filters.category !== 'all') {
    where.category = filters.category;
  }

  if (filters.severity && filters.severity !== 'all') {
    where.severity = filters.severity;
  }

  if (filters.startDate && filters.endDate) {
    where.timestamp = {
      gte: new Date(filters.startDate),
      lte: new Date(filters.endDate)
    };
  }

  const auditLogs = await prisma.auditLog.findMany({
    where,
    include: {
      user: {
        select: {
          name: true,
          email: true
        }
      }
    },
    orderBy: {
      timestamp: 'desc'
    }
  });

  // In a real implementation, you would generate the actual file here
  // For now, return a success response with download URL
  const filename = `audit-logs-${Date.now()}.${format}`;
  
  res.status(200).json({
    success: true,
    message: `Audit logs exported successfully in ${format.toUpperCase()} format`,
    data: {
      filename,
      recordCount: auditLogs.length,
      downloadUrl: `/exports/${filename}`
    }
  });
});

/**
 * Get audit log by ID
 */
const getAuditLogById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const auditLog = await prisma.auditLog.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    }
  });

  if (!auditLog) {
    return res.status(404).json({
      success: false,
      message: 'Audit log not found'
    });
  }

  res.status(200).json({
    success: true,
    data: auditLog
  });
});

module.exports = {
  getAuditLogs,
  getAuditStats,
  createAuditLog,
  exportAuditLogs,
  getAuditLogById
};
