"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useState, useEffect, ReactNode } from "react"
import { useRouter } from "next/navigation"

interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: string
  isEmailVerified: boolean
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string, mfaToken?: string) => Promise<{
    success: boolean
    user?: User
    error?: string
    mfaRequired?: boolean
  }>
  logout: () => Promise<void>
  refreshToken: () => Promise<string | null>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  // Set up automatic token refresh
  useEffect(() => {
    let refreshInterval: NodeJS.Timeout

    if (user) {
      // Check token every 10 minutes and refresh if needed
      refreshInterval = setInterval(async () => {
        console.log("🔄 AuthProvider: Checking token status...")
        try {
          const response = await fetch("/api/auth/session", {
            credentials: 'include'
          })

          if (response.status === 401) {
            console.log("🔄 AuthProvider: Token expired, attempting refresh...")
            const newToken = await refreshToken()
            if (!newToken) {
              console.log("❌ AuthProvider: Refresh failed, logging out...")
              await logout()
            } else {
              console.log("✅ AuthProvider: Token refreshed successfully")
            }
          }
        } catch (error) {
          console.error("❌ AuthProvider: Token check failed:", error)
        }
      }, 10 * 60 * 1000) // 10 minutes
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval)
      }
    }
  }, [user])

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch("/api/auth/session", {
        credentials: 'include',
        cache: 'no-cache'
      })

      if (!response.ok) {
        throw new Error(`Auth check failed: ${response.status}`)
      }

      const data = await response.json()

      if (data.user) {
        setUser(data.user)
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error("Auth check failed:", error)
      setUser(null)
      // Don't throw the error here to prevent error boundary from catching it
      // This is expected behavior when user is not authenticated
    } finally {
      setIsLoading(false)
    }
  }

  const refreshToken = async () => {
    try {
      const response = await fetch("/api/auth/refresh", {
        method: "POST",
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        return data.accessToken
      }
      return null
    } catch (error) {
      console.error("Token refresh error:", error)
      return null
    }
  }

  const login = async (email: string, password: string, mfaToken?: string) => {
    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: 'include',
        body: JSON.stringify({ email, password, mfaToken }),
      })

      const data = await response.json()

      if (response.ok) {
        setUser(data.user)
        return { success: true, user: data.user }
      } else {
        return {
          success: false,
          error: data.message || "Login failed",
          mfaRequired: data.mfaRequired,
        }
      }
    } catch (error) {
      console.error("Login error:", error)
      return { success: false, error: "Network error. Please check your connection and try again." }
    }
  }

  const logout = async () => {
    try {
      // Call logout API
      await fetch("/api/auth/logout", {
        method: "POST",
        credentials: 'include'
      })
    } catch (error) {
      console.error("Logout API failed:", error)
      // Continue with local cleanup even if API fails
    } finally {
      // Always clear local state regardless of API success/failure
      setUser(null)

      // Clear any localStorage items that might contain auth data
      try {
        localStorage.removeItem("isLoggedIn")
        localStorage.removeItem("currentUser")
        localStorage.removeItem("currentInstitutionId")
        localStorage.removeItem("authToken")
        localStorage.removeItem("refreshToken")
      } catch (storageError) {
        console.warn("Failed to clear localStorage:", storageError)
      }

      // Redirect to login page
      router.push("/auth/login")
    }
  }

  return (
    <AuthContext.Provider value={{ user, isLoading, isAuthenticated: !!user, login, logout, refreshToken }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
} 