{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node src/server.js", "dev": "nodemon src/server.js", "prisma:generate": "npx prisma generate", "create-test-users": "node scripts/create-test-users.js", "prisma:migrate": "npx prisma migrate dev", "prisma:studio": "npx prisma studio", "init:db": "node src/utils/initDb.js", "test-email": "node scripts/test-email.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@prisma/client": "^6.8.2", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/node": "^22.15.21", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "eslint": "^9.27.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "prisma": "^6.8.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}