const { PrismaClient } = require('@prisma/client');
const asyncHandler = require('../../../utils/asyncHandler');

const prisma = new PrismaClient();

/**
 * Get analytics dashboard stats
 */
const getAnalyticsStats = asyncHandler(async (req, res) => {
  // Get total institutions by status
  const totalInstitutions = await prisma.institution.count();
  const activeInstitutions = await prisma.institution.count({
    where: { verificationStatus: true }
  });
  const pendingInstitutions = await prisma.institution.count({
    where: { verificationStatus: false }
  });

  // Get total users by role
  const totalUsers = await prisma.user.count();
  const adminUsers = await prisma.user.count({
    where: { role: 'ADMIN' }
  });
  const teacherUsers = await prisma.user.count({
    where: { role: 'TEACHER' }
  });
  const studentUsers = await prisma.user.count({
    where: { role: 'STUDENT' }
  });

  // Get total students (mock for now - would need proper student table)
  const totalStudents = studentUsers; // This would be actual student count from student table

  // Calculate growth rates (mock data for now)
  const institutionGrowthRate = 12.5;
  const userGrowthRate = 8.3;
  const studentGrowthRate = 6.8;

  res.status(200).json({
    success: true,
    data: {
      institutions: {
        total: totalInstitutions,
        active: activeInstitutions,
        pending: pendingInstitutions,
        growthRate: institutionGrowthRate
      },
      users: {
        total: totalUsers,
        admin: adminUsers,
        teacher: teacherUsers,
        student: studentUsers,
        growthRate: userGrowthRate
      },
      students: {
        total: totalStudents,
        growthRate: studentGrowthRate
      }
    }
  });
});

/**
 * Get institution analytics
 */
const getInstitutionAnalytics = asyncHandler(async (req, res) => {
  // Get institutions with user counts
  const institutions = await prisma.institution.findMany({
    include: {
      users: true,
      _count: {
        select: {
          users: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  // Calculate institution distribution by type (mock data)
  const institutionTypes = {
    'Primary School': institutions.filter(i => i.name.toLowerCase().includes('primary')).length,
    'Secondary School': institutions.filter(i => i.name.toLowerCase().includes('secondary')).length,
    'College': institutions.filter(i => i.name.toLowerCase().includes('college')).length,
    'University': institutions.filter(i => i.name.toLowerCase().includes('university')).length
  };

  // Monthly registration data (mock)
  const monthlyRegistrations = [
    { month: 'Jan', count: 5 },
    { month: 'Feb', count: 8 },
    { month: 'Mar', count: 12 },
    { month: 'Apr', count: 15 },
    { month: 'May', count: 18 }
  ];

  res.status(200).json({
    success: true,
    data: {
      institutions: institutions.map(institution => ({
        id: institution.id,
        name: institution.name,
        userCount: institution._count.users,
        verificationStatus: institution.verificationStatus,
        createdAt: institution.createdAt
      })),
      distribution: institutionTypes,
      monthlyRegistrations
    }
  });
});

/**
 * Get user analytics
 */
const getUserAnalytics = asyncHandler(async (req, res) => {
  // Get user distribution by role
  const usersByRole = await prisma.user.groupBy({
    by: ['role'],
    _count: {
      id: true
    }
  });

  // Get user activity data (mock for now - would need activity tracking)
  const userActivity = {
    activeToday: Math.floor(Math.random() * 100) + 50,
    activeThisWeek: Math.floor(Math.random() * 500) + 200,
    activeThisMonth: Math.floor(Math.random() * 1000) + 500
  };

  // Get registration trends (last 6 months)
  const registrationTrends = [
    { month: 'Dec 2024', count: 45 },
    { month: 'Jan 2025', count: 62 },
    { month: 'Feb 2025', count: 58 },
    { month: 'Mar 2025', count: 73 },
    { month: 'Apr 2025', count: 84 },
    { month: 'May 2025', count: 91 }
  ];

  res.status(200).json({
    success: true,
    data: {
      roleDistribution: usersByRole.reduce((acc, item) => {
        acc[item.role] = item._count.id;
        return acc;
      }, {}),
      activity: userActivity,
      registrationTrends
    }
  });
});

/**
 * Get revenue analytics
 */
const getRevenueAnalytics = asyncHandler(async (req, res) => {
  // Mock revenue data (would come from billing/subscription system)
  const revenueData = {
    totalRevenue: 125000,
    monthlyRevenue: 18500,
    growthRate: 15.2,
    subscriptionDistribution: {
      Basic: 45000,
      Standard: 28800,
      Premium: 45000,
      Enterprise: 6200
    },
    monthlyTrends: [
      { month: 'Jan', revenue: 15200 },
      { month: 'Feb', revenue: 16800 },
      { month: 'Mar', revenue: 17200 },
      { month: 'Apr', revenue: 17800 },
      { month: 'May', revenue: 18500 }
    ],
    topPayingInstitutions: [
      { name: 'Lusaka International School', amount: 5200, plan: 'Enterprise' },
      { name: 'Kitwe Secondary School', amount: 2500, plan: 'Premium' },
      { name: 'Ndola Primary Academy', amount: 2500, plan: 'Premium' },
      { name: 'Copper Belt College', amount: 2500, plan: 'Premium' },
      { name: 'Central Province School', amount: 1500, plan: 'Standard' }
    ]
  };

  res.status(200).json({
    success: true,
    data: revenueData
  });
});

/**
 * Export analytics data
 */
const exportAnalytics = asyncHandler(async (req, res) => {
  const { type, format } = req.query;

  // This would generate actual export data based on type and format
  // For now, return a success message
  res.status(200).json({
    success: true,
    message: `${type} analytics exported successfully in ${format} format`,
    downloadUrl: `/exports/analytics-${type}-${Date.now()}.${format}`
  });
});

module.exports = {
  getAnalyticsStats,
  getInstitutionAnalytics,
  getUserAnalytics,
  getRevenueAnalytics,
  exportAnalytics
};
