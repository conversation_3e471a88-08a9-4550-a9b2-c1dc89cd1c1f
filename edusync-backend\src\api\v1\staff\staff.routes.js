const express = require('express');
const router = express.Router();
const staffController = require('./staff.controller');
const { authenticate } = require('../../../middleware/authenticate');
const { authorize } = require('../../../middleware/authorization');

// Apply authentication to all routes
router.use(authenticate);

// GET /api/v1/staff - Get all staff with pagination and filtering
router.get('/', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), staffController.getAllStaff);

// GET /api/v1/staff/stats - Get staff statistics
router.get('/stats', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), staffController.getStaffStats);

// GET /api/v1/staff/:id - Get staff by ID
router.get('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), staffController.getStaffById);

// POST /api/v1/staff - Create new staff member
router.post('/', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN']), staffController.createStaff);

// PUT /api/v1/staff/:id - Update staff member
router.put('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), staffController.updateStaff);

// DELETE /api/v1/staff/:id - Delete staff member
router.delete('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN']), staffController.deleteStaff);

module.exports = router;
