"use client"

import type React from "react"

import { useState } from "react"
import { Building, Upload } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useOnboarding } from "@/contexts/onboarding-context"

export function InstitutionDetailsStep() {
  const { onboardingData, updateOnboardingData, nextStep, prevStep } = useOnboarding()
  const [logo, setLogo] = useState<File | null>(null)

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setLogo(file)
      updateOnboardingData({ logo: file })
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    nextStep()
  }

  return (
    <Card className="border-0 shadow-none">
      <CardHeader>
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100">
          <Building className="h-6 w-6 text-emerald-600" />
        </div>
        <CardTitle className="text-center text-2xl">Institution Details</CardTitle>
        <CardDescription className="text-center">Tell us about your educational institution</CardDescription>
      </CardHeader>
      <CardContent>
        <form id="institution-form" onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="institutionName">Institution Name</Label>
            <Input
              id="institutionName"
              placeholder="e.g., Lusaka Primary School"
              value={onboardingData.institutionName}
              onChange={(e) => updateOnboardingData({ institutionName: e.target.value })}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="institutionType">Institution Type</Label>
            <Select
              value={onboardingData.institutionType}
              onValueChange={(value) => updateOnboardingData({ institutionType: value })}
              required
            >
              <SelectTrigger id="institutionType">
                <SelectValue placeholder="Select institution type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="preschool">Pre-School</SelectItem>
                <SelectItem value="primary">Primary School</SelectItem>
                <SelectItem value="secondary">Secondary School</SelectItem>
                <SelectItem value="college">College</SelectItem>
                <SelectItem value="university">University</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">Address</Label>
            <Textarea
              id="address"
              placeholder="Street address"
              value={onboardingData.address}
              onChange={(e) => updateOnboardingData({ address: e.target.value })}
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                placeholder="e.g., Lusaka"
                value={onboardingData.city}
                onChange={(e) => updateOnboardingData({ city: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <Input
                id="country"
                value={onboardingData.country}
                onChange={(e) => updateOnboardingData({ country: e.target.value })}
                disabled
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="logo">Institution Logo (Optional)</Label>
            <div className="flex items-center gap-4">
              <div className="h-16 w-16 rounded-md border flex items-center justify-center overflow-hidden">
                {logo ? (
                  <img
                    src={URL.createObjectURL(logo) || "/placeholder.svg"}
                    alt="Logo preview"
                    className="h-full w-full object-contain"
                  />
                ) : (
                  <div className="text-2xl font-bold text-muted-foreground/30">
                    {onboardingData.institutionName ? onboardingData.institutionName.charAt(0) : "L"}
                  </div>
                )}
              </div>
              <div className="flex-1">
                <Input id="logo" type="file" accept="image/*" onChange={handleLogoChange} className="hidden" />
                <Button type="button" variant="outline" onClick={() => document.getElementById("logo")?.click()}>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Logo
                </Button>
              </div>
            </div>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={prevStep}>
          Back
        </Button>
        <Button type="submit" form="institution-form">
          Continue
        </Button>
      </CardFooter>
    </Card>
  )
}
