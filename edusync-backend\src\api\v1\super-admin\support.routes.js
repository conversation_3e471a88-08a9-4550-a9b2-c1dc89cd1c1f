const express = require('express');
const router = express.Router();
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const {
  getSupportTickets,
  getSupportTicketById,
  createSupportTicket,
  updateSupportTicket,
  addTicketMessage,
  deleteSupportTicket,
  getSupportStats,
  getKnowledgeBase,
  createKnowledgeBaseArticle,
  updateKnowledgeBaseArticle,
  deleteKnowledgeBaseArticle
} = require('./support.controller');

// Protect all routes and restrict to SUPER_ADMIN
router.use(authenticate);
router.use(restrictTo('SUPER_ADMIN'));

// Support statistics
router.get('/stats', getSupportStats);

// Support tickets
router.get('/tickets', getSupportTickets);
router.get('/tickets/:id', getSupportTicketById);
router.post('/tickets', createSupportTicket);
router.put('/tickets/:id', updateSupportTicket);
router.post('/tickets/:id/messages', addTicketMessage);
router.delete('/tickets/:id', deleteSupportTicket);

// Knowledge base
router.get('/knowledge-base', getKnowledgeBase);
router.post('/knowledge-base', createKnowledgeBaseArticle);
router.put('/knowledge-base/:id', updateKnowledgeBaseArticle);
router.delete('/knowledge-base/:id', deleteKnowledgeBaseArticle);

module.exports = router;
