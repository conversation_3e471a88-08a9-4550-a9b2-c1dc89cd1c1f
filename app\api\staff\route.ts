import { NextRequest, NextResponse } from "next/server"
import { authenticateUser, createAuthHead<PERSON> } from "@/lib/auth"

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request)
    
    if (!auth.user) {
      return NextResponse.json({ error: auth.error }, { status: auth.status })
    }

    const { searchParams } = new URL(request.url)
    const queryString = searchParams.toString()

    const response = await fetch(`${BACKEND_URL}/api/v1/staff?${queryString}`, {
      method: "GET",
      headers: await createAuthHeaders(request),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Staff API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request)
    
    if (!auth.user) {
      return NextResponse.json({ error: auth.error }, { status: auth.status })
    }

    const body = await request.json()

    const response = await fetch(`${BACKEND_URL}/api/v1/staff`, {
      method: "POST",
      headers: await createAuthHeaders(request),
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status })
    }

    return NextResponse.json(data, { status: 201 })
  } catch (error) {
    console.error("Staff creation error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
