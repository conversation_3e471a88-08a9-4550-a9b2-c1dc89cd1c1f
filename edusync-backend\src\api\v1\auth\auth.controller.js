const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const { PrismaClient } = require('@prisma/client');
const { 
  generateTokens, 
  verifyRefreshToken 
} = require('../../../services/tokenService');
const { 
  sendPasswordResetEmail, 
  sendVerificationEmail 
} = require('../../../services/emailService');
const catchAsync = require('../../../utils/catchAsync');
const AppError = require('../../../utils/appError');
const { logger } = require('../../../utils/logger');
const { createAuditLog } = require('../../../services/auditService');

const prisma = new PrismaClient();

/**
 * Register a new user
 */
exports.register = catchAsync(async (req, res) => {
  const { email, password, firstName, lastName, role, phoneNumber } = req.body;

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email },
  });

  if (existingUser) {
    throw new AppError('User with this email already exists', 400);
  }

  // Hash password
  const passwordHash = await bcrypt.hash(password, 12);

  // Generate email verification token
  const emailVerificationToken = uuidv4();
  const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

  // Create new user
  const user = await prisma.user.create({
    data: {
      email,
      passwordHash,
      firstName,
      lastName,
      role,
      phoneNumber,
      emailVerificationToken,
      emailVerificationExpires,
    },
  });

  // Remove sensitive information
  delete user.passwordHash;
  delete user.emailVerificationToken;
  delete user.emailVerificationExpires;

  // Send verification email
  await sendVerificationEmail(user.email, emailVerificationToken);

  // Create audit log
  await createAuditLog({
    userId: user.id,
    action: 'USER_REGISTERED',
    details: 'User registered successfully',
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'User',
    resourceId: user.id,
  });

  // Return user data
  res.status(201).json({
    status: 'success',
    message: 'User registered successfully. Please check your email to verify your account.',
    data: { user },
  });
});

/**
 * Login user
 */
exports.login = catchAsync(async (req, res) => {
  const { email, password } = req.body;

  // Find user by email
  const user = await prisma.user.findUnique({
    where: { email },
  });

  // Check if user exists and password is correct
  if (!user || !(await bcrypt.compare(password, user.passwordHash))) {
    throw new AppError('Invalid email or password', 401);
  }

  // Check if user is active
  if (!user.isActive) {
    throw new AppError('Your account has been deactivated. Please contact support.', 403);
  }

  // Generate tokens
  const { accessToken, refreshToken } = await generateTokens(user);

  // Update last login timestamp
  await prisma.user.update({
    where: { id: user.id },
    data: { lastLoginAt: new Date() },
  });

  // Create audit log
  await createAuditLog({
    userId: user.id,
    action: 'USER_LOGGED_IN',
    details: 'User logged in successfully',
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'User',
    resourceId: user.id,
  });

  // Set refresh token as HTTP-only cookie
  res.cookie('refreshToken', refreshToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  });

  // Return access token and user data
res.status(200).json({
  status: 'success',
  data: {
    accessToken,
    user: {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      isEmailVerified: user.isEmailVerified,
    },
  },
});
});

/**
 * Refresh access token
 */
exports.refreshToken = catchAsync(async (req, res) => {
  // Get refresh token from cookie or request body
  const refreshToken = req.cookies.refreshToken || req.body.refreshToken;

  if (!refreshToken) {
    throw new AppError('Refresh token is required', 400);
  }

  // Verify refresh token
  const { userId } = await verifyRefreshToken(refreshToken);

  // Find user
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (!user || !user.isActive) {
    throw new AppError('Invalid or expired refresh token', 401);
  }

  // Generate new tokens
  const tokens = await generateTokens(user);

  // Set refresh token as HTTP-only cookie
  res.cookie('refreshToken', tokens.refreshToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  });

  // Return new access token
  res.status(200).json({
    status: 'success',
    data: { accessToken: tokens.accessToken },
  });
});

/**
 * Logout user
 */
exports.logout = catchAsync(async (req, res) => {
  const refreshToken = req.cookies.refreshToken;

  if (refreshToken) {
    // Find and revoke refresh token
    const token = await prisma.refreshToken.findFirst({
      where: { token: refreshToken },
    });

    if (token) {
      await prisma.refreshToken.update({
        where: { id: token.id },
        data: { isRevoked: true },
      });
    }

    // Create audit log
    await createAuditLog({
      userId: req.user.id,
      action: 'USER_LOGGED_OUT',
      details: 'User logged out successfully',
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      resourceType: 'User',
      resourceId: req.user.id,
    });
  }

  // Clear refresh token cookie
  res.clearCookie('refreshToken');

  res.status(200).json({
    status: 'success',
    message: 'Logged out successfully',
  });
});

/**
 * Request password reset
 */
exports.forgotPassword = catchAsync(async (req, res) => {
  const { email } = req.body;

  // Find user by email
  const user = await prisma.user.findUnique({
    where: { email },
  });

  // If user doesn't exist, still return success response for security
  if (!user) {
    return res.status(200).json({
      status: 'success',
      message: 'If a user with that email exists, a password reset link has been sent',
    });
  }

  // Generate password reset token
  const passwordResetToken = uuidv4();
  const passwordResetExpires = new Date(Date.now() + 1 * 60 * 60 * 1000); // 1 hour

  // Update user with reset token
  await prisma.user.update({
    where: { id: user.id },
    data: {
      passwordResetToken,
      passwordResetExpires,
    },
  });

  // Send password reset email
  await sendPasswordResetEmail(user.email, passwordResetToken);

  // Create audit log
  await createAuditLog({
    userId: user.id,
    action: 'PASSWORD_RESET_REQUESTED',
    details: 'Password reset requested',
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'User',
    resourceId: user.id,
  });

  res.status(200).json({
    status: 'success',
    message: 'If a user with that email exists, a password reset link has been sent',
  });
});

/**
 * Reset password with token
 */
exports.resetPassword = catchAsync(async (req, res) => {
  const { token } = req.params;
  const { password } = req.body;

  // Find user by reset token
  const user = await prisma.user.findFirst({
    where: {
      passwordResetToken: token,
      passwordResetExpires: {
        gt: new Date(),
      },
    },
  });

  if (!user) {
    throw new AppError('Invalid or expired password reset token', 400);
  }

  // Hash new password
  const passwordHash = await bcrypt.hash(password, 12);

  // Update user password and clear reset token
  await prisma.user.update({
    where: { id: user.id },
    data: {
      passwordHash,
      passwordResetToken: null,
      passwordResetExpires: null,
    },
  });

  // Revoke all refresh tokens for user
  await prisma.refreshToken.updateMany({
    where: { userId: user.id },
    data: { isRevoked: true },
  });

  // Create audit log
  await createAuditLog({
    userId: user.id,
    action: 'PASSWORD_RESET',
    details: 'Password reset successfully',
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'User',
    resourceId: user.id,
  });

  res.status(200).json({
    status: 'success',
    message: 'Password reset successfully. Please log in with your new password.',
  });
});

/**
 * Verify email address
 */
exports.verifyEmail = catchAsync(async (req, res) => {
  const { token } = req.params;

  // Find user by verification token
  const user = await prisma.user.findFirst({
    where: {
      emailVerificationToken: token,
      emailVerificationExpires: {
        gt: new Date(),
      },
    },
  });

  if (!user) {
    throw new AppError('Invalid or expired email verification token', 400);
  }

  // Update user as verified and clear verification token
  await prisma.user.update({
    where: { id: user.id },
    data: {
      isEmailVerified: true,
      emailVerificationToken: null,
      emailVerificationExpires: null,
    },
  });

  // Create audit log
  await createAuditLog({
    userId: user.id,
    action: 'EMAIL_VERIFIED',
    details: 'Email verified successfully',
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'User',
    resourceId: user.id,
  });

  // Redirect to frontend verification success page
  res.redirect(`${process.env.FRONTEND_URL}/auth/verify-email/success`);
});

/**
 * Resend email verification
 */
exports.resendVerification = catchAsync(async (req, res) => {
  const userId = req.user.id;

  // Find user
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (!user) {
    throw new AppError('User not found', 404);
  }

  if (user.isEmailVerified) {
    throw new AppError('Email is already verified', 400);
  }

  // Generate new verification token
  const emailVerificationToken = uuidv4();
  const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

  // Update user with new verification token
  await prisma.user.update({
    where: { id: user.id },
    data: {
      emailVerificationToken,
      emailVerificationExpires,
    },
  });

  // Send verification email
  await sendVerificationEmail(user.email, emailVerificationToken);

  // Create audit log
  await createAuditLog({
    userId: user.id,
    action: 'VERIFICATION_EMAIL_RESENT',
    details: 'Verification email resent',
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'User',
    resourceId: user.id,
  });

  res.status(200).json({
    status: 'success',
    message: 'Verification email has been sent',
  });
});

/**
 * Get current user information
 */
exports.getCurrentUser = catchAsync(async (req, res) => {
  const userId = req.user.id;

  // Find user with related data
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      phoneNumber: true,
      profileImageUrl: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      isActive: true,
      createdAt: true,
      institutions: {
        select: {
          institution: {
            select: {
              id: true,
              name: true,
              logo: true,
              isActive: true,
            },
          },
          role: true,
        },
      },
      schools: {
        select: {
          school: {
            select: {
              id: true,
              name: true,
              type: true,
              institutionId: true,
              address: true,
              city: true,
              state: true,
              country: true,
              postalCode: true,
              phoneNumber: true,
              email: true,
              website: true,
              isActive: true,
            },
          },
          role: true,
        },
      },
    },
  });

  if (!user) {
    throw new AppError('User not found', 404);
  }

  res.status(200).json({
    status: 'success',
    data: { user },
  });
});

/**
 * Change user password
 */
exports.changePassword = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const { currentPassword, newPassword } = req.body;

  // Find user
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Verify current password
  const isCurrentPasswordValid = await bcrypt.compare(
    currentPassword,
    user.passwordHash
  );

  if (!isCurrentPasswordValid) {
    throw new AppError('Current password is incorrect', 401);
  }

  // Hash new password
  const passwordHash = await bcrypt.hash(newPassword, 12);

  // Update user password
  await prisma.user.update({
    where: { id: user.id },
    data: { passwordHash },
  });

  // Revoke all refresh tokens for user
  await prisma.refreshToken.updateMany({
    where: { userId: user.id },
    data: { isRevoked: true },
  });

  // Create audit log
  await createAuditLog({
    userId: user.id,
    action: 'PASSWORD_CHANGED',
    details: 'Password changed successfully',
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'User',
    resourceId: user.id,
  });

  res.status(200).json({
    status: 'success',
    message: 'Password changed successfully. Please log in again with your new password.',
  });
});
