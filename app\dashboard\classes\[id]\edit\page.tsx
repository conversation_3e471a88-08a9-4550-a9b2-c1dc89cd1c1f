"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON>er } from "next/navigation"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Clock, BookOpen, Save, X } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "@/components/ui/use-toast"
import { InstitutionProtectedPage } from "@/components/auth/protected-page"

// Mock data for the class
const mockClass = {
  id: "1",
  name: "Class 10-A",
  section: "A",
  grade: "10",
  academicYear: "2023-2024",
  roomNumber: "301",
  capacity: 35,
  currentStudents: 32,
  classTeacher: "Ms. Sarah <PERSON>",
  description: "Advanced Science and Mathematics focused class for 10th grade students.",
  schedule: [
    { day: "Monday", periods: ["Mathematics", "Physics", "English", "History", "Computer Science"] },
    { day: "Tuesday", periods: ["Chemistry", "Mathematics", "Physical Education", "Geography", "English"] },
    { day: "Wednesday", periods: ["Physics", "Biology", "Mathematics", "Art", "Language"] },
    { day: "Thursday", periods: ["English", "Chemistry", "History", "Mathematics", "Music"] },
    { day: "Friday", periods: ["Mathematics", "Physics", "Computer Science", "Biology", "Ethics"] },
  ],
  subjects: [
    { id: "1", name: "Mathematics", teacher: "Mr. Robert Chen", hoursPerWeek: 6 },
    { id: "2", name: "Physics", teacher: "Dr. Emily Watson", hoursPerWeek: 4 },
    { id: "3", name: "Chemistry", teacher: "Ms. Lisa Rodriguez", hoursPerWeek: 3 },
    { id: "4", name: "Biology", teacher: "Mr. James Wilson", hoursPerWeek: 3 },
    { id: "5", name: "English", teacher: "Ms. Sarah Johnson", hoursPerWeek: 4 },
    { id: "6", name: "History", teacher: "Dr. Michael Brown", hoursPerWeek: 2 },
    { id: "7", name: "Geography", teacher: "Ms. Patricia Miller", hoursPerWeek: 1 },
    { id: "8", name: "Computer Science", teacher: "Mr. David Lee", hoursPerWeek: 2 },
    { id: "9", name: "Physical Education", teacher: "Mr. Thomas Martinez", hoursPerWeek: 2 },
    { id: "10", name: "Art", teacher: "Ms. Jennifer Taylor", hoursPerWeek: 1 },
    { id: "11", name: "Music", teacher: "Mr. Christopher Davis", hoursPerWeek: 1 },
    { id: "12", name: "Language", teacher: "Ms. Elizabeth Garcia", hoursPerWeek: 1 },
    { id: "13", name: "Ethics", teacher: "Dr. Susan Anderson", hoursPerWeek: 1 },
  ],
}

// Mock data for teachers
const teachers = [
  { id: "1", name: "Ms. Sarah Johnson" },
  { id: "2", name: "Mr. Robert Chen" },
  { id: "3", name: "Dr. Emily Watson" },
  { id: "4", name: "Ms. Lisa Rodriguez" },
  { id: "5", name: "Mr. James Wilson" },
  { id: "6", name: "Dr. Michael Brown" },
  { id: "7", name: "Ms. Patricia Miller" },
  { id: "8", name: "Mr. David Lee" },
]

// Form schema
const formSchema = z.object({
  name: z.string().min(2, {
    message: "Class name must be at least 2 characters.",
  }),
  section: z.string().min(1, {
    message: "Section is required.",
  }),
  grade: z.string().min(1, {
    message: "Grade is required.",
  }),
  academicYear: z.string().min(4, {
    message: "Academic year is required.",
  }),
  roomNumber: z.string().min(1, {
    message: "Room number is required.",
  }),
  capacity: z.coerce.number().min(1, {
    message: "Capacity must be at least 1.",
  }),
  classTeacher: z.string().min(1, {
    message: "Class teacher is required.",
  }),
  description: z.string().optional(),
})

function EditClassContent() {
  const params = useParams()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  // Initialize form with mock data
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: mockClass.name,
      section: mockClass.section,
      grade: mockClass.grade,
      academicYear: mockClass.academicYear,
      roomNumber: mockClass.roomNumber,
      capacity: mockClass.capacity,
      classTeacher: mockClass.classTeacher,
      description: mockClass.description,
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      console.log(values)
      setIsLoading(false)
      toast({
        title: "Class updated",
        description: `${values.name} has been updated successfully.`,
      })
      router.push("/dashboard/classes")
    }, 1000)
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Class</h1>
          <p className="text-muted-foreground">Update information for {mockClass.name}</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => router.back()}>
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button onClick={form.handleSubmit(onSubmit)} disabled={isLoading}>
            <Save className="mr-2 h-4 w-4" />
            Save Changes
          </Button>
        </div>
      </div>

      <Tabs defaultValue="general">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="general">General Information</TabsTrigger>
          <TabsTrigger value="subjects">Subjects</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Class Details</CardTitle>
              <CardDescription>Update the basic information about this class.</CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Class Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Class 10-A" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="section"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Section</FormLabel>
                          <FormControl>
                            <Input placeholder="A" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="grade"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Grade</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select grade" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Array.from({ length: 12 }, (_, i) => (
                                <SelectItem key={i + 1} value={String(i + 1)}>
                                  Grade {i + 1}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="academicYear"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Academic Year</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select academic year" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="2022-2023">2022-2023</SelectItem>
                              <SelectItem value="2023-2024">2023-2024</SelectItem>
                              <SelectItem value="2024-2025">2024-2025</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="roomNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Room Number</FormLabel>
                          <FormControl>
                            <Input placeholder="301" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="capacity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Capacity</FormLabel>
                          <FormControl>
                            <Input type="number" placeholder="35" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="classTeacher"
                      render={({ field }) => (
                        <FormItem className="col-span-1 md:col-span-2">
                          <FormLabel>Class Teacher</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select class teacher" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {teachers.map((teacher) => (
                                <SelectItem key={teacher.id} value={teacher.name}>
                                  {teacher.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem className="col-span-1 md:col-span-2">
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter class description"
                              className="resize-none"
                              rows={4}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subjects" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Manage Subjects</CardTitle>
              <CardDescription>Add, remove, or update subjects for this class.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Current Subjects</h3>
                  <Button size="sm">
                    <BookOpen className="mr-2 h-4 w-4" />
                    Add Subject
                  </Button>
                </div>

                <div className="border rounded-md">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="py-3 px-4 text-left font-medium">Subject</th>
                        <th className="py-3 px-4 text-left font-medium">Teacher</th>
                        <th className="py-3 px-4 text-left font-medium">Hours/Week</th>
                        <th className="py-3 px-4 text-right font-medium">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mockClass.subjects.map((subject) => (
                        <tr key={subject.id} className="border-b last:border-0">
                          <td className="py-3 px-4">{subject.name}</td>
                          <td className="py-3 px-4">{subject.teacher}</td>
                          <td className="py-3 px-4">{subject.hoursPerWeek}</td>
                          <td className="py-3 px-4 text-right">
                            <Button variant="ghost" size="sm">
                              Edit
                            </Button>
                            <Button variant="ghost" size="sm" className="text-destructive">
                              Remove
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Class Schedule</CardTitle>
              <CardDescription>Manage the weekly schedule for this class.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Weekly Timetable</h3>
                  <Button size="sm">
                    <Clock className="mr-2 h-4 w-4" />
                    Edit Schedule
                  </Button>
                </div>

                <div className="border rounded-md overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="py-3 px-4 text-left font-medium">Day</th>
                        <th className="py-3 px-4 text-left font-medium">Period 1</th>
                        <th className="py-3 px-4 text-left font-medium">Period 2</th>
                        <th className="py-3 px-4 text-left font-medium">Period 3</th>
                        <th className="py-3 px-4 text-left font-medium">Period 4</th>
                        <th className="py-3 px-4 text-left font-medium">Period 5</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mockClass.schedule.map((day) => (
                        <tr key={day.day} className="border-b last:border-0">
                          <td className="py-3 px-4 font-medium">{day.day}</td>
                          {day.periods.map((period, index) => (
                            <td key={index} className="py-3 px-4">
                              {period}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default function EditClassPage() {
  return (
    <InstitutionProtectedPage>
      <EditClassContent />
    </InstitutionProtectedPage>
  )
}
