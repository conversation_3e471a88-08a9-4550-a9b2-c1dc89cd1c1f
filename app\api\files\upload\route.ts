import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    // Get the form data from the request
    const formData = await request.formData()

    // Get the authorization header
    const authHeader = request.headers.get("authorization")
    if (!authHeader) {
      return NextResponse.json({ error: "Authorization header required" }, { status: 401 })
    }

    // Forward the request to Flask backend
    const response = await fetch(`${process.env.BACKEND_URL}/api/v1/files/upload`, {
      method: "POST",
      headers: {
        Authorization: authHeader,
      },
      body: formData,
    })

    const result = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: result.error || "File upload failed" }, { status: response.status })
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error("File upload API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get("category")
    const page = searchParams.get("page") || "1"
    const per_page = searchParams.get("per_page") || "20"

    // Get the authorization header
    const authHeader = request.headers.get("authorization")
    if (!authHeader) {
      return NextResponse.json({ error: "Authorization header required" }, { status: 401 })
    }

    // Build query string
    const queryParams = new URLSearchParams({
      page,
      per_page,
    })

    if (category) {
      queryParams.append("category", category)
    }

    // Forward the request to Flask backend
    const response = await fetch(`${process.env.BACKEND_URL}/api/v1/files/files?${queryParams}`, {
      method: "GET",
      headers: {
        Authorization: authHeader,
        "Content-Type": "application/json",
      },
    })

    const result = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: result.error || "Failed to fetch files" }, { status: response.status })
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error("Files fetch API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
