const express = require('express');
const router = express.Router();
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const {
  getAllInstitutions,
  getInstitutionById,
  createInstitution,
  updateInstitution,
  deleteInstitution,
  approveInstitution,
  rejectInstitution,
  getInstitutionStats,
  exportInstitutions
} = require('./institutions.controller');

// Apply authentication and super admin role requirement to all routes
router.use(authenticate);
router.use(restrictTo('SUPER_ADMIN'));

// Institution routes
router.get('/', getAllInstitutions);
router.get('/stats', getInstitutionStats);
router.get('/export', exportInstitutions);
router.get('/:id', getInstitutionById);
router.post('/', createInstitution);
router.put('/:id', updateInstitution);
router.delete('/:id', deleteInstitution);

// Institution approval routes
router.post('/:id/approve', approveInstitution);
router.post('/:id/reject', rejectInstitution);

module.exports = router;
