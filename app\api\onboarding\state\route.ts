import { type NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  try {
    // Get auth token from cookies or headers
    const authToken =
      request.cookies.get("accessToken")?.value || request.headers.get("authorization")?.replace("Bearer ", "")

    if (!authToken) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    // Forward request to Flask backend
    const backendUrl = process.env.BACKEND_URL || "http://localhost:4000"
    const response = await fetch(`${backendUrl}/api/v1/onboarding/state`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    })

    if (response.status === 404) {
      // No onboarding state found - this is normal for new users
      return NextResponse.json({ error: "No onboarding state found" }, { status: 404 })
    }

    if (!response.ok) {
      throw new Error("Failed to fetch onboarding state")
    }

    const state = await response.json()
    return NextResponse.json(state)
  } catch (error) {
    console.error("Error fetching onboarding state:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
