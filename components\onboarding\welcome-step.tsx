"use client"

import { School } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useOnboarding } from "@/contexts/onboarding-context"

export function WelcomeStep() {
  const { nextStep } = useOnboarding()

  return (
    <Card className="border-0 shadow-none">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-emerald-100">
          <School className="h-8 w-8 text-emerald-600" />
        </div>
        <CardTitle className="text-2xl">Welcome to Edusync</CardTitle>
        <CardDescription className="text-base">
          Let's set up your school management system in just a few steps
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 text-center">
        <p>
          We'll guide you through setting up your institution profile, administrator account, and initial system
          configuration.
        </p>
        <div className="mx-auto mt-6 grid max-w-md grid-cols-2 gap-4 text-center">
          {[
            { title: "Institution Details", description: "Set up your school information" },
            { title: "Admin Profile", description: "Create your administrator account" },
            { title: "Academic Setup", description: "Configure academic settings" },
            { title: "Invite Users", description: "Add teachers and staff" },
          ].map((step, i) => (
            <div key={i} className="rounded-lg border p-3">
              <h3 className="font-medium">{step.title}</h3>
              <p className="text-xs text-muted-foreground">{step.description}</p>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button onClick={nextStep}>Get Started</Button>
      </CardFooter>
    </Card>
  )
}
