const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { body, param } = require('express-validator');
const router = express.Router();
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const { validateRequest } = require('../../../middleware/validateRequest');
const {
  getInstitutionSettings,
  updateInstitutionSettings,
  uploadInstitutionLogo,
  getInstitutionStats
} = require('./settings.controller');

// Ensure uploads directories exist
const uploadsDir = path.join(process.cwd(), 'uploads');
const institutionsDir = path.join(uploadsDir, 'institutions');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}
if (!fs.existsSync(institutionsDir)) {
  fs.mkdirSync(institutionsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, institutionsDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    // Accept images only
    if (!file.originalname.match(/\.(jpg|jpeg|png|gif|svg)$/)) {
      return cb(new Error('Only image files are allowed!'), false);
    }
    cb(null, true);
  }
});

// Apply authentication to all routes
router.use(authenticate);

/**
 * @route GET /api/v1/institutions/:id/settings
 * @desc Get institution settings
 * @access Private (Institution members)
 */
router.get(
  '/:id/settings',
  [
    param('id').isUUID().withMessage('Invalid institution ID'),
    validateRequest
  ],
  getInstitutionSettings
);

/**
 * @route PATCH /api/v1/institutions/:id/settings
 * @desc Update institution settings
 * @access Private (Institution admins)
 */
router.patch(
  '/:id/settings',
  [
    param('id').isUUID().withMessage('Invalid institution ID'),
    body('category').isIn(['general', 'academic', 'notifications', 'localization', 'security', 'features'])
      .withMessage('Invalid settings category'),
    body('settings').isObject().withMessage('Settings must be an object'),
    validateRequest
  ],
  restrictTo('INSTITUTION_ADMIN', 'SUPER_ADMIN'),
  updateInstitutionSettings
);

/**
 * @route POST /api/v1/institutions/:id/settings/logo
 * @desc Upload institution logo
 * @access Private (Institution admins)
 */
router.post(
  '/:id/settings/logo',
  [
    param('id').isUUID().withMessage('Invalid institution ID'),
    validateRequest
  ],
  restrictTo('INSTITUTION_ADMIN', 'SUPER_ADMIN'),
  upload.single('logo'),
  uploadInstitutionLogo
);

/**
 * @route GET /api/v1/institutions/:id/settings/stats
 * @desc Get institution statistics for settings page
 * @access Private (Institution members)
 */
router.get(
  '/:id/settings/stats',
  [
    param('id').isUUID().withMessage('Invalid institution ID'),
    validateRequest
  ],
  getInstitutionStats
);

module.exports = router;
