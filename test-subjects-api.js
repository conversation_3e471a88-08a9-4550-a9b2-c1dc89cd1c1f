// Simple test to check subjects API response structure
const http = require('http');

function makeRequest(path, method = 'GET', data = null, token = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 4000,
      path: `/api/v1${path}`,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      options.headers['Authorization'] = `Bearer ${token}`;
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsed
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testSubjectsAPI() {
  try {
    console.log('🔐 Testing login...');
    
    // Test login
    const loginResponse = await makeRequest('/auth/login', 'POST', {
      email: '<EMAIL>',
      password: 'Admin@123'
    });

    console.log('Login response status:', loginResponse.status);
    console.log('Login response:', JSON.stringify(loginResponse.data, null, 2));

    if (loginResponse.status !== 200) {
      console.log('❌ Login failed');
      return;
    }

    const token = loginResponse.data.data.accessToken;
    console.log('✅ Login successful, token obtained');

    // Test getting subjects
    console.log('\n📚 Testing get subjects...');
    const subjectsResponse = await makeRequest('/subjects', 'GET', null, token);
    
    console.log('Subjects response status:', subjectsResponse.status);
    console.log('Subjects response structure:');
    console.log('- success:', subjectsResponse.data.success);
    console.log('- has data:', !!subjectsResponse.data.data);
    console.log('- has subjects:', !!subjectsResponse.data.data?.subjects);
    console.log('- subjects count:', subjectsResponse.data.data?.subjects?.length || 0);
    
    if (subjectsResponse.data.data?.subjects) {
      console.log('\n📋 Subjects found:');
      subjectsResponse.data.data.subjects.forEach((subject, index) => {
        console.log(`${index + 1}. ${subject.name} (${subject.code || 'No code'}) - School: ${subject.schoolId}`);
      });
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSubjectsAPI();
