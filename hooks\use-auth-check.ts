"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: string
  isEmailVerified: boolean
  institutions?: any[]
  schools?: any[]
}

interface AuthState {
  authenticated: boolean
  user: User | null
  sessionValid: boolean
  loading: boolean
  error: string | null
}

interface UseAuthCheckOptions {
  redirectTo?: string
  requiredRoles?: string[]
  requireEmailVerified?: boolean
}

export function useAuthCheck(options: UseAuthCheckOptions = {}) {
  const {
    redirectTo = "/auth/login",
    requiredRoles = [],
    requireEmailVerified = false
  } = options

  const router = useRouter()
  const [authState, setAuthState] = useState<AuthState>({
    authenticated: false,
    user: null,
    sessionValid: false,
    loading: true,
    error: null
  })

  const checkAuth = async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }))

      const response = await fetch("/api/auth/session", {
        method: "GET",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      })

      const data = await response.json()

      if (!response.ok || !data.authenticated) {
        setAuthState({
          authenticated: false,
          user: null,
          sessionValid: false,
          loading: false,
          error: data.error || "Authentication failed"
        })

        // Redirect to login if not authenticated
        if (redirectTo) {
          router.push(redirectTo)
        }
        return
      }

      const user = data.user

      // Check role requirements
      if (requiredRoles.length > 0 && !requiredRoles.includes(user.role)) {
        setAuthState({
          authenticated: false,
          user: null,
          sessionValid: false,
          loading: false,
          error: `Access denied. Required roles: ${requiredRoles.join(", ")}`
        })

        // Redirect to appropriate dashboard based on role
        const roleDashboards = {
          SUPER_ADMIN: "/super-admin",
          INSTITUTION_ADMIN: "/dashboard",
          SCHOOL_ADMIN: "/dashboard",
          TEACHER: "/dashboard",
          STUDENT: "/dashboard",
          PARENT: "/dashboard",
          STAFF: "/dashboard"
        }
        
        const userDashboard = roleDashboards[user.role as keyof typeof roleDashboards] || "/dashboard"
        router.push(userDashboard)
        return
      }

      // Check email verification requirement
      if (requireEmailVerified && !user.isEmailVerified) {
        setAuthState({
          authenticated: false,
          user: null,
          sessionValid: false,
          loading: false,
          error: "Email verification required"
        })

        router.push("/auth/verify-email")
        return
      }

      setAuthState({
        authenticated: true,
        user,
        sessionValid: data.sessionValid,
        loading: false,
        error: null
      })

    } catch (error) {
      console.error("Auth check error:", error)
      setAuthState({
        authenticated: false,
        user: null,
        sessionValid: false,
        loading: false,
        error: "Network error during authentication check"
      })

      if (redirectTo) {
        router.push(redirectTo)
      }
    }
  }

  useEffect(() => {
    checkAuth()
  }, [])

  const refetch = () => {
    checkAuth()
  }

  return {
    ...authState,
    refetch
  }
}

// Convenience hooks for specific roles
export function useAdminAuth() {
  return useAuthCheck({
    requiredRoles: ["SUPER_ADMIN", "INSTITUTION_ADMIN", "SCHOOL_ADMIN", "ADMIN"]
  })
}

export function useTeacherAuth() {
  return useAuthCheck({
    requiredRoles: ["SUPER_ADMIN", "INSTITUTION_ADMIN", "SCHOOL_ADMIN", "ADMIN", "TEACHER"]
  })
}

export function useSuperAdminAuth() {
  return useAuthCheck({
    requiredRoles: ["SUPER_ADMIN"],
    redirectTo: "/dashboard"
  })
}

export function useInstitutionAuth() {
  return useAuthCheck({
    requiredRoles: ["SUPER_ADMIN", "INSTITUTION_ADMIN", "SCHOOL_ADMIN", "ADMIN", "TEACHER", "STAFF"]
  })
}
