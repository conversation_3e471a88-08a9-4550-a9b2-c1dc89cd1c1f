const express = require('express')
const { body, param, query } = require('express-validator')
const router = express.Router()

const {
  getStudentFees,
  getClassFees,
  createFee,
  updateFee,
  recordPayment,
  getFeeStats,
  deleteFee,
  createBulkFees
} = require('./fees.controller')

const { authenticate, restrictTo } = require('../../../middleware/authenticate')
const { validateRequest } = require('../../../middleware/validateRequest')

// Validation middleware
const validateStudentId = [
  param('studentId').isInt({ min: 1 }).withMessage('Valid student ID is required')
]

const validateClassId = [
  param('classId').isInt({ min: 1 }).withMessage('Valid class ID is required')
]

const validateFeeId = [
  param('id').isInt({ min: 1 }).withMessage('Valid fee ID is required'),
]

const validatePaymentFeeId = [
  param('feeId').isInt({ min: 1 }).withMessage('Valid fee ID is required')
]

const validateCreateFee = [
  body('studentId').isInt({ min: 1 }).withMessage('Valid student ID is required'),
  body('type').isIn(['TUITION', 'EXAMINATION', 'LIBRARY', 'LABORATORY', 'TRANSPORT', 'HOSTEL', 'SPORTS', 'OTHER']).withMessage('Valid fee type is required'),
  body('amount').isFloat({ min: 0 }).withMessage('Valid amount is required'),
  body('dueDate').isISO8601().withMessage('Valid due date is required'),
  body('description').optional().isString().trim(),
  body('academicYear').optional().isString().trim(),
  body('term').optional().isString().trim()
]

const validateUpdateFee = [
  body('type').optional().isIn(['TUITION', 'EXAMINATION', 'LIBRARY', 'LABORATORY', 'TRANSPORT', 'HOSTEL', 'SPORTS', 'OTHER']).withMessage('Valid fee type is required'),
  body('amount').optional().isFloat({ min: 0 }).withMessage('Valid amount is required'),
  body('dueDate').optional().isISO8601().withMessage('Valid due date is required'),
  body('status').optional().isIn(['PENDING', 'PARTIAL', 'PAID', 'OVERDUE', 'WAIVED']).withMessage('Valid status is required'),
  body('description').optional().isString().trim(),
  body('academicYear').optional().isString().trim(),
  body('term').optional().isString().trim()
]

const validateRecordPayment = [
  body('amount').isFloat({ min: 0.01 }).withMessage('Valid payment amount is required'),
  body('paymentMethod').isIn(['CASH', 'CARD', 'BANK_TRANSFER', 'CHEQUE', 'UPI', 'OTHER']).withMessage('Valid payment method is required'),
  body('transactionId').optional().isString().trim(),
  body('paymentDate').optional().isISO8601().withMessage('Valid payment date is required'),
  body('remarks').optional().isString().trim()
]

const validateBulkFees = [
  body('classId').isInt({ min: 1 }).withMessage('Valid class ID is required'),
  body('type').isIn(['TUITION', 'EXAMINATION', 'LIBRARY', 'LABORATORY', 'TRANSPORT', 'HOSTEL', 'SPORTS', 'OTHER']).withMessage('Valid fee type is required'),
  body('amount').isFloat({ min: 0 }).withMessage('Valid amount is required'),
  body('dueDate').isISO8601().withMessage('Valid due date is required'),
  body('description').optional().isString().trim(),
  body('academicYear').optional().isString().trim(),
  body('term').optional().isString().trim()
]

const validateFeeQuery = [
  query('status').optional().isIn(['PENDING', 'PARTIAL', 'PAID', 'OVERDUE', 'WAIVED']).withMessage('Valid status is required'),
  query('feeType').optional().isIn(['TUITION', 'EXAMINATION', 'LIBRARY', 'LABORATORY', 'TRANSPORT', 'HOSTEL', 'SPORTS', 'OTHER']).withMessage('Valid fee type is required'),
  query('academicYear').optional().isString().trim()
]

// Routes

// GET /api/v1/fees/student/:studentId - Get all fees for a specific student
router.get(
  '/student/:studentId',
  authenticate,
  restrictTo(['ADMIN', 'TEACHER', 'PARENT', 'STUDENT']),
  validateStudentId,
  validateFeeQuery,
  validateRequest,
  getStudentFees
)

// GET /api/v1/fees/class/:classId - Get all fees for a specific class
router.get(
  '/class/:classId',
  authenticate,
  restrictTo(['ADMIN', 'TEACHER']),
  validateClassId,
  validateFeeQuery,
  validateRequest,
  getClassFees
)

// GET /api/v1/fees/stats - Get fee statistics
router.get(
  '/stats',
  authenticate,
  restrictTo(['ADMIN', 'TEACHER']),
  query('classId').optional().isInt({ min: 1 }).withMessage('Valid class ID is required'),
  query('academicYear').optional().isString().trim(),
  validateRequest,
  getFeeStats
)

// POST /api/v1/fees - Create a new fee record
router.post(
  '/',
  authenticate,
  restrictTo(['ADMIN']),
  validateCreateFee,
  validateRequest,
  createFee
)

// POST /api/v1/fees/bulk - Create bulk fees for a class
router.post(
  '/bulk',
  authenticate,
  restrictTo(['ADMIN']),
  validateBulkFees,
  validateRequest,
  createBulkFees
)

// POST /api/v1/fees/:feeId/payment - Record a payment for a fee
router.post(
  '/:feeId/payment',
  authenticate,
  restrictTo(['ADMIN', 'TEACHER']),
  validatePaymentFeeId,
  validateRecordPayment,
  validateRequest,
  recordPayment
)

// PUT /api/v1/fees/:id - Update fee record
router.put(
  '/:id',
  authenticate,
  restrictTo(['ADMIN']),
  validateFeeId,
  validateUpdateFee,
  validateRequest,
  updateFee
)

// DELETE /api/v1/fees/:id - Delete fee record
router.delete(
  '/:id',
  authenticate,
  restrictTo(['ADMIN']),
  validateFeeId,
  validateRequest,
  deleteFee
)

module.exports = router
