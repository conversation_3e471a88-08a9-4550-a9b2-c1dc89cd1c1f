const nodemailer = require('nodemailer');
const { logger } = require('../utils/logger');
const config = require('../config');

/**
 * Configure email transporter
 */
const createTransporter = () => {
  // Gmail-specific configuration
  const gmailConfig = {
    service: 'gmail', // Use Gmail service
    host: config.email.host,
    port: config.email.port,
    secure: false, // true for 465, false for other ports like 587
    auth: {
      user: config.email.user,
      pass: config.email.password, // App password for Gmail
    },
    tls: {
      rejectUnauthorized: false // Allow self-signed certificates
    }
  };

  // For production, use the same Gmail config
  if (process.env.NODE_ENV === 'production') {
    return nodemailer.createTransport(gmailConfig);
  }

  // For development/testing, use Gmail config
  return nodemailer.createTransport(gmailConfig);
};

/**
 * Send email
 * @param {Object} options - Email options
 */
const sendEmail = async (options) => {
  try {
    const transporter = createTransporter();

    // Test the connection first
    await transporter.verify();
    logger.info('Email transporter is ready to send emails');

    const mailOptions = {
      from: `${config.app_name} <${config.email.from}>`,
      to: options.email || options.to,
      subject: options.subject,
      html: options.html,
    };

    const info = await transporter.sendMail(mailOptions);
    
    if (process.env.NODE_ENV === 'development') {
      logger.info(`Email sent successfully to ${options.email || options.to}`);
      logger.info(`Message ID: ${info.messageId}`);
    }

    return info;
  } catch (error) {
    logger.error('Email sending failed:', error);
    
    // Provide more specific error messages for common Gmail issues
    if (error.code === 'EAUTH') {
      logger.error('Gmail authentication failed. Please check your email credentials and ensure you are using an App Password.');
    } else if (error.code === 'ENOTFOUND') {
      logger.error('Gmail SMTP host not found. Please check your network connection.');
    } else if (error.responseCode === 535) {
      logger.error('Gmail authentication failed. Please ensure you have enabled 2FA and are using an App Password.');
    }
    
    throw error;
  }
};

/**
 * Send verification email
 * @param {string} email - User's email
 * @param {string} token - Verification token
 */
const sendVerificationEmail = async (email, token) => {
  const verificationUrl = `${config.frontend_url}/auth/verify-email?token=${token}`;

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <h2>Verify Your Email Address</h2>
      <p>Thank you for registering with ${config.app_name}. Please click the button below to verify your email address:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${verificationUrl}" style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
          Verify Email Address
        </a>
      </div>
      <p>If you did not create an account, please ignore this email.</p>
      <p>This link will expire in 24 hours.</p>
      <p>If the button above doesn't work, you can also copy and paste the following link into your browser:</p>
      <p>${verificationUrl}</p>
      <p>Thank you,<br>The EduSync Team</p>
    </div>
  `;

  await sendEmail({
    email,
    subject: 'EduSync - Verify Your Email Address',
    html,
  });
};

/**
 * Send password reset email
 * @param {string} email - User's email
 * @param {string} token - Password reset token
 */
const sendPasswordResetEmail = async (email, token) => {
  const resetUrl = `${config.frontend_url}/auth/reset-password?token=${token}`;

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <h2>Reset Your Password</h2>
      <p>You requested a password reset for your ${config.app_name} account. Please click the button below to reset your password:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${resetUrl}" style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
          Reset Password
        </a>
      </div>
      <p>If you did not request a password reset, please ignore this email and your password will remain unchanged.</p>
      <p>This link will expire in 1 hour.</p>
      <p>If the button above doesn't work, you can also copy and paste the following link into your browser:</p>
      <p>${resetUrl}</p>
      <p>Thank you,<br>The EduSync Team</p>
    </div>
  `;

  await sendEmail({
    email,
    subject: 'EduSync - Reset Your Password',
    html,
  });
};

/**
 * Send welcome email
 * @param {string} email - User's email
 * @param {string} firstName - User's first name
 */
const sendWelcomeEmail = async (email, firstName) => {
  const loginUrl = `${config.frontend_url}/auth/login`;

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <h2>Welcome to ${config.app_name}!</h2>
      <p>Hello ${firstName},</p>
      <p>Thank you for joining ${config.app_name}. We're excited to have you on board!</p>
      <p>With EduSync, you can:</p>
      <ul>
        <li>Manage your school or institution efficiently</li>
        <li>Track student progress and attendance</li>
        <li>Streamline administrative tasks</li>
        <li>Enhance communication between teachers, parents, and students</li>
      </ul>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${loginUrl}" style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
          Log In to Your Account
        </a>
      </div>
      <p>If you have any questions or need assistance, please don't hesitate to contact our support <NAME_EMAIL>.</p>
      <p>Thank you,<br>The EduSync Team</p>
    </div>
  `;

  await sendEmail({
    email,
    subject: 'Welcome to EduSync!',
    html,
  });
};

/**
 * Test email configuration
 * @param {string} testEmail - Email address to send test email to
 */
const testEmailConfiguration = async (testEmail = config.email.user) => {
  try {
    const transporter = createTransporter();
    
    // Verify connection configuration
    await transporter.verify();
    logger.info('✅ Email server connection verified successfully');
    
    // Send a test email
    const testMailOptions = {
      from: `${config.app_name} <${config.email.from}>`,
      to: testEmail,
      subject: 'EduSync - Email Configuration Test',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2>Email Configuration Test</h2>
          <p>This is a test email to verify that your EduSync email configuration is working correctly.</p>
          <p>If you received this email, your email service is properly configured!</p>
          <p>Configuration details:</p>
          <ul>
            <li>Host: ${config.email.host}</li>
            <li>Port: ${config.email.port}</li>
            <li>From: ${config.email.from}</li>
          </ul>
          <p>Best regards,<br>The EduSync Team</p>
        </div>
      `,
    };
    
    const info = await transporter.sendMail(testMailOptions);
    logger.info(`✅ Test email sent successfully to ${testEmail}`);
    logger.info(`Message ID: ${info.messageId}`);
    
    return { success: true, messageId: info.messageId };
  } catch (error) {
    logger.error('❌ Email configuration test failed:', error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  sendEmail,
  createTransporter,
  sendWelcomeEmail,
  sendVerificationEmail,
  sendPasswordResetEmail,
  testEmailConfiguration
};
