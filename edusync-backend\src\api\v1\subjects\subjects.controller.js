const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Get all subjects with filtering and pagination
 */
const getSubjects = async (req, res) => {
  try {
    console.log('🔍 Getting subjects for user:', req.user.id, 'role:', req.user.role);
    console.log('  - User schoolId:', req.user.schoolId);
    console.log('  - User institutionId:', req.user.institutionId);

    const {
      page = 1,
      limit = 10,
      search = '',
      department = '',
      gradeLevel = '',
      isActive = 'true',
      schoolId
    } = req.query;

    // Convert isActive string to boolean
    const isActiveBoolean = isActive === 'true' || isActive === true;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Determine the final schoolId to use for filtering
    let finalSchoolId = schoolId || req.user.schoolId;

    console.log('  - Initial finalSchoolId:', finalSchoolId);

    // If no schoolId, try to determine from user context
    if (!finalSchoolId) {
      console.log('  - No schoolId provided, determining from user context');

      // First try to use the schools from the enhanced user object
      if (req.user.schools && req.user.schools.length > 0) {
        finalSchoolId = req.user.schools[0].schoolId;
        console.log('  - Using school ID from user context:', finalSchoolId);
      } else if (req.user.institutionId) {
        // If user has institution but no school, get the first school from that institution
        console.log('  - No direct school association, looking up schools in institution:', req.user.institutionId);

        const institutionSchools = await prisma.school.findMany({
          where: {
            institutionId: req.user.institutionId,
            isActive: true
          },
          select: { id: true, name: true },
          take: 1
        });

        if (institutionSchools.length > 0) {
          finalSchoolId = institutionSchools[0].id;
          console.log('  - Using first school from institution:', finalSchoolId);
        }
      }
    }

    console.log('  - Final schoolId for filtering:', finalSchoolId);

    // Build where conditions
    const where = {
      schoolId: finalSchoolId,
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { code: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }),
      ...(department && {
        department: {
          name: { contains: department, mode: 'insensitive' }
        }
      }),
      ...(isActive !== undefined && { isActive: isActiveBoolean })
    };

    const [subjects, totalCount] = await Promise.all([
      prisma.subject.findMany({
        where,
        skip,
        take,
        include: {
          department: {
            select: {
              id: true,
              name: true
            }
          },
          school: {
            select: {
              id: true,
              name: true
            }
          },
          classes: {
            include: {
              class: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          teacherAssignments: {
            where: {
              isActive: true
            },
            include: {
              teacher: {
                select: {
                  id: true,
                  user: {
                    select: {
                      firstName: true,
                      lastName: true
                    }
                  }
                }
              }
            }
          },
          _count: {
            select: {
              classes: true,
              teacherAssignments: {
                where: {
                  isActive: true
                }
              },
              attendanceRecords: true,
              gradeRecords: true
            }
          }
        },
        orderBy: [
          { name: 'asc' }
        ]
      }),
      prisma.subject.count({ where })
    ]);

    console.log('  - Found subjects:', subjects.length);
    console.log('  - Total count:', totalCount);
    console.log('  - Where conditions:', JSON.stringify(where, null, 2));

    // Transform data to match frontend expectations
    const transformedSubjects = subjects.map(subject => {
      console.log(`🔍 Subject ${subject.name}: ${subject._count.teacherAssignments} teacher assignments`);
      return {
        id: subject.id,
        name: subject.name,
        code: subject.code,
        description: subject.description,
        credits: subject.credits,
        isCore: subject.isCore,
        isActive: subject.isActive,
        department: subject.department,
        departmentId: subject.departmentId,
        school: subject.school,
        teacherAssignments: subject.teacherAssignments,
        _count: {
          classes: subject._count.classes,
          teacherAssignments: subject._count.teacherAssignments,
          attendanceRecords: subject._count.attendanceRecords,
          gradeRecords: subject._count.gradeRecords
        },
        // Legacy fields for backward compatibility
        classes: subject._count.classes,
        teachers: subject._count.teacherAssignments,
        status: subject.isActive ? 'Active' : 'Inactive',
        createdAt: subject.createdAt,
        updatedAt: subject.updatedAt
      };
    });

    const totalPages = Math.ceil(totalCount / take);

    res.status(200).json({
      success: true,
      data: {
        subjects: transformedSubjects,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });
  } catch (error) {
    console.error('Get subjects error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch subjects',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

/**
 * Get subject by ID
 */
const getSubjectById = async (req, res) => {
  try {
    const { id } = req.params;

    const subject = await prisma.subject.findUnique({
      where: { id },
      include: {
        department: {
          select: {
            id: true,
            name: true
          }
        },
        school: {
          select: {
            id: true,
            name: true
          }
        },
        classes: {
          include: {
            class: {
              select: {
                id: true,
                name: true,
                gradeLevel: true
              }
            }
          }
        },
        teacherAssignments: {
          include: {
            teacher: {
              select: {
                id: true,
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    email: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!subject) {
      return res.status(404).json({
        success: false,
        message: 'Subject not found'
      });
    }

    // Transform data
    const transformedSubject = {
      id: subject.id,
      name: subject.name,
      code: subject.code,
      description: subject.description,
      credits: subject.credits,
      isCore: subject.isCore,
      isActive: subject.isActive,
      department: subject.department,
      school: subject.school,
      classes: subject.classes.map(cs => cs.class),
      teachers: subject.teacherAssignments.map(ta => ({
        id: ta.teacher.id,
        name: `${ta.teacher.user.firstName} ${ta.teacher.user.lastName}`,
        email: ta.teacher.user.email
      })),
      createdAt: subject.createdAt,
      updatedAt: subject.updatedAt
    };

    res.status(200).json({
      success: true,
      data: transformedSubject
    });
  } catch (error) {
    console.error('Get subject by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch subject',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

/**
 * Create new subject
 */
const createSubject = async (req, res) => {
  try {
    console.log('🔍 Creating subject for user:', req.user.id, 'role:', req.user.role);
    console.log('  - User schoolId:', req.user.schoolId);
    console.log('  - User institutionId:', req.user.institutionId);
    console.log('  - Request body:', req.body);

    const {
      name,
      code,
      description,
      credits,
      isCore = true,
      isActive = true,
      departmentId,
      schoolId
    } = req.body;

    // Use schoolId from request body or user context
    let finalSchoolId = schoolId || req.user.schoolId;

    console.log('  - Initial finalSchoolId:', finalSchoolId);

    // If no schoolId, try to determine from user context
    if (!finalSchoolId) {
      console.log('  - No schoolId provided, determining from user context');

      // First try to use the schools from the enhanced user object
      if (req.user.schools && req.user.schools.length > 0) {
        finalSchoolId = req.user.schools[0].schoolId;
        console.log('  - Using school ID from user context:', finalSchoolId);
      } else if (req.user.institutionId) {
        // If user has institution but no school, get the first school from that institution
        console.log('  - No direct school association, looking up schools in institution:', req.user.institutionId);

        const institutionSchools = await prisma.school.findMany({
          where: {
            institutionId: req.user.institutionId,
            isActive: true
          },
          select: { id: true, name: true },
          take: 1
        });

        if (institutionSchools.length > 0) {
          finalSchoolId = institutionSchools[0].id;
          console.log('  - Using first school from institution:', finalSchoolId);
        }
      }
    }

    if (!finalSchoolId) {
      console.log('❌ No school ID could be determined');
      return res.status(400).json({
        success: false,
        message: 'School ID is required and could not be determined from user context'
      });
    }

    // Check if subject with same code exists in the school
    if (code) {
      const existingSubject = await prisma.subject.findFirst({
        where: {
          code,
          schoolId: finalSchoolId
        }
      });

      if (existingSubject) {
        return res.status(400).json({
          success: false,
          message: 'Subject with this code already exists in the school'
        });
      }
    }

    const subject = await prisma.subject.create({
      data: {
        name,
        code,
        description,
        credits: credits ? parseInt(credits) : null,
        isCore,
        isActive,
        schoolId: finalSchoolId,
        departmentId: departmentId && departmentId.trim() !== '' ? departmentId : null
      },
      include: {
        department: {
          select: {
            id: true,
            name: true
          }
        },
        school: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'Subject created successfully',
      data: subject
    });
  } catch (error) {
    console.error('Create subject error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create subject',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

/**
 * Update subject
 */
const updateSubject = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      code,
      description,
      credits,
      isCore,
      isActive,
      departmentId
    } = req.body;

    // Check if subject exists
    const existingSubject = await prisma.subject.findUnique({
      where: { id }
    });

    if (!existingSubject) {
      return res.status(404).json({
        success: false,
        message: 'Subject not found'
      });
    }

    // Check if subject code conflicts with another subject in the same school
    if (code && code !== existingSubject.code) {
      const conflictingSubject = await prisma.subject.findFirst({
        where: {
          code,
          schoolId: existingSubject.schoolId,
          id: { not: id }
        }
      });

      if (conflictingSubject) {
        return res.status(400).json({
          success: false,
          message: 'Subject with this code already exists in the school'
        });
      }
    }

    const subject = await prisma.subject.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(code !== undefined && { code }),
        ...(description !== undefined && { description }),
        ...(credits !== undefined && { credits: credits ? parseInt(credits) : null }),
        ...(isCore !== undefined && { isCore }),
        ...(isActive !== undefined && { isActive }),
        ...(departmentId !== undefined && { departmentId: departmentId && departmentId.trim() !== '' ? departmentId : null })
      },
      include: {
        department: {
          select: {
            id: true,
            name: true
          }
        },
        school: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    res.status(200).json({
      success: true,
      message: 'Subject updated successfully',
      data: subject
    });
  } catch (error) {
    console.error('Update subject error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update subject',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

/**
 * Delete subject
 */
const deleteSubject = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if subject exists
    const existingSubject = await prisma.subject.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            classes: true,
            teacherAssignments: true,
            attendanceRecords: true,
            gradeRecords: true
          }
        }
      }
    });

    if (!existingSubject) {
      return res.status(404).json({
        success: false,
        message: 'Subject not found'
      });
    }

    // Check if subject has dependencies
    const hasDepencies = existingSubject._count.classes > 0 ||
                        existingSubject._count.teacherAssignments > 0 ||
                        existingSubject._count.attendanceRecords > 0 ||
                        existingSubject._count.gradeRecords > 0;

    if (hasDepencies) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete subject. It has associated classes, teachers, attendance, or grade records.'
      });
    }

    await prisma.subject.delete({
      where: { id }
    });

    res.status(200).json({
      success: true,
      message: 'Subject deleted successfully'
    });
  } catch (error) {
    console.error('Delete subject error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete subject',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

/**
 * Get subjects statistics
 */
const getSubjectsStats = async (req, res) => {
  try {
    const { schoolId } = req.query;
    const finalSchoolId = schoolId || req.user.schoolId;

    const [
      totalSubjects,
      activeSubjects,
      coreSubjects,
      totalDepartments,
      subjectsByDepartment
    ] = await Promise.all([
      prisma.subject.count({
        where: { schoolId: finalSchoolId }
      }),
      prisma.subject.count({
        where: { 
          schoolId: finalSchoolId,
          isActive: true 
        }
      }),
      prisma.subject.count({
        where: { 
          schoolId: finalSchoolId,
          isCore: true,
          isActive: true
        }
      }),
      prisma.department.count({
        where: { schoolId: finalSchoolId }
      }),
      prisma.subject.groupBy({
        by: ['departmentId'],
        where: { 
          schoolId: finalSchoolId,
          isActive: true 
        },
        _count: {
          id: true
        }
      })
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalSubjects,
        activeSubjects,
        inactiveSubjects: totalSubjects - activeSubjects,
        coreSubjects,
        electiveSubjects: activeSubjects - coreSubjects,
        totalDepartments,
        averageSubjectsPerDepartment: totalDepartments > 0 ? Math.round(activeSubjects / totalDepartments) : 0
      }
    });
  } catch (error) {
    console.error('Get subjects stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch subjects statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

/**
 * Get teacher assignments for a specific subject
 */
const getSubjectAssignments = async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🔍 Getting assignments for subject:', id);

    // Check if subject exists
    const subject = await prisma.subject.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        code: true,
        school: {
          select: {
            id: true,
            institutionId: true
          }
        }
      }
    });

    if (!subject) {
      return res.status(404).json({
        success: false,
        message: 'Subject not found'
      });
    }

    // Get teacher assignments for this subject
    const assignments = await prisma.teacherAssignment.findMany({
      where: {
        subjectId: id,
        isActive: true
      },
      include: {
        teacher: {
          select: {
            id: true,
            specialization: true,
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        }
      },
      orderBy: [
        { teacher: { user: { firstName: 'asc' } } },
        { teacher: { user: { lastName: 'asc' } } }
      ]
    });

    console.log('🔍 Found assignments:', assignments.length);

    res.json({
      success: true,
      data: {
        subject: {
          id: subject.id,
          name: subject.name,
          code: subject.code
        },
        assignments
      }
    });
  } catch (error) {
    console.error('Error fetching subject assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch subject assignments',
      error: error.message
    });
  }
};

module.exports = {
  getSubjects,
  getSubjectById,
  createSubject,
  updateSubject,
  deleteSubject,
  getSubjectsStats,
  getSubjectAssignments
};