const { PrismaClient } = require('@prisma/client');
const asyncHandler = require('../../../utils/asyncHandler');

const prisma = new PrismaClient();

/**
 * Get all features
 */
const getFeatures = asyncHandler(async (req, res) => {
  const features = await prisma.feature.findMany({
    orderBy: [
      { orderIndex: 'asc' },
      { createdAt: 'desc' }
    ]
  });

  res.status(200).json({
    success: true,
    data: features
  });
});

/**
 * Create feature
 */
const createFeature = asyncHandler(async (req, res) => {
  const {
    title,
    description,
    icon,
    benefits,
    category,
    isActive = true,
    orderIndex = 0
  } = req.body;

  const feature = await prisma.feature.create({
    data: {
      title,
      description,
      icon,
      benefits: benefits || [],
      category,
      isActive,
      orderIndex: parseInt(orderIndex)
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Feature Created',
      details: `New feature created: ${title}`,
      category: 'Content Management',
      severity: 'Info',
      entityType: 'Feature',
      entityId: feature.id,
      entityName: title,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(201).json({
    success: true,
    message: 'Feature created successfully',
    data: feature
  });
});

/**
 * Update feature
 */
const updateFeature = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    title,
    description,
    icon,
    benefits,
    category,
    is_active,
    order_index
  } = req.body;

  const feature = await prisma.feature.findUnique({
    where: { id: parseInt(id) }
  });

  if (!feature) {
    return res.status(404).json({
      success: false,
      message: 'Feature not found'
    });
  }

  const updatedFeature = await prisma.feature.update({
    where: { id: parseInt(id) },
    data: {
      title,
      description,
      icon,
      benefits: benefits || [],
      category,
      isActive: is_active !== undefined ? is_active : feature.isActive,
      orderIndex: order_index !== undefined ? parseInt(order_index) : feature.orderIndex
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Feature Updated',
      details: `Feature updated: ${title}`,
      category: 'Content Management',
      severity: 'Info',
      entityType: 'Feature',
      entityId: feature.id,
      entityName: title,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Feature updated successfully',
    data: updatedFeature
  });
});

/**
 * Delete feature
 */
const deleteFeature = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const feature = await prisma.feature.findUnique({
    where: { id }
  });

  if (!feature) {
    return res.status(404).json({
      success: false,
      message: 'Feature not found'
    });
  }

  await prisma.feature.delete({
    where: { id }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Feature Deleted',
      details: `Feature deleted: ${feature.title}`,
      category: 'Content Management',
      severity: 'Info',
      entityType: 'Feature',
      entityId: feature.id,
      entityName: feature.title,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Feature deleted successfully'
  });
});

/**
 * Get all pricing plans
 */
const getPricingPlans = asyncHandler(async (req, res) => {
  const plans = await prisma.pricingPlan.findMany({
    orderBy: [
      { orderIndex: 'asc' },
      { createdAt: 'desc' }
    ]
  });

  res.status(200).json({
    success: true,
    data: plans
  });
});

/**
 * Create pricing plan
 */
const createPricingPlan = asyncHandler(async (req, res) => {
  const {
    name,
    description,
    price,
    billing,
    features,
    storage,
    isPopular = false,
    isActive = true,
    orderIndex = 0
  } = req.body;

  const plan = await prisma.pricingPlan.create({
    data: {
      name,
      description,
      price,
      billing,
      features: features || [],
      storage,
      isPopular,
      isActive,
      orderIndex: parseInt(orderIndex)
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Pricing Plan Created',
      details: `New pricing plan created: ${name}`,
      category: 'Content Management',
      severity: 'Info',
      entityType: 'PricingPlan',
      entityId: plan.id,
      entityName: name,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(201).json({
    success: true,
    message: 'Pricing plan created successfully',
    data: plan
  });
});

/**
 * Update pricing plan
 */
const updatePricingPlan = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    name,
    description,
    price,
    billing,
    features,
    storage,
    isPopular,
    isActive,
    orderIndex
  } = req.body;

  const plan = await prisma.pricingPlan.findUnique({
    where: { id }
  });

  if (!plan) {
    return res.status(404).json({
      success: false,
      message: 'Pricing plan not found'
    });
  }

  const updatedPlan = await prisma.pricingPlan.update({
    where: { id },
    data: {
      name,
      description,
      price,
      billing,
      features: features || [],
      storage,
      isPopular,
      isActive,
      orderIndex: orderIndex ? parseInt(orderIndex) : plan.orderIndex
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Pricing Plan Updated',
      details: `Pricing plan updated: ${name}`,
      category: 'Content Management',
      severity: 'Info',
      entityType: 'PricingPlan',
      entityId: plan.id,
      entityName: name,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Pricing plan updated successfully',
    data: updatedPlan
  });
});

/**
 * Delete pricing plan
 */
const deletePricingPlan = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const plan = await prisma.pricingPlan.findUnique({
    where: { id }
  });

  if (!plan) {
    return res.status(404).json({
      success: false,
      message: 'Pricing plan not found'
    });
  }

  await prisma.pricingPlan.delete({
    where: { id }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Pricing Plan Deleted',
      details: `Pricing plan deleted: ${plan.name}`,
      category: 'Content Management',
      severity: 'Info',
      entityType: 'PricingPlan',
      entityId: plan.id,
      entityName: plan.name,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Pricing plan deleted successfully'
  });
});

/**
 * Get all testimonials
 */
const getTestimonials = asyncHandler(async (req, res) => {
  const testimonials = await prisma.testimonial.findMany({
    orderBy: [
      { orderIndex: 'asc' },
      { createdAt: 'desc' }
    ]
  });

  res.status(200).json({
    success: true,
    data: testimonials
  });
});

/**
 * Create testimonial
 */
const createTestimonial = asyncHandler(async (req, res) => {
  const {
    name,
    role,
    institution,
    content,
    rating,
    avatar,
    isActive = true,
    orderIndex = 0
  } = req.body;

  const testimonial = await prisma.testimonial.create({
    data: {
      name,
      role,
      institution,
      content,
      rating: parseInt(rating),
      avatar,
      isActive,
      orderIndex: parseInt(orderIndex)
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Testimonial Created',
      details: `New testimonial created from: ${name}`,
      category: 'Content Management',
      severity: 'Info',
      entityType: 'Testimonial',
      entityId: testimonial.id,
      entityName: name,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(201).json({
    success: true,
    message: 'Testimonial created successfully',
    data: testimonial
  });
});

/**
 * Update testimonial
 */
const updateTestimonial = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    name,
    role,
    institution,
    content,
    rating,
    avatar,
    isActive,
    orderIndex
  } = req.body;

  const testimonial = await prisma.testimonial.findUnique({
    where: { id }
  });

  if (!testimonial) {
    return res.status(404).json({
      success: false,
      message: 'Testimonial not found'
    });
  }

  const updatedTestimonial = await prisma.testimonial.update({
    where: { id },
    data: {
      name,
      role,
      institution,
      content,
      rating: rating ? parseInt(rating) : testimonial.rating,
      avatar,
      isActive,
      orderIndex: orderIndex ? parseInt(orderIndex) : testimonial.orderIndex
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Testimonial Updated',
      details: `Testimonial updated: ${name}`,
      category: 'Content Management',
      severity: 'Info',
      entityType: 'Testimonial',
      entityId: testimonial.id,
      entityName: name,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Testimonial updated successfully',
    data: updatedTestimonial
  });
});

/**
 * Delete testimonial
 */
const deleteTestimonial = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const testimonial = await prisma.testimonial.findUnique({
    where: { id }
  });

  if (!testimonial) {
    return res.status(404).json({
      success: false,
      message: 'Testimonial not found'
    });
  }

  await prisma.testimonial.delete({
    where: { id }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Testimonial Deleted',
      details: `Testimonial deleted: ${testimonial.name}`,
      category: 'Content Management',
      severity: 'Info',
      entityType: 'Testimonial',
      entityId: testimonial.id,
      entityName: testimonial.name,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Testimonial deleted successfully'
  });
});

module.exports = {
  // Features
  getFeatures,
  createFeature,
  updateFeature,
  deleteFeature,
  // Pricing
  getPricingPlans,
  createPricingPlan,
  updatePricingPlan,
  deletePricingPlan,
  // Testimonials
  getTestimonials,
  createTestimonial,
  updateTestimonial,
  deleteTestimonial
};
