import { NextRequest, NextResponse } from "next/server"
import { authenticateUser, createAuthHead<PERSON> } from "@/lib/auth"

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request)
    
    if (!auth.user) {
      return NextResponse.json({ error: auth.error }, { status: auth.status })
    }

    const { searchParams } = new URL(request.url)
    const page = searchParams.get("page") || "1"
    const limit = searchParams.get("limit") || "10"
    const search = searchParams.get("search") || ""
    const classId = searchParams.get("classId") || ""
    const status = searchParams.get("status") || ""

    // Build query parameters
    const queryParams = new URLSearchParams({
      page,
      limit,
      ...(search && { search }),
      ...(classId && { classId }),
      ...(status && { status })
    })

    // Forward the request to our backend API
    const response = await fetch(`${BACKEND_URL}/api/v1/students?${queryParams}`, {
      headers: await createAuthHeaders(request),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      return NextResponse.json({ 
        error: errorData.message || "Failed to fetch students" 
      }, { status: response.status })
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Students API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request)
    
    if (!auth.user) {
      return NextResponse.json({ error: auth.error }, { status: auth.status })
    }

    const body = await request.json()

    // Forward the request to our backend API
    const response = await fetch(`${BACKEND_URL}/api/v1/students`, {
      method: "POST",
      headers: await createAuthHeaders(request),
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json({ 
        error: data.message || "Failed to create student" 
      }, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Create student API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
