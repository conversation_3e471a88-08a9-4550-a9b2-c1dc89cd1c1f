/**
 * Enhanced API client with automatic token refresh and consistent error handling
 */

interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  status: number
}

class EnhancedApiClient {
  private baseUrl: string
  private isRefreshing = false
  private failedQueue: Array<{
    resolve: (value: any) => void
    reject: (error: any) => void
  }> = []

  constructor(baseUrl = '') {
    this.baseUrl = baseUrl
  }

  private async processQueue(error: any, token: string | null = null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error)
      } else {
        resolve(token)
      }
    })
    
    this.failedQueue = []
  }

  private async refreshToken(): Promise<boolean> {
    try {
      console.log("🔄 EnhancedApiClient: Attempting token refresh...")
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      })

      if (response.ok) {
        console.log("✅ EnhancedApiClient: Token refresh successful")
        return true
      } else {
        console.log("❌ EnhancedApiClient: Token refresh failed")
        return false
      }
    } catch (error) {
      console.error("❌ EnhancedApiClient: Token refresh error:", error)
      return false
    }
  }

  async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`
    
    const config: RequestInit = {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    try {
      let response = await fetch(url, config)

      // If we get a 401, try to refresh the token
      if (response.status === 401) {
        if (this.isRefreshing) {
          // If already refreshing, wait for it to complete
          return new Promise((resolve, reject) => {
            this.failedQueue.push({ resolve, reject })
          }).then(async () => {
            // Retry the original request
            const retryResponse = await fetch(url, config)
            const data = retryResponse.ok ? await retryResponse.json() : undefined
            return {
              success: retryResponse.ok,
              data: data?.data || data,
              status: retryResponse.status,
              error: retryResponse.ok ? undefined : data?.error || 'Request failed'
            }
          }).catch((error) => ({
            success: false,
            status: 401,
            error: 'Authentication failed'
          }))
        }

        this.isRefreshing = true

        try {
          const refreshSuccess = await this.refreshToken()
          
          if (refreshSuccess) {
            this.processQueue(null, 'refreshed')
            
            // Retry the original request
            response = await fetch(url, config)
          } else {
            this.processQueue(new Error('Token refresh failed'), null)
            // Redirect to login
            if (typeof window !== 'undefined') {
              window.location.href = '/auth/login'
            }
            return { success: false, status: 401, error: 'Authentication failed' }
          }
        } catch (refreshError) {
          this.processQueue(refreshError, null)
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login'
          }
          return { success: false, status: 401, error: 'Authentication failed' }
        } finally {
          this.isRefreshing = false
        }
      }

      const data = response.ok ? await response.json() : undefined
      
      return {
        success: response.ok,
        data: data?.data || data,
        status: response.status,
        error: response.ok ? undefined : data?.error || data?.message || 'Request failed'
      }
    } catch (error) {
      console.error('API request error:', error)
      return {
        success: false,
        status: 500,
        error: 'Network error'
      }
    }
  }

  // Convenience methods
  async get<T = any>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const url = new URL(endpoint, this.baseUrl || window.location.origin)
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value))
        }
      })
    }
    
    return this.request<T>(url.pathname + url.search)
  }

  async post<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    })
  }
}

// Create a singleton instance
export const enhancedApiClient = new EnhancedApiClient()

// Export the class for custom instances
export { EnhancedApiClient }
export type { ApiResponse }
