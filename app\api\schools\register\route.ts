import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()

    // Extract form data
    const schoolData = {
      // Institution Details
      institutionName: formData.get("institutionName") as string,
      institutionType: formData.get("institutionType") as string,
      address: formData.get("address") as string,
      city: formData.get("city") as string,
      state: formData.get("state") as string,
      country: formData.get("country") as string,
      postalCode: formData.get("postalCode") as string,
      phone: formData.get("phone") as string,
      institutionEmail: formData.get("institutionEmail") as string,
      website: formData.get("website") as string,

      // School Details
      schoolName: formData.get("schoolName") as string,
      schoolType: formData.get("schoolType") as string,
      schoolAddress: formData.get("schoolAddress") as string,
      schoolCity: formData.get("schoolCity") as string,
      schoolState: formData.get("schoolState") as string,
      schoolCountry: formData.get("schoolCountry") as string,
      schoolPostalCode: formData.get("schoolPostalCode") as string,
      schoolPhone: formData.get("schoolPhone") as string,
      schoolEmail: formData.get("schoolEmail") as string,
      schoolWebsite: formData.get("schoolWebsite") as string,

      // Admin Details
      adminFirstName: formData.get("adminFirstName") as string,
      adminLastName: formData.get("adminLastName") as string,
      adminEmail: formData.get("adminEmail") as string,
      adminPhone: formData.get("adminPhone") as string,
      adminPassword: formData.get("adminPassword") as string,

      // Additional Info
      studentCount: formData.get("studentCount") as string,
      teacherCount: formData.get("teacherCount") as string,
      subscriptionPlan: formData.get("subscriptionPlan") as string,
      referralSource: formData.get("referralSource") as string,
      specialRequirements: formData.get("specialRequirements") as string,
    }

    // Handle logo file if present
    const logoFile = formData.get("logo") as File | null

    // Create FormData for backend
    const backendFormData = new FormData()

    // Add all fields to FormData
    Object.entries(schoolData).forEach(([key, value]) => {
      if (value) {
        backendFormData.append(key, value)
      }
    })

    // Add logo file if present
    if (logoFile && logoFile.size > 0) {
      backendFormData.append("logo", logoFile)
    }

    // Get the backend URL from environment variables
    const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

    // Forward to Node.js backend
    const response = await fetch(`${BACKEND_URL}/api/v1/institutions/register`, {
      method: "POST",
      body: backendFormData,
    })

    const result = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: result.error || "School registration failed" }, { status: response.status })
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error("School registration API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
