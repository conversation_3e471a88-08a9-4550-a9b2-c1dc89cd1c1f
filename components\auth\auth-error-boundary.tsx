"use client"

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>ertTriangle, RefreshCw } from "lucide-react"

interface AuthErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface AuthErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>
}

export class AuthErrorBoundary extends React.Component<
  AuthErrorBoundaryProps,
  AuthErrorBoundaryState
> {
  constructor(props: AuthErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): AuthErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Auth Error Boundary caught an error:", error, errorInfo)

    // Check if it's an authentication-related error or data structure error
    const isAuthError =
      error.message.includes('401') ||
      error.message.includes('Unauthorized') ||
      error.message.includes('Authentication') ||
      error.message.includes('Token')

    // Check if it's a data structure error (like .map is not a function)
    const isDataError =
      error.message.includes('.map is not a function') ||
      error.message.includes('.filter is not a function') ||
      error.message.includes('.forEach is not a function') ||
      error.message.includes('Cannot read properties of undefined')

    if (isAuthError || isDataError) {
      // Clear any stored auth data for auth errors
      if (isAuthError && typeof window !== 'undefined') {
        localStorage.removeItem('user')
        localStorage.removeItem('token')
        // Clear cookies by setting them to expire
        document.cookie = 'accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
        document.cookie = 'refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
        document.cookie = 'session=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
      }
    }
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />
      }

      return <DefaultAuthErrorFallback error={this.state.error} resetError={this.resetError} />
    }

    return this.props.children
  }
}

function DefaultAuthErrorFallback({ error, resetError }: { error?: Error; resetError: () => void }) {
  const handleReload = () => {
    resetError()
    window.location.reload()
  }

  const handleGoToLogin = () => {
    resetError()
    window.location.href = "/auth/login"
  }

  // Check if it's a data structure error
  const isDataError = error?.message.includes('.map is not a function') ||
    error?.message.includes('.filter is not a function') ||
    error?.message.includes('.forEach is not a function')

  const errorTitle = isDataError ? "Data Loading Error" : "Error Found"
  const errorDescription = isDataError
    ? "There was an issue loading the page data. This might be due to a temporary connection problem."
    : "Something went wrong. This might be a temporary issue."

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            <AlertTriangle className="h-12 w-12 text-red-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-center">{errorTitle}</CardTitle>
          <CardDescription className="text-center">
            {errorDescription}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {process.env.NODE_ENV === "development" && error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-800 font-medium">Error Details:</p>
              <p className="text-xs text-red-600 mt-1 font-mono">{error.message}</p>
            </div>
          )}
          <div className="flex flex-col space-y-2">
            <Button onClick={handleReload} className="w-full">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            <Button variant="outline" onClick={handleGoToLogin} className="w-full">
              Go to Login
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Hook to use error boundary in functional components
export function useAuthErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null)

  const handleError = React.useCallback((error: Error) => {
    console.error("Auth error:", error)
    setError(error)
  }, [])

  const resetError = React.useCallback(() => {
    setError(null)
  }, [])

  React.useEffect(() => {
    if (error) {
      throw error
    }
  }, [error])

  return { handleError, resetError }
}
