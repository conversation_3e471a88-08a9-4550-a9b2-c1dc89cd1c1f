#!/usr/bin/env node

/**
 * Script to test email configuration
 */

const { testEmailConfiguration } = require('../src/services/emailService');
const { logger } = require('../src/utils/logger');

async function testEmail() {
  console.log('🧪 Testing email configuration...');
  console.log('==========================================');
  
  try {
    // Get test email from command line args or use default
    const testEmail = process.argv[2] || process.env.EMAIL_TO_CLIENT;
    
    if (!testEmail) {
      console.error('❌ No test email provided. Usage: npm run test-email [<EMAIL>]');
      process.exit(1);
    }
    
    console.log(`📧 Sending test email to: ${testEmail}`);
    
    const result = await testEmailConfiguration(testEmail);
    
    if (result.success) {
      console.log('✅ Email configuration test PASSED!');
      console.log(`📬 Message ID: ${result.messageId}`);
      console.log('Check your inbox for the test email.');
    } else {
      console.log('❌ Email configuration test FAILED!');
      console.log(`Error: ${result.error}`);
      
      // Provide troubleshooting tips
      console.log('\n🔧 Troubleshooting tips:');
      console.log('1. Make sure you have enabled 2-Factor Authentication on your Gmail account');
      console.log('2. Generate an App Password for Gmail (not your regular password)');
      console.log('3. Use the App Password in your EMAIL_PASSWORD environment variable');
      console.log('4. Check that EMAIL_HOST=smtp.gmail.com and EMAIL_PORT=587');
      console.log('5. Ensure "Less secure app access" is turned off (use App Password instead)');
    }
  } catch (error) {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  }
}

// Run the test
testEmail();
