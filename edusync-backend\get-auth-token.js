const fetch = require('node-fetch');

async function getAuthToken() {
  try {
    console.log('Attempting to login...');
    
    const response = await fetch('http://localhost:4000/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'TestPassword123!'
      })
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Login successful!');
      console.log('Access Token:', data.data.accessToken);
      console.log('User:', data.data.user);
      return data.data.accessToken;
    } else {
      console.log('❌ Login failed:');
      console.log('Status:', response.status);
      console.log('Error:', data);
      return null;
    }
  } catch (error) {
    console.error('❌ Login error:', error.message);
    return null;
  }
}

// Test the institution endpoint with the token
async function testInstitutionEndpoint(token) {
  try {
    console.log('\nTesting institution endpoint...');
    
    const response = await fetch('http://localhost:4000/api/v1/institutions/360ccd63-cb03-4b5c-81c6-28d429c95b88', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Institution fetch successful!');
      console.log('Institution:', JSON.stringify(data, null, 2));
    } else {
      console.log('❌ Institution fetch failed:');
      console.log('Status:', response.status);
      console.log('Error:', data);
    }
  } catch (error) {
    console.error('❌ Institution fetch error:', error.message);
  }
}

// Run the test
async function runTest() {
  const token = await getAuthToken();
  if (token) {
    await testInstitutionEndpoint(token);
  }
}

runTest();
