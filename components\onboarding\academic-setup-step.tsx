"use client"

import type React from "react"

import { BookOpen } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useOnboarding } from "@/contexts/onboarding-context"

export function AcademicSetupStep() {
  const { onboardingData, updateOnboardingData, nextStep, prevStep } = useOnboarding()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    nextStep()
  }

  const currentYear = new Date().getFullYear()
  const academicYears = [
    `${currentYear - 1}-${currentYear}`,
    `${currentYear}-${currentYear + 1}`,
    `${currentYear + 1}-${currentYear + 2}`,
  ]

  return (
    <Card className="border-0 shadow-none">
      <CardHeader>
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100">
          <BookOpen className="h-6 w-6 text-emerald-600" />
        </div>
        <CardTitle className="text-center text-2xl">Academic Setup</CardTitle>
        <CardDescription className="text-center">Configure your institution's academic settings</CardDescription>
      </CardHeader>
      <CardContent>
        <form id="academic-form" onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="academicYear">Current Academic Year</Label>
            <Select
              value={onboardingData.academicYear}
              onValueChange={(value) => updateOnboardingData({ academicYear: value })}
            >
              <SelectTrigger id="academicYear">
                <SelectValue placeholder="Select academic year" />
              </SelectTrigger>
              <SelectContent>
                {academicYears.map((year) => (
                  <SelectItem key={year} value={year}>
                    {year}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="terms">Number of Terms/Semesters</Label>
            <Select
              value={onboardingData.terms.toString()}
              onValueChange={(value) => updateOnboardingData({ terms: Number.parseInt(value) })}
            >
              <SelectTrigger id="terms">
                <SelectValue placeholder="Select number of terms" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">1 Term/Semester</SelectItem>
                <SelectItem value="2">2 Terms/Semesters</SelectItem>
                <SelectItem value="3">3 Terms/Semesters</SelectItem>
                <SelectItem value="4">4 Terms/Semesters</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="gradeSystem">Grading System</Label>
            <RadioGroup
              value={onboardingData.gradeSystem}
              onValueChange={(value) => updateOnboardingData({ gradeSystem: value })}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="percentage" id="percentage" />
                <Label htmlFor="percentage">Percentage (0-100%)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="letter" id="letter" />
                <Label htmlFor="letter">Letter Grades (A, B, C, D, F)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="gpa" id="gpa" />
                <Label htmlFor="gpa">GPA (0.0-4.0)</Label>
              </div>
            </RadioGroup>
          </div>

          <div className="rounded-lg bg-muted p-4">
            <p className="text-sm font-medium">Note</p>
            <p className="text-xs text-muted-foreground">
              You can customize these settings further after completing the initial setup.
            </p>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={prevStep}>
          Back
        </Button>
        <Button type="submit" form="academic-form">
          Continue
        </Button>
      </CardFooter>
    </Card>
  )
}
