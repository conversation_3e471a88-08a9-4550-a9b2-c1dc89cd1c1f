import { NextResponse } from "next/server"

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    // Forward the request to our Flask backend
    const response = await fetch(`http://localhost:4000/api/v1/institutions/${params.id}`, {
      headers: {
        Authorization: request.headers.get("Authorization") || "",
        Cookie: request.headers.get("Cookie") || "",
      },
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: data.error || "Failed to fetch institution" }, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Institution API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const body = await request.json()

    // Forward the request to our Flask backend
    const response = await fetch(`http://localhost:4000/api/v1/institutions/${params.id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: request.headers.get("Authorization") || "",
        Cookie: request.headers.get("Cookie") || "",
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: data.error || "Failed to update institution" }, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Update institution API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    // Forward the request to our Flask backend
    const response = await fetch(`http://localhost:4000/api/v1/institutions/${params.id}`, {
      method: "DELETE",
      headers: {
        Authorization: request.headers.get("Authorization") || "",
        Cookie: request.headers.get("Cookie") || "",
      },
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: data.error || "Failed to delete institution" }, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Delete institution API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
