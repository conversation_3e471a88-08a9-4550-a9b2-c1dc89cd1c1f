"use client"

import { useState, useEffect } from "react"
import { PlusCircle, Search, Filter, MoreHorizontal, Download, Trash2, Edit, UserPlus, Loader2 } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { useTenant } from "@/contexts/tenant-context"
import { staffService } from "@/lib/backend-api"

// TypeScript interfaces
interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phoneNumber?: string
  profileImage?: string
  isActive: boolean
}

interface Staff {
  id: string
  userId: string
  institutionId: string
  department: string
  jobTitle: string
  hireDate: string
  salary?: number
  status: 'ACTIVE' | 'INACTIVE' | 'ON_LEAVE'
  user: User
  createdAt: string
  updatedAt: string
}

interface StaffResponse {
  staff: Staff[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}

interface CreateStaffData {
  email: string
  password: string
  firstName: string
  lastName: string
  phoneNumber?: string
  role: 'STAFF' | 'SCHOOL_ADMIN' | 'LIBRARIAN' | 'ACCOUNTANT'
  department: string
  jobTitle: string
  hireDate: string
  salary?: number
  status: 'ACTIVE' | 'INACTIVE' | 'ON_LEAVE'
  schoolId?: string // For SCHOOL_ADMIN role
}

// Common departments and job titles
const DEPARTMENTS = [
  "Administration",
  "Library",
  "IT",
  "Student Services",
  "Facilities",
  "Health Services",
  "Security",
  "Food Services",
  "Maintenance",
  "Office",
]

const JOB_TITLES = [
  "Administrator",
  "Librarian",
  "IT Support",
  "Counselor",
  "Maintenance",
  "Nurse",
  "Security Guard",
  "Cafeteria Staff",
  "Custodian",
  "Administrative Assistant",
  "Receptionist",
  "Lab Technician",
]

export default function StaffPage() {
  const { toast } = useToast()
  const { currentInstitution } = useTenant()
  const [staff, setStaff] = useState<Staff[]>([])
  const [schools, setSchools] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [jobTitleFilter, setJobTitleFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isAddStaffOpen, setIsAddStaffOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalStaff, setTotalStaff] = useState(0)

  // Form state for creating staff
  const [formData, setFormData] = useState<CreateStaffData>({
    email: "",
    password: "",
    firstName: "",
    lastName: "",
    phoneNumber: "",
    role: "STAFF",
    department: "",
    jobTitle: "",
    hireDate: "",
    salary: undefined,
    status: "ACTIVE",
  })

  // Fetch schools for the current institution
  const fetchSchools = async () => {
    if (!currentInstitution?.id) {
      console.log("No currentInstitution ID available for fetching schools")
      return
    }

    try {
      console.log("Fetching schools for institution:", currentInstitution.id)
      const response = await fetch(`/api/schools?institutionId=${currentInstitution.id}`, {
        credentials: 'include',
      })

      console.log("Schools response status:", response.status)

      if (response.ok) {
        const data = await response.json()
        console.log("Schools data received:", data)
        setSchools(data.data?.schools || [])
      } else {
        console.error("Failed to fetch schools:", response.status)
      }
    } catch (error) {
      console.error("Error fetching schools:", error)
    }
  }

  // Fetch staff from backend
  const fetchStaff = async () => {
    try {
      setLoading(true)
      const params = {
        page: currentPage,
        limit: 10,
        ...(searchTerm && { search: searchTerm }),
        ...(departmentFilter && departmentFilter !== "all" && { department: departmentFilter }),
        ...(jobTitleFilter && jobTitleFilter !== "all" && { jobTitle: jobTitleFilter }),
        ...(statusFilter && statusFilter !== "all" && { status: statusFilter }),
      }

      const response = await staffService.getStaff(params)
      
      if (response.success && response.data) {
        setStaff(response.data.staff || [])
        setTotalPages(response.data.pagination?.totalPages || 1)
        setTotalStaff(response.data.pagination?.total || 0)
      } else {
        throw new Error(response.error || "Failed to fetch staff")
      }
    } catch (error) {
      console.error("Error fetching staff:", error)
      toast({
        title: "Error",
        description: "Failed to fetch staff members. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Create new staff member
  const handleCreateStaff = async () => {
    try {
      setCreating(true)

      // Validate required fields
      if (!formData.email || !formData.password || !formData.firstName || !formData.lastName) {
        toast({
          title: "Error",
          description: "Please fill in all required fields.",
          variant: "destructive",
        })
        return
      }

      if (!currentInstitution?.id) {
        toast({
          title: "Error",
          description: "No institution selected.",
          variant: "destructive",
        })
        return
      }

      // Prepare user data for creation
      const userData = {
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName,
        phoneNumber: formData.phoneNumber,
        role: formData.role,
        institutionId: currentInstitution.id,
        ...(formData.role === 'SCHOOL_ADMIN' && formData.schoolId && { schoolId: formData.schoolId })
      }

      // Create user via API
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(userData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create user')
      }

      toast({
        title: "Success",
        description: `${formData.role === 'SCHOOL_ADMIN' ? 'School Admin' : 'Staff member'} created successfully.`,
      })

      setIsAddStaffOpen(false)
      setFormData({
        email: "",
        password: "",
        firstName: "",
        lastName: "",
        phoneNumber: "",
        role: "STAFF",
        department: "",
        jobTitle: "",
        hireDate: "",
        salary: undefined,
        status: "ACTIVE",
      })
      fetchStaff()
    } catch (error) {
      console.error("Error creating staff:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create staff member. Please try again.",
        variant: "destructive",
      })
    } finally {
      setCreating(false)
    }
  }

  // Delete staff member
  const handleDeleteStaff = async (staffId: string) => {
    try {
      await staffService.deleteStaff(staffId)
      toast({
        title: "Success",
        description: "Staff member deleted successfully.",
      })
      fetchStaff()
    } catch (error) {
      console.error("Error deleting staff:", error)
      toast({
        title: "Error",
        description: "Failed to delete staff member. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Effect to fetch staff when filters or page change
  useEffect(() => {
    fetchStaff()
  }, [currentPage, searchTerm, departmentFilter, jobTitleFilter, statusFilter])

  // Effect to fetch schools when institution changes
  useEffect(() => {
    if (currentInstitution?.id) {
      fetchSchools()
    }
  }, [currentInstitution])

  // Handle search with debounce effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setCurrentPage(1) // Reset to first page when searching
    }, 300)
    return () => clearTimeout(timeoutId)
  }, [searchTerm])

  // Reset filters
  const resetFilters = () => {
    setSearchTerm("")
    setDepartmentFilter("all")
    setJobTitleFilter("all")
    setStatusFilter("all")
    setCurrentPage(1)
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Staff Management</h2>
          <p className="text-muted-foreground">
            Manage all non-teaching staff members in your institution ({totalStaff} total)
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={isAddStaffOpen} onOpenChange={setIsAddStaffOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add User
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>Add New User</DialogTitle>
                <DialogDescription>
                  Create a new user account. You can add staff members, school admins, librarians, or accountants.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="firstName" className="text-right">
                    First Name
                  </Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="lastName" className="text-right">
                    Last Name
                  </Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">
                    Email *
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="password" className="text-right">
                    Password *
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className="col-span-3"
                    placeholder="Minimum 8 characters"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="phoneNumber" className="text-right">
                    Phone
                  </Label>
                  <Input
                    id="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={(e) => setFormData({ ...formData, phoneNumber: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="role" className="text-right">
                    Role *
                  </Label>
                  <Select value={formData.role} onValueChange={(value) => setFormData({ ...formData, role: value as any })}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="STAFF">Staff</SelectItem>
                      <SelectItem value="SCHOOL_ADMIN">School Admin</SelectItem>
                      <SelectItem value="LIBRARIAN">Librarian</SelectItem>
                      <SelectItem value="ACCOUNTANT">Accountant</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {formData.role === 'SCHOOL_ADMIN' && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="schoolId" className="text-right">
                      School *
                    </Label>
                    <Select value={formData.schoolId || ""} onValueChange={(value) => setFormData({ ...formData, schoolId: value })}>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select school" />
                      </SelectTrigger>
                      <SelectContent>
                        {schools.map((school) => (
                          <SelectItem key={school.id} value={school.id}>
                            {school.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="department" className="text-right">
                    Department
                  </Label>
                  <Select value={formData.department} onValueChange={(value) => setFormData({ ...formData, department: value })}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {DEPARTMENTS.map((dept) => (
                        <SelectItem key={dept} value={dept}>
                          {dept}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="jobTitle" className="text-right">
                    Job Title
                  </Label>
                  <Select value={formData.jobTitle} onValueChange={(value) => setFormData({ ...formData, jobTitle: value })}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select job title" />
                    </SelectTrigger>
                    <SelectContent>
                      {JOB_TITLES.map((title) => (
                        <SelectItem key={title} value={title}>
                          {title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="hireDate" className="text-right">
                    Hire Date
                  </Label>
                  <Input
                    id="hireDate"
                    type="date"
                    value={formData.hireDate}
                    onChange={(e) => setFormData({ ...formData, hireDate: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="salary" className="text-right">
                    Salary
                  </Label>
                  <Input
                    id="salary"
                    type="number"
                    value={formData.salary || ""}
                    onChange={(e) => setFormData({ ...formData, salary: e.target.value ? Number(e.target.value) : undefined })}
                    className="col-span-3"
                    placeholder="Optional"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="status" className="text-right">
                    Status
                  </Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value as any })}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ACTIVE">Active</SelectItem>
                      <SelectItem value="INACTIVE">Inactive</SelectItem>
                      <SelectItem value="ON_LEAVE">On Leave</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddStaffOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateStaff} disabled={creating}>
                  {creating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Save
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Staff Directory</CardTitle>
          <CardDescription>View and manage all staff members in your institution</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0 mb-4">
            <div className="relative w-full md:w-96">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search staff..."
                className="w-full pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  {DEPARTMENTS.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={jobTitleFilter} onValueChange={setJobTitleFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Job Title" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Titles</SelectItem>
                  {JOB_TITLES.map((title) => (
                    <SelectItem key={title} value={title}>
                      {title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="INACTIVE">Inactive</SelectItem>
                  <SelectItem value="ON_LEAVE">On Leave</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="sm" onClick={resetFilters}>
                Clear
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading staff...</span>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Job Title</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Hire Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {staff.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          No staff members found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      staff.map((staffMember) => (
                        <TableRow key={staffMember.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center space-x-2">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={staffMember.user.profileImage || "/placeholder.svg"} alt={`${staffMember.user.firstName} ${staffMember.user.lastName}`} />
                                <AvatarFallback>
                                  {staffMember.user.firstName.charAt(0)}{staffMember.user.lastName.charAt(0)}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div>{staffMember.user.firstName} {staffMember.user.lastName}</div>
                                <div className="text-xs text-muted-foreground">{staffMember.user.email}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{staffMember.jobTitle}</TableCell>
                          <TableCell>{staffMember.department}</TableCell>
                          <TableCell>{new Date(staffMember.hireDate).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <Badge variant={
                              staffMember.status === "ACTIVE" ? "default" :
                              staffMember.status === "ON_LEAVE" ? "secondary" : "destructive"
                            }>
                              {staffMember.status.replace("_", " ")}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Open menu</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <UserPlus className="mr-2 h-4 w-4" />
                                  Change Status
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  className="text-destructive"
                                  onClick={() => handleDeleteStaff(staffMember.id)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between space-x-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalStaff)} of {totalStaff} staff
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter(page => Math.abs(page - currentPage) <= 2)
                        .map(page => (
                          <Button
                            key={page}
                            variant={page === currentPage ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(page)}
                            className="w-8 h-8 p-0"
                          >
                            {page}
                          </Button>
                        ))}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
