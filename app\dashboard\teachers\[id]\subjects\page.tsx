"use client"

import { useState, useEffect, use } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, BookOpen, Users, Plus, Loader2 } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import Link from "next/link"
import { useTenant } from "@/contexts/tenant-context"
import { toast } from "sonner"

interface User {
  id: string
  firstName: string
  lastName: string
  email: string
}

interface Teacher {
  id: string
  userId: string
  user: User
  employeeId?: string
  qualification?: string
  specialization?: string
  department?: {
    id: string
    name: string
  }
}

interface Subject {
  id: string
  name: string
  code?: string
  description?: string
  isCore: boolean
  isActive: boolean
}

interface Assignment {
  id: string
  isClassTeacher: boolean
  startDate: string
  endDate?: string
  isActive: boolean
  subject: Subject
  department?: {
    id: string
    name: string
  }
}

export default function TeacherSubjectsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id: teacherId } = use(params)
  const { currentInstitution } = useTenant()

  const [teacher, setTeacher] = useState<Teacher | null>(null)
  const [assignments, setAssignments] = useState<Assignment[]>([])
  const [availableSubjects, setAvailableSubjects] = useState<Subject[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false)
  const [selectedSubject, setSelectedSubject] = useState<string>("")
  const [isAssigning, setIsAssigning] = useState(false)

  // Fetch teacher data
  const fetchTeacher = async () => {
    try {
      const response = await fetch(`/api/teachers/${teacherId}?institutionId=${currentInstitution.id}`, {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      if (data.success) {
        setTeacher(data.data)
      } else {
        toast.error(data.message || 'Failed to fetch teacher')
      }
    } catch (error) {
      console.error('Error fetching teacher:', error)
      toast.error('Failed to fetch teacher data')
    }
  }

  // Fetch teacher assignments
  const fetchAssignments = async () => {
    try {
      const response = await fetch(`/api/teachers/${teacherId}/assignments?institutionId=${currentInstitution.id}`, {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      if (data.success) {
        setAssignments(data.data)
      } else {
        toast.error(data.message || 'Failed to fetch assignments')
      }
    } catch (error) {
      console.error('Error fetching assignments:', error)
      toast.error('Failed to fetch teacher assignments')
    }
  }

  // Fetch available subjects
  const fetchAvailableSubjects = async () => {
    try {
      const response = await fetch(`/api/subjects?institutionId=${currentInstitution.id}&limit=100`, {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      if (data.success) {
        setAvailableSubjects(Array.isArray(data.data) ? data.data : [])
      } else {
        toast.error(data.message || 'Failed to fetch subjects')
      }
    } catch (error) {
      console.error('Error fetching subjects:', error)
      toast.error('Failed to fetch available subjects')
    }
  }

  // Handle subject assignment
  const handleAssignSubject = async () => {
    if (!selectedSubject) {
      toast.error('Please select a subject')
      return
    }

    setIsAssigning(true)
    try {
      const response = await fetch('/api/teachers/assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          teacherId,
          subjectId: selectedSubject,
          isClassTeacher: false
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Subject assigned successfully')
        setIsAssignDialogOpen(false)
        setSelectedSubject("")
        fetchAssignments() // Refresh assignments
      } else {
        toast.error(data.message || 'Failed to assign subject')
      }
    } catch (error) {
      console.error('Error assigning subject:', error)
      toast.error('Failed to assign subject')
    } finally {
      setIsAssigning(false)
    }
  }

  // Handle assignment removal
  const handleRemoveAssignment = async (assignmentId: string) => {
    try {
      const response = await fetch(`/api/teachers/assignments/${assignmentId}`, {
        method: 'DELETE',
        credentials: 'include',
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Assignment removed successfully')
        fetchAssignments() // Refresh assignments
      } else {
        toast.error(data.message || 'Failed to remove assignment')
      }
    } catch (error) {
      console.error('Error removing assignment:', error)
      toast.error('Failed to remove assignment')
    }
  }

  // Load data on component mount
  useEffect(() => {
    if (currentInstitution?.id) {
      const loadData = async () => {
        setIsLoading(true)
        await Promise.all([
          fetchTeacher(),
          fetchAssignments(),
          fetchAvailableSubjects()
        ])
        setIsLoading(false)
      }
      loadData()
    }
  }, [currentInstitution?.id, teacherId])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!teacher) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Teacher Not Found</h2>
          <p className="text-muted-foreground mb-4">The requested teacher could not be found.</p>
          <Button asChild>
            <Link href="/dashboard/teachers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Teachers
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  // Filter available subjects to exclude already assigned ones
  const unassignedSubjects = Array.isArray(availableSubjects) ? availableSubjects.filter(
    subject => !assignments.some(assignment => assignment.subject.id === subject.id)
  ) : []

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <Link href="/dashboard/teachers">
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Back to teachers</span>
              </Link>
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">Subject Assignments</h1>
          </div>
          <p className="text-muted-foreground ml-10">
            Subjects assigned to {teacher.user.firstName} {teacher.user.lastName}
            {teacher.department && ` • ${teacher.department.name} Department`}
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Assign Subject
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Assign Subject</DialogTitle>
                <DialogDescription>
                  Assign a new subject to {teacher.user.firstName} {teacher.user.lastName}.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="subject">Select Subject</Label>
                  <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a subject to assign" />
                    </SelectTrigger>
                    <SelectContent>
                      {unassignedSubjects.map((subject) => (
                        <SelectItem key={subject.id} value={subject.id}>
                          {subject.name} {subject.code && `(${subject.code})`}
                        </SelectItem>
                      ))}
                      {unassignedSubjects.length === 0 && (
                        <SelectItem value="" disabled>
                          No available subjects to assign
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsAssignDialogOpen(false)
                    setSelectedSubject("")
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={handleAssignSubject}
                  disabled={!selectedSubject || isAssigning}
                >
                  {isAssigning && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Assign Subject
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Subject Assignments Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {assignments.length > 0 ? (
          assignments.map((assignment) => (
            <Card key={assignment.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{assignment.subject.name}</CardTitle>
                    <CardDescription>
                      {assignment.subject.code && `${assignment.subject.code} • `}
                      {assignment.isClassTeacher && "Class Teacher • "}
                      Active since {new Date(assignment.startDate).toLocaleDateString()}
                    </CardDescription>
                  </div>
                  <Badge variant={assignment.isActive ? "default" : "secondary"}>
                    {assignment.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {assignment.subject.description && (
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {assignment.subject.description}
                    </p>
                  )}
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {assignment.subject.isCore ? "Core Subject" : "Elective"}
                    </Badge>
                    {assignment.isClassTeacher && (
                      <Badge variant="secondary" className="text-xs">
                        Class Teacher
                      </Badge>
                    )}
                  </div>
                  {assignment.department && (
                    <p className="text-xs text-muted-foreground">
                      Department: {assignment.department.name}
                    </p>
                  )}
                </div>
              </CardContent>
              <CardContent className="pt-0">
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <BookOpen className="mr-2 h-4 w-4" />
                    View Details
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveAssignment(assignment.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    Remove
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="col-span-full">
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <BookOpen className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Subject Assignments</h3>
                <p className="text-muted-foreground text-center mb-4">
                  This teacher hasn't been assigned to any subjects yet.
                </p>
                <Button onClick={() => setIsAssignDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Assign First Subject
                </Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Summary Statistics */}
      {assignments.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-primary" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Total Subjects</p>
                  <p className="text-2xl font-bold">{assignments.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-primary" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Core Subjects</p>
                  <p className="text-2xl font-bold">
                    {assignments.filter(a => a.subject.isCore).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-primary" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Class Teacher</p>
                  <p className="text-2xl font-bold">
                    {assignments.filter(a => a.isClassTeacher).length > 0 ? "Yes" : "No"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-primary" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Active Assignments</p>
                  <p className="text-2xl font-bold">
                    {assignments.filter(a => a.isActive).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
