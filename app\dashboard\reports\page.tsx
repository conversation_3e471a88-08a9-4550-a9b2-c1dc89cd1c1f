"use client"

import { useState } from "react"
import { format } from "date-fns"
import { CalendarIcon, Download, FileText, BarChart3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Filter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"

// Mock report types
const reportTypes = [
  { id: 1, name: "Student Attendance Report", icon: <BarChart3 className="h-5 w-5" /> },
  { id: 2, name: "Fee Collection Report", icon: <PieChart className="h-5 w-5" /> },
  { id: 3, name: "Exam Results Report", icon: <LineChart className="h-5 w-5" /> },
  { id: 4, name: "Teacher Performance Report", icon: <BarChart3 className="h-5 w-5" /> },
  { id: 5, name: "Class Performance Report", icon: <LineChart className="h-5 w-5" /> },
  { id: 6, name: "Student Progress Report", icon: <LineChart className="h-5 w-5" /> },
]

// Mock generated reports
const generatedReports = [
  {
    id: 1,
    name: "Class 10A Attendance - May 2023",
    type: "Student Attendance Report",
    generatedBy: "John Doe",
    generatedOn: new Date(2023, 4, 15),
    format: "PDF",
  },
  {
    id: 2,
    name: "Fee Collection Summary - Q2 2023",
    type: "Fee Collection Report",
    generatedBy: "Jane Smith",
    generatedOn: new Date(2023, 6, 2),
    format: "Excel",
  },
  {
    id: 3,
    name: "Final Exam Results - Class 9B",
    type: "Exam Results Report",
    generatedBy: "John Doe",
    generatedOn: new Date(2023, 5, 20),
    format: "PDF",
  },
  {
    id: 4,
    name: "Teacher Performance - Science Department",
    type: "Teacher Performance Report",
    generatedBy: "Admin",
    generatedOn: new Date(2023, 7, 10),
    format: "PDF",
  },
]

export default function ReportsPage() {
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  const [selectedReportType, setSelectedReportType] = useState("")
  const [selectedClass, setSelectedClass] = useState("")
  const [searchQuery, setSearchQuery] = useState("")

  const filteredReports = generatedReports.filter((report) =>
    report.name.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Reports</h2>
          <p className="text-muted-foreground">Generate and view reports for your institution</p>
        </div>
      </div>

      <Tabs defaultValue="generate">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="generate">Generate Reports</TabsTrigger>
          <TabsTrigger value="history">Report History</TabsTrigger>
        </TabsList>
        <TabsContent value="generate" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Generate New Report</CardTitle>
              <CardDescription>Select a report type and set parameters to generate a new report</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="report-type">Report Type</Label>
                  <Select value={selectedReportType} onValueChange={setSelectedReportType}>
                    <SelectTrigger id="report-type">
                      <SelectValue placeholder="Select report type" />
                    </SelectTrigger>
                    <SelectContent>
                      {reportTypes.map((type) => (
                        <SelectItem key={type.id} value={type.name}>
                          <div className="flex items-center">
                            <span className="mr-2">{type.icon}</span>
                            <span>{type.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="class">Class/Section</Label>
                  <Select value={selectedClass} onValueChange={setSelectedClass}>
                    <SelectTrigger id="class">
                      <SelectValue placeholder="Select class" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Classes</SelectItem>
                      <SelectItem value="10A">Class 10A</SelectItem>
                      <SelectItem value="10B">Class 10B</SelectItem>
                      <SelectItem value="9A">Class 9A</SelectItem>
                      <SelectItem value="9B">Class 9B</SelectItem>
                      <SelectItem value="8A">Class 8A</SelectItem>
                      <SelectItem value="8B">Class 8B</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Start Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !startDate && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {startDate ? format(startDate, "PPP") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar mode="single" selected={startDate} onSelect={setStartDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-2">
                  <Label>End Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !endDate && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {endDate ? format(endDate, "PPP") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar mode="single" selected={endDate} onSelect={setEndDate} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="format">Report Format</Label>
                  <Select defaultValue="pdf">
                    <SelectTrigger id="format">
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="excel">Excel</SelectItem>
                      <SelectItem value="csv">CSV</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="additional-filters">Additional Filters</Label>
                  <Button variant="outline" className="w-full justify-start">
                    <Filter className="mr-2 h-4 w-4" />
                    Add Filters
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button>Generate Report</Button>
            </CardFooter>
          </Card>
          <div className="grid gap-4 md:grid-cols-3">
            {reportTypes.map((type) => (
              <Card key={type.id} className="cursor-pointer hover:bg-gray-50 transition-colors">
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-lg">{type.name}</CardTitle>
                  <div className="p-2 bg-primary/10 rounded-full">{type.icon}</div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Generate detailed {type.name.toLowerCase()} with customizable parameters.
                  </p>
                </CardContent>
                <CardFooter>
                  <Button variant="ghost" className="w-full">
                    Quick Generate
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Report History</CardTitle>
                <CardDescription>View and download previously generated reports</CardDescription>
              </div>
              <div className="w-full max-w-sm">
                <Input
                  placeholder="Search reports..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredReports.length === 0 ? (
                  <div className="text-center py-6">
                    <FileText className="mx-auto h-12 w-12 text-muted-foreground opacity-50" />
                    <p className="mt-2 text-lg font-medium">No reports found</p>
                    <p className="text-sm text-muted-foreground">Try adjusting your search or generate a new report.</p>
                  </div>
                ) : (
                  filteredReports.map((report) => (
                    <div key={report.id} className="flex items-center justify-between rounded-lg border p-4">
                      <div className="flex items-start space-x-4">
                        <div className="p-2 bg-primary/10 rounded-full">
                          {report.type === "Student Attendance Report" ? (
                            <BarChart3 className="h-5 w-5" />
                          ) : report.type === "Fee Collection Report" ? (
                            <PieChart className="h-5 w-5" />
                          ) : (
                            <LineChart className="h-5 w-5" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{report.name}</p>
                          <p className="text-sm text-muted-foreground">{report.type}</p>
                          <p className="text-xs text-muted-foreground">
                            Generated by {report.generatedBy} on {format(report.generatedOn, "MMM dd, yyyy")}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm">
                          Preview
                        </Button>
                        <Button size="sm">
                          <Download className="mr-2 h-4 w-4" />
                          Download {report.format}
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
