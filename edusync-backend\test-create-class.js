const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestClass() {
  try {
    console.log('🔍 Creating a test class...');
    
    // Get the first school and academic year
    const school = await prisma.school.findFirst({ where: { isActive: true } });
    const academicYear = await prisma.academicYear.findFirst({ where: { isActive: true } });
    
    if (!school || !academicYear) {
      console.log('❌ Missing school or academic year');
      return;
    }
    
    console.log(`📚 Using school: ${school.name} (${school.id})`);
    console.log(`📅 Using academic year: ${academicYear.name} (${academicYear.id})`);
    
    // Create a new class
    const newClass = await prisma.class.create({
      data: {
        name: `Test Class ${Date.now()}`,
        gradeLevel: '2',
        section: 'B',
        capacity: 30,
        roomNumber: '202',
        schoolId: school.id,
        academicYearId: academicYear.id,
        isActive: true
      },
      include: {
        school: { select: { id: true, name: true } },
        academicYear: { select: { id: true, name: true } }
      }
    });
    
    console.log('✅ Class created successfully!');
    console.log(`   - ID: ${newClass.id}`);
    console.log(`   - Name: ${newClass.name}`);
    console.log(`   - Grade: ${newClass.gradeLevel}`);
    console.log(`   - Section: ${newClass.section}`);
    console.log(`   - Capacity: ${newClass.capacity}`);
    console.log(`   - Room: ${newClass.roomNumber}`);
    console.log(`   - School: ${newClass.school.name}`);
    console.log(`   - Academic Year: ${newClass.academicYear.name}`);
    
    // Verify by counting total classes
    const totalClasses = await prisma.class.count();
    console.log(`📊 Total classes in database: ${totalClasses}`);
    
  } catch (error) {
    console.error('❌ Error creating class:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestClass();
