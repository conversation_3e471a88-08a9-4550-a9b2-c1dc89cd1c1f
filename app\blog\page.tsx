import type { <PERSON>ada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"

import { LandingHeader } from "@/components/landing/landing-header"
import { Footer } from "@/components/landing/footer"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export const metadata: Metadata = {
  title: "Blog | Edusync",
  description: "Latest news, updates, and insights about education technology and school management.",
}

export default function BlogPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <LandingHeader />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 py-20 md:py-28">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Our Blog</h1>
                <p className="mx-auto max-w-[700px] text-slate-500 dark:text-slate-400 md:text-xl">
                  Insights, updates, and stories from the world of education technology
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Post */}
        <section className="py-20">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="grid gap-10 lg:grid-cols-2 items-center">
              <div className="relative aspect-video overflow-hidden rounded-xl">
                <Image
                  src="/placeholder.svg?height=600&width=800"
                  alt="Featured post"
                  width={800}
                  height={600}
                  className="object-cover"
                />
              </div>
              <div className="space-y-4">
                <div className="flex items-center space-x-4 text-sm text-slate-500 dark:text-slate-400">
                  <span>March 15, 2024</span>
                  <span>•</span>
                  <span>5 min read</span>
                </div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">
                  The Future of Education: How Technology is Transforming Learning
                </h2>
                <p className="text-slate-500 dark:text-slate-400 md:text-lg">
                  Explore how emerging technologies are reshaping the educational landscape and what it means for
                  schools, teachers, and students in the digital age.
                </p>
                <Button asChild>
                  <Link href="/blog/future-of-education">Read More</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Blog Posts Grid */}
        <section className="py-20 bg-slate-50 dark:bg-slate-900">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {blogPosts.map((post, index) => (
                <Card key={index} className="overflow-hidden">
                  <div className="aspect-video relative">
                    <Image src={post.image} alt={post.title} fill className="object-cover" />
                  </div>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4 text-sm text-slate-500 dark:text-slate-400 mb-2">
                      <span>{post.date}</span>
                      <span>•</span>
                      <span>{post.readTime}</span>
                    </div>
                    <h3 className="font-bold text-xl mb-2">{post.title}</h3>
                    <p className="text-slate-500 dark:text-slate-400 mb-4">{post.excerpt}</p>
                    <Button variant="ghost" asChild>
                      <Link href={post.href}>Read More →</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="py-20">
          <div className="container mx-auto px-4 md:px-6 max-w-6xl">
            <div className="bg-primary text-white rounded-2xl p-8 md:p-12">
              <div className="max-w-2xl mx-auto text-center">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl mb-4">
                  Subscribe to Our Newsletter
                </h2>
                <p className="text-primary-foreground/80 mb-8">
                  Get the latest updates, news, and insights delivered straight to your inbox.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 px-4 py-2 rounded-lg text-black"
                  />
                  <Button variant="secondary" className="whitespace-nowrap">
                    Subscribe
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}

const blogPosts = [
  {
    title: "5 Ways to Improve Student Engagement in the Digital Age",
    excerpt: "Discover effective strategies for keeping students engaged in an increasingly digital learning environment.",
    date: "March 10, 2024",
    readTime: "4 min read",
    image: "/placeholder.svg?height=400&width=600",
    href: "/blog/improve-student-engagement",
  },
  {
    title: "The Role of AI in Modern Education",
    excerpt: "How artificial intelligence is revolutionizing teaching methods and personalized learning experiences.",
    date: "March 5, 2024",
    readTime: "6 min read",
    image: "/placeholder.svg?height=400&width=600",
    href: "/blog/ai-in-education",
  },
  {
    title: "Building a Strong School Community Online",
    excerpt: "Tips and strategies for fostering meaningful connections in virtual learning environments.",
    date: "February 28, 2024",
    readTime: "5 min read",
    image: "/placeholder.svg?height=400&width=600",
    href: "/blog/online-school-community",
  },
  {
    title: "Data-Driven Decision Making in Education",
    excerpt: "How schools can leverage data analytics to improve student outcomes and operational efficiency.",
    date: "February 20, 2024",
    readTime: "7 min read",
    image: "/placeholder.svg?height=400&width=600",
    href: "/blog/data-driven-education",
  },
  {
    title: "The Impact of Mobile Learning on Student Success",
    excerpt: "Exploring how mobile technology is changing the way students learn and interact with educational content.",
    date: "February 15, 2024",
    readTime: "5 min read",
    image: "/placeholder.svg?height=400&width=600",
    href: "/blog/mobile-learning-impact",
  },
  {
    title: "Cybersecurity Best Practices for Schools",
    excerpt: "Essential security measures to protect student data and maintain a safe digital learning environment.",
    date: "February 10, 2024",
    readTime: "6 min read",
    image: "/placeholder.svg?height=400&width=600",
    href: "/blog/school-cybersecurity",
  },
] 