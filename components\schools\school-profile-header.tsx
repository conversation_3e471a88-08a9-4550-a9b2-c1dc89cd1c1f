import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Building2, Mail, Globe, Phone } from "lucide-react"

export function SchoolProfileHeader() {
  return (
    <Card className="border-none shadow-none">
      <CardContent className="p-6">
        <div className="flex flex-col gap-8">
          <div className="flex items-start gap-8">
            <div className="flex flex-col items-center gap-4">
              <div className="relative">
                <Avatar className="h-32 w-32 border-4 border-background shadow-lg">
                  <AvatarImage src="/placeholder-school-logo.png" alt="School Logo" />
                  <AvatarFallback className="text-2xl">SC</AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-2 left-1/2 -translate-x-1/2 flex gap-2">
                  <Button variant="outline" size="sm" className="rounded-full">
                    Change Logo
                  </Button>
                  <Button variant="outline" size="sm" className="rounded-full text-destructive">
                    Remove
                  </Button>
                </div>
              </div>
            </div>
            <div className="flex-1 space-y-6 ml-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="schoolName" className="text-sm font-medium">School Name</Label>
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <Input 
                      id="schoolName" 
                      placeholder="Enter school name"
                      className="text-lg font-semibold" 
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="schoolEmail" className="text-sm font-medium">Email Address</Label>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <Input id="schoolEmail" type="email" placeholder="Enter school email" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="schoolPhone" className="text-sm font-medium">Phone Number</Label>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <Input id="schoolPhone" type="tel" placeholder="Enter phone number" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="schoolWebsite" className="text-sm font-medium">Website</Label>
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <Input id="schoolWebsite" type="url" placeholder="Enter website URL" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 