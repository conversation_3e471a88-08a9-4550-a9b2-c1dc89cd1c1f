"use client"

import { useRef } from "react"
import { motion, useInView } from "framer-motion"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowRight } from "lucide-react"

export function HowItWorksSection() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const steps = [
    {
      title: "Sign Up",
      description: "Create your school account in minutes with our simple onboarding process.",
      image: "/placeholder.svg?height=300&width=400",
    },
    {
      title: "Set Up Your School",
      description: "Add your school details, classes, teachers, and students to the platform.",
      image: "/placeholder.svg?height=300&width=400",
    },
    {
      title: "Start Managing",
      description: "Begin using the platform to manage attendance, grades, fees, and more.",
      image: "/placeholder.svg?height=300&width=400",
    },
  ]

  return (
    <section ref={ref} className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900">How It Works</h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Get your school up and running on our platform in three simple steps
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
              className="flex flex-col items-center"
            >
              <div className="relative mb-6">
                <div className="absolute -inset-1 bg-gradient-to-r from-emerald-600 to-teal-600 rounded-full opacity-75 blur-sm"></div>
                <div className="relative flex items-center justify-center w-16 h-16 rounded-full bg-white text-emerald-600 text-2xl font-bold border-2 border-emerald-500">
                  {index + 1}
                </div>
                {index < steps.length - 1 && (
                  <div className="absolute top-1/2 left-full w-full h-0.5 bg-emerald-200 hidden md:block"></div>
                )}
              </div>

              <div className="bg-gray-50 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                <div className="relative h-48 w-full">
                  <Image src={step.image || "/placeholder.svg"} alt={step.title} fill className="object-cover" />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{step.title}</h3>
                  <p className="text-gray-600">{step.description}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <Link href="/auth/register">
            <Button size="lg" className="group">
              Get Started Now
              <ArrowRight className="ml-2 h-4 w-4 transform group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
