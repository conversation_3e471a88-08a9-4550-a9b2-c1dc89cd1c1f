import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Building2, Mail, Globe, Phone, MapPin } from "lucide-react"
import { useState } from "react"

interface Institution {
  id: string
  name: string
  domain?: string
  logo?: string
  primaryColor?: string
  secondaryColor?: string
  isActive: boolean
  subscriptionStatus: string
  subscriptionEndDate?: string
  address?: string
  city?: string
  state?: string
  country?: string
  postalCode?: string
  phoneNumber?: string
  email?: string
  website?: string
  verificationStatus: boolean
  studentCount?: number
  teacherCount?: number
  referralSource?: string
  specialRequirements?: string
  createdAt: string
  updatedAt: string
}

interface InstitutionProfileHeaderProps {
  institution: Institution
  onUpdate: (data: Partial<Institution>) => Promise<void>
}

export function InstitutionProfileHeader({ institution, onUpdate }: InstitutionProfileHeaderProps) {
  const [isEditingLogo, setIsEditingLogo] = useState(false)
  const [logoFile, setLogoFile] = useState<File | null>(null)

  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setLogoFile(file)
      // Here you would typically upload the file to your server
      // For now, we'll just show a preview
      const reader = new FileReader()
      reader.onload = (e) => {
        // You could update the institution logo preview here
      }
      reader.readAsDataURL(file)
    }
  }

  const handleLogoUpload = async () => {
    if (!logoFile) return

    try {
      // Here you would upload the file to your server
      // For now, we'll just simulate the update
      await onUpdate({ logo: URL.createObjectURL(logoFile) })
      setIsEditingLogo(false)
      setLogoFile(null)
    } catch (error) {
      console.error("Error uploading logo:", error)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(word => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <Card className="border-none shadow-none">
      <CardContent className="p-6">
        <div className="flex flex-col gap-8">
          <div className="flex items-start gap-8">
            <div className="flex flex-col items-center gap-4">
              <div className="relative">
                <Avatar className="h-32 w-32 border-4 border-background shadow-lg">
                  <AvatarImage src={institution.logo || "/placeholder-institution-logo.png"} alt="Institution Logo" />
                  <AvatarFallback className="text-2xl bg-primary text-primary-foreground">
                    {getInitials(institution.name)}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-2 left-1/2 -translate-x-1/2 flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="rounded-full"
                    onClick={() => setIsEditingLogo(true)}
                  >
                    Change Logo
                  </Button>
                  {institution.logo && (
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="rounded-full text-destructive"
                      onClick={() => onUpdate({ logo: undefined })}
                    >
                      Remove
                    </Button>
                  )}
                </div>
              </div>
              
              {isEditingLogo && (
                <div className="flex flex-col gap-2 w-full max-w-xs">
                  <Label htmlFor="logo-upload">Upload new logo</Label>
                  <Input
                    id="logo-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleLogoChange}
                    className="text-sm"
                  />
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      onClick={handleLogoUpload}
                      disabled={!logoFile}
                    >
                      Upload
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => {
                        setIsEditingLogo(false)
                        setLogoFile(null)
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex-1 space-y-4">
              <div>
                <h2 className="text-3xl font-bold text-foreground">{institution.name}</h2>
                <p className="text-muted-foreground">
                  {institution.domain && `Domain: ${institution.domain}`}
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Email</p>
                    <p className="text-sm text-muted-foreground">
                      {institution.email || "Not provided"}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Phone</p>
                    <p className="text-sm text-muted-foreground">
                      {institution.phoneNumber || "Not provided"}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Globe className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Website</p>
                    <p className="text-sm text-muted-foreground">
                      {institution.website ? (
                        <a 
                          href={institution.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-primary hover:underline"
                        >
                          {institution.website}
                        </a>
                      ) : (
                        "Not provided"
                      )}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Location</p>
                    <p className="text-sm text-muted-foreground">
                      {[institution.city, institution.state, institution.country]
                        .filter(Boolean)
                        .join(", ") || "Not provided"}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-4 pt-2">
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${
                    institution.isActive ? "bg-green-500" : "bg-red-500"
                  }`} />
                  <span className="text-sm font-medium">
                    {institution.isActive ? "Active" : "Inactive"}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    Subscription: {institution.subscriptionStatus}
                  </span>
                </div>
                
                {institution.verificationStatus && (
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-blue-500" />
                    <span className="text-sm font-medium text-blue-600">
                      Verified
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 