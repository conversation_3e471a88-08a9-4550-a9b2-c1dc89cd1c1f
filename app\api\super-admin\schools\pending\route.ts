import { NextResponse } from "next/server"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const page = searchParams.get("page") || "1"
    const per_page = searchParams.get("per_page") || "20"

    const response = await fetch(
      `${process.env.BACKEND_URL}/api/v1/super-admin/schools/pending?page=${page}&per_page=${per_page}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: request.headers.get("Authorization") || "",
        },
      },
    )

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: data.error || "Failed to fetch pending schools" }, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Pending schools API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
