const { PrismaClient } = require('@prisma/client');
const asyncHandler = require('../../../utils/asyncHandler');

const prisma = new PrismaClient();

/**
 * Get subscription statistics
 */
const getSubscriptionStats = asyncHandler(async (req, res) => {
  // Get real subscription statistics from database
  const [
    totalSubscriptions,
    activeSubscriptions,
    pendingSubscriptions,
    cancelledSubscriptions,
    subscriptionsByPlan,
    monthlyPayments,
    annualPayments
  ] = await Promise.all([
    prisma.subscription.count(),
    prisma.subscription.count({ where: { status: 'ACTIVE' } }),
    prisma.subscription.count({ where: { status: 'TRIAL' } }),
    prisma.subscription.count({ where: { status: 'INACTIVE' } }),
    prisma.subscription.groupBy({
      by: ['pricingPlanId'],
      _count: { id: true },
      include: {
        pricingPlan: {
          select: { name: true }
        }
      }
    }),
    prisma.payment.aggregate({
      _sum: { amount: true },
      where: {
        status: 'PAID',
        createdAt: {
          gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        }
      }
    }),
    prisma.payment.aggregate({
      _sum: { amount: true },
      where: {
        status: 'PAID',
        createdAt: {
          gte: new Date(new Date().getFullYear(), 0, 1)
        }
      }
    })
  ]);

  // Get plan distribution with plan names
  const planDistribution = {};
  for (const planStat of subscriptionsByPlan) {
    const plan = await prisma.pricingPlan.findUnique({
      where: { id: planStat.pricingPlanId },
      select: { name: true }
    });
    planDistribution[plan?.name || 'Unknown'] = planStat._count.id;
  }

  const stats = {
    totalSubscriptions,
    activeSubscriptions,
    pendingSubscriptions,
    cancelledSubscriptions,
    monthlyRevenue: monthlyPayments._sum.amount || 0,
    annualRevenue: annualPayments._sum.amount || 0,
    planDistribution,
    growthRate: 12.5 // TODO: Calculate actual growth rate
  };

  res.status(200).json({
    success: true,
    data: stats
  });
});

/**
 * Get all subscriptions
 */
const getSubscriptions = asyncHandler(async (req, res) => {
  const { page = 1, limit = 50, plan, status, search } = req.query;
  const skip = (page - 1) * limit;

  // Build filter object
  const where = {};
  
  if (status && status !== 'all') {
    where.status = status;
  }

  if (plan && plan !== 'all') {
    where.pricingPlan = {
      name: plan
    };
  }

  if (search) {
    where.institution = {
      name: {
        contains: search,
        mode: 'insensitive'
      }
    };
  }

  // Get total count for pagination
  const totalItems = await prisma.subscription.count({ where });

  // Get subscriptions with related data
  const subscriptions = await prisma.subscription.findMany({
    where,
    include: {
      institution: {
        select: {
          id: true,
          name: true,
          email: true,
          contactPerson: true
        }
      },
      pricingPlan: {
        select: {
          id: true,
          name: true,
          price: true,
          billing: true
        }
      },
      payments: {
        orderBy: { createdAt: 'desc' },
        take: 1,
        select: {
          id: true,
          amount: true,
          status: true,
          paidAt: true
        }
      }
    },
    orderBy: { createdAt: 'desc' },
    skip: parseInt(skip),
    take: parseInt(limit)
  });

  // Format subscription data
  const formattedSubscriptions = subscriptions.map(sub => ({
    id: sub.id,
    institutionId: sub.institutionId,
    institutionName: sub.institution.name,
    contactPerson: sub.institution.contactPerson,
    plan: sub.pricingPlan.name,
    status: sub.status,
    amount: parseFloat(sub.amount),
    billingCycle: sub.billingCycle,
    startDate: sub.startDate,
    endDate: sub.endDate,
    nextPayment: sub.nextBillingDate,
    lastPayment: sub.payments[0]?.paidAt || null,
    lastPaymentAmount: sub.payments[0] ? parseFloat(sub.payments[0].amount) : null,
    autoRenew: sub.autoRenew,
    currentStudents: sub.currentStudents,
    currentTeachers: sub.currentTeachers,
    storageUsed: sub.storageUsed,
    createdAt: sub.createdAt
  }));

  res.status(200).json({
    success: true,
    data: {
      subscriptions: formattedSubscriptions,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalItems / limit),
        totalItems,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

/**
 * Get subscription by ID
 */
const getSubscriptionById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const subscription = await prisma.subscription.findUnique({
    where: { id },
    include: {
      institution: {
        select: {
          id: true,
          name: true,
          email: true,
          contactPerson: true,
          phone: true,
          address: true
        }
      },
      pricingPlan: {
        select: {
          id: true,
          name: true,
          description: true,
          price: true,
          billing: true,
          features: true
        }
      },
      payments: {
        orderBy: { createdAt: 'desc' },
        take: 10,
        select: {
          id: true,
          amount: true,
          status: true,
          paymentMethod: true,
          paidAt: true,
          createdAt: true,
          invoiceNumber: true,
          transactionId: true
        }
      }
    }
  });

  if (!subscription) {
    return res.status(404).json({
      success: false,
      message: 'Subscription not found'
    });
  }

  // Format the response
  const formattedSubscription = {
    id: subscription.id,
    institutionId: subscription.institutionId,
    institutionName: subscription.institution.name,
    institutionEmail: subscription.institution.email,
    contactPerson: subscription.institution.contactPerson,
    plan: subscription.pricingPlan.name,
    planDescription: subscription.pricingPlan.description,
    status: subscription.status,
    amount: parseFloat(subscription.amount),
    billingCycle: subscription.billingCycle,
    startDate: subscription.startDate,
    endDate: subscription.endDate,
    nextPayment: subscription.nextBillingDate,
    autoRenew: subscription.autoRenew,
    features: subscription.pricingPlan.features,
    currentStudents: subscription.currentStudents,
    currentTeachers: subscription.currentTeachers,
    storageUsed: subscription.storageUsed,
    lastPaymentDate: subscription.lastPaymentDate,
    lastPaymentAmount: subscription.lastPaymentAmount ? parseFloat(subscription.lastPaymentAmount) : null,
    failedPaymentCount: subscription.failedPaymentCount,
    trialEndDate: subscription.trialEndDate,
    cancelledAt: subscription.cancelledAt,
    cancellationReason: subscription.cancellationReason,
    notes: subscription.notes,
    paymentHistory: subscription.payments.map(payment => ({
      id: payment.id,
      amount: parseFloat(payment.amount),
      date: payment.paidAt || payment.createdAt,
      status: payment.status,
      method: payment.paymentMethod,
      invoiceNumber: payment.invoiceNumber,
      transactionId: payment.transactionId
    })),
    createdAt: subscription.createdAt,
    updatedAt: subscription.updatedAt
  };

  res.status(200).json({
    success: true,
    data: formattedSubscription
  });
});

/**
 * Update subscription
 */
const updateSubscription = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { 
    pricingPlanId, 
    status, 
    billingCycle, 
    autoRenew,
    nextBillingDate,
    cancellationReason,
    notes
  } = req.body;

  // Check if subscription exists
  const existingSubscription = await prisma.subscription.findUnique({
    where: { id },
    include: {
      institution: { select: { name: true } },
      pricingPlan: { select: { name: true, price: true } }
    }
  });

  if (!existingSubscription) {
    return res.status(404).json({
      success: false,
      message: 'Subscription not found'
    });
  }

  // Prepare update data
  const updateData = {};
  
  if (pricingPlanId) {
    // Validate pricing plan exists
    const pricingPlan = await prisma.pricingPlan.findUnique({
      where: { id: pricingPlanId }
    });
    
    if (!pricingPlan) {
      return res.status(400).json({
        success: false,
        message: 'Invalid pricing plan ID'
      });
    }
    
    updateData.pricingPlanId = pricingPlanId;
    updateData.amount = pricingPlan.price;
  }
  
  if (status) updateData.status = status;
  if (billingCycle) updateData.billingCycle = billingCycle;
  if (autoRenew !== undefined) updateData.autoRenew = autoRenew;
  if (nextBillingDate) updateData.nextBillingDate = new Date(nextBillingDate);
  if (notes !== undefined) updateData.notes = notes;
  
  // Handle cancellation
  if (status === 'CANCELLED') {
    updateData.cancelledAt = new Date();
    updateData.cancellationReason = cancellationReason || 'Cancelled by admin';
    updateData.autoRenew = false;
  }

  // Update subscription
  const updatedSubscription = await prisma.subscription.update({
    where: { id },
    data: updateData,
    include: {
      institution: { select: { name: true } },
      pricingPlan: { select: { name: true, price: true } }
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Subscription Updated',
      details: `Subscription updated for ${existingSubscription.institution.name} - Status: ${status || 'unchanged'}, Plan: ${updatedSubscription.pricingPlan.name}`,
      category: 'Billing',
      severity: 'Info',
      entityType: 'Subscription',
      entityId: id,
      entityName: existingSubscription.institution.name,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Subscription updated successfully',
    data: {
      id: updatedSubscription.id,
      institutionName: updatedSubscription.institution.name,
      plan: updatedSubscription.pricingPlan.name,
      status: updatedSubscription.status,
      billingCycle: updatedSubscription.billingCycle,
      amount: parseFloat(updatedSubscription.amount),
      autoRenew: updatedSubscription.autoRenew,
      nextBillingDate: updatedSubscription.nextBillingDate,
      updatedAt: updatedSubscription.updatedAt
    }
  });
});

/**
 * Create new subscription
 */
const createSubscription = asyncHandler(async (req, res) => {
  const { 
    institutionId, 
    pricingPlanId, 
    billingCycle, 
    startDate,
    autoRenew = true,
    trialDays = 0
  } = req.body;

  // Validate required fields
  if (!institutionId || !pricingPlanId || !billingCycle) {
    return res.status(400).json({
      success: false,
      message: 'Institution ID, pricing plan ID, and billing cycle are required'
    });
  }

  // Check if institution exists
  const institution = await prisma.institution.findUnique({
    where: { id: institutionId },
    select: { id: true, name: true }
  });

  if (!institution) {
    return res.status(404).json({
      success: false,
      message: 'Institution not found'
    });
  }

  // Check if pricing plan exists
  const pricingPlan = await prisma.pricingPlan.findUnique({
    where: { id: pricingPlanId },
    select: { id: true, name: true, price: true, billing: true }
  });

  if (!pricingPlan) {
    return res.status(404).json({
      success: false,
      message: 'Pricing plan not found'
    });
  }

  // Check if institution already has an active subscription
  const existingSubscription = await prisma.subscription.findUnique({
    where: { institutionId }
  });

  if (existingSubscription) {
    return res.status(400).json({
      success: false,
      message: 'Institution already has an active subscription'
    });
  }

  // Calculate dates
  const subscriptionStartDate = startDate ? new Date(startDate) : new Date();
  const trialEndDate = trialDays > 0 ? new Date(subscriptionStartDate.getTime() + (trialDays * 24 * 60 * 60 * 1000)) : null;
  
  // Calculate end date based on billing cycle
  const endDate = new Date(subscriptionStartDate);
  if (billingCycle === 'monthly') {
    endDate.setMonth(endDate.getMonth() + 1);
  } else if (billingCycle === 'yearly') {
    endDate.setFullYear(endDate.getFullYear() + 1);
  }

  // Calculate next billing date
  const nextBillingDate = trialEndDate || new Date(subscriptionStartDate);
  if (billingCycle === 'monthly') {
    nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
  } else if (billingCycle === 'yearly') {
    nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
  }

  // Create subscription
  const subscription = await prisma.subscription.create({
    data: {
      institutionId,
      pricingPlanId,
      status: trialDays > 0 ? 'TRIAL' : 'ACTIVE',
      billingCycle,
      amount: pricingPlan.price,
      currency: 'USD',
      startDate: subscriptionStartDate,
      endDate,
      nextBillingDate,
      autoRenew,
      trialEndDate,
      currentStudents: 0,
      currentTeachers: 0,
      storageUsed: 0
    },
    include: {
      institution: { select: { name: true } },
      pricingPlan: { select: { name: true, price: true } }
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Subscription Created',
      details: `New subscription created for ${institution.name} - Plan: ${pricingPlan.name}, Billing: ${billingCycle}`,
      category: 'Billing',
      severity: 'Info',
      entityType: 'Subscription',
      entityId: subscription.id,
      entityName: institution.name,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(201).json({
    success: true,
    message: 'Subscription created successfully',
    data: {
      id: subscription.id,
      institutionId: subscription.institutionId,
      institutionName: subscription.institution.name,
      plan: subscription.pricingPlan.name,
      billingCycle: subscription.billingCycle,
      amount: parseFloat(subscription.amount),
      status: subscription.status,
      startDate: subscription.startDate,
      endDate: subscription.endDate,
      nextBillingDate: subscription.nextBillingDate,
      trialEndDate: subscription.trialEndDate,
      autoRenew: subscription.autoRenew,
      createdAt: subscription.createdAt
    }
  });
});

/**
 * Cancel subscription
 */
const cancelSubscription = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { reason, cancelAtPeriodEnd = true } = req.body;

  // In a real implementation, this would cancel the subscription in the billing system
  
  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Subscription Cancelled',
      details: `Subscription ${id} cancelled. Reason: ${reason}`,
      category: 'Billing',
      severity: 'Important',
      entityType: 'Subscription',
      entityId: id,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Subscription cancelled successfully',
    data: {
      id,
      status: 'Cancelled',
      cancelledAt: new Date(),
      reason,
      cancelAtPeriodEnd
    }
  });
});

/**
 * Get payment history
 */
const getPaymentHistory = asyncHandler(async (req, res) => {
  const { page = 1, limit = 50, status, method, institutionId, startDate, endDate } = req.query;
  const skip = (page - 1) * limit;

  // Build filter object
  const where = {};
  
  if (status && status !== 'all') {
    where.status = status;
  }

  if (method && method !== 'all') {
    where.paymentMethod = method;
  }

  if (institutionId) {
    where.subscription = {
      institutionId: institutionId
    };
  }

  // Date range filter
  if (startDate || endDate) {
    where.createdAt = {};
    if (startDate) where.createdAt.gte = new Date(startDate);
    if (endDate) where.createdAt.lte = new Date(endDate);
  }

  // Get total count for pagination
  const totalItems = await prisma.payment.count({ where });

  // Get payments with related data
  const payments = await prisma.payment.findMany({
    where,
    include: {
      subscription: {
        include: {
          institution: {
            select: {
              id: true,
              name: true
            }
          },
          pricingPlan: {
            select: {
              name: true
            }
          }
        }
      }
    },
    orderBy: { createdAt: 'desc' },
    skip: parseInt(skip),
    take: parseInt(limit)
  });

  // Format payment data
  const formattedPayments = payments.map(payment => ({
    id: payment.id,
    subscriptionId: payment.subscriptionId,
    institutionId: payment.subscription.institutionId,
    institutionName: payment.subscription.institution.name,
    amount: parseFloat(payment.amount),
    currency: payment.currency,
    status: payment.status,
    method: payment.paymentMethod,
    transactionId: payment.transactionId,
    invoiceNumber: payment.invoiceNumber,
    date: payment.paidAt || payment.createdAt,
    dueDate: payment.dueDate,
    description: payment.description || `${payment.subscription.pricingPlan.name} subscription`,
    failureReason: payment.failureReason,
    billingStart: payment.billingStart,
    billingEnd: payment.billingEnd,
    createdAt: payment.createdAt,
    updatedAt: payment.updatedAt
  }));

  res.status(200).json({
    success: true,
    data: {
      payments: formattedPayments,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalItems / limit),
        totalItems,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

/**
 * Generate subscription report
 */
const generateSubscriptionReport = asyncHandler(async (req, res) => {
  const { format = 'csv', period = '30', type = 'overview' } = req.query;

  // In a real implementation, this would generate actual report data
  const reportData = {
    period: `Last ${period} days`,
    generatedAt: new Date(),
    type,
    summary: {
      totalRevenue: 45000,
      activeSubscriptions: 132,
      newSubscriptions: 12,
      cancelledSubscriptions: 3,
      averageRevenuePerUser: 340
    }
  };

  res.status(200).json({
    success: true,
    message: `Subscription report generated successfully in ${format.toUpperCase()} format`,
    data: {
      filename: `subscription-report-${Date.now()}.${format}`,
      reportData,
      downloadUrl: `/exports/subscription-report-${Date.now()}.${format}`
    }
  });
});

/**
 * Get subscription plans
 */
const getSubscriptionPlans = asyncHandler(async (req, res) => {
  // Get pricing plans from database
  const plans = await prisma.pricingPlan.findMany({
    where: { isActive: true },
    orderBy: { orderIndex: 'asc' }
  });

  res.status(200).json({
    success: true,
    data: plans
  });
});

/**
 * Update subscription plan
 */
const updateSubscriptionPlan = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const planData = req.body;

  const plan = await prisma.pricingPlan.findUnique({
    where: { id }
  });

  if (!plan) {
    return res.status(404).json({
      success: false,
      message: 'Subscription plan not found'
    });
  }

  const updatedPlan = await prisma.pricingPlan.update({
    where: { id },
    data: planData
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Subscription Plan Updated',
      details: `Subscription plan updated: ${updatedPlan.name}`,
      category: 'Billing',
      severity: 'Info',
      entityType: 'PricingPlan',
      entityId: id,
      entityName: updatedPlan.name,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Subscription plan updated successfully',
    data: updatedPlan
  });
});

/**
 * Create subscription plan
 */
const createSubscriptionPlan = asyncHandler(async (req, res) => {
  const { name, price, billingCycle, features, description } = req.body;

  // Validate required fields
  if (!name || !price || !billingCycle) {
    return res.status(400).json({
      success: false,
      message: 'Name, price, and billing cycle are required'
    });
  }

  // In a real implementation, this would create a plan in the billing system
  const planId = `plan_${Date.now()}`;
  
  const newPlan = {
    id: planId,
    name,
    price,
    billingCycle,
    features: features || [],
    description: description || '',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Subscription Plan Created',
      details: `New subscription plan created: ${name} - $${price}/${billingCycle}`,
      category: 'Billing',
      severity: 'Info',
      entityType: 'PricingPlan',
      entityId: planId,
      entityName: name,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(201).json({
    success: true,
    message: 'Subscription plan created successfully',
    data: newPlan
  });
});

/**
 * Delete subscription plan
 */
const deleteSubscriptionPlan = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // In a real implementation, this would delete/deactivate the plan in the billing system
  // For now, we'll just simulate the deletion
  
  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Subscription Plan Deleted',
      details: `Subscription plan deleted: ${id}`,
      category: 'Billing',
      severity: 'Important',
      entityType: 'PricingPlan',
      entityId: id,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Subscription plan deleted successfully',
    data: {
      id,
      deletedAt: new Date()
    }
  });
});

module.exports = {
  getSubscriptionStats,
  getSubscriptions,
  getSubscriptionById,
  updateSubscription,
  cancelSubscription,
  getPaymentHistory,
  generateSubscriptionReport,
  getSubscriptionPlans,
  updateSubscriptionPlan,
  createSubscription,
  createSubscriptionPlan,
  deleteSubscriptionPlan
};
