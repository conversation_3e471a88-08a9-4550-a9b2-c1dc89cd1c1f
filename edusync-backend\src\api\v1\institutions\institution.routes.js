const express = require('express');
const router = express.Router();
const institutionController = require('./institution.controller');
const settingsRoutes = require('./settings.routes');
const { authenticate } = require('../../../middleware/authenticate');
const validateRequest = require('../../../middleware/validateRequest');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Ensure uploads directory exists
const uploadsDir = path.join(process.cwd(), 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Setup multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    // Accept images only
    if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
      return cb(new Error('Only image files are allowed!'), false);
    }
    cb(null, true);
  }
});

// Multer error handling middleware
const handleMulterError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        status: 'error',
        message: 'File too large. Maximum size is 5MB.'
      });
    }
  } else if (err) {
    return res.status(400).json({
      status: 'error',
      message: err.message || 'File upload error'
    });
  }
  next();
};

// Institution registration - public route, no authentication required
router.post('/register', upload.single('logo'), handleMulterError, institutionController.registerInstitution);

// Email verification
router.get('/verify-email/:token', institutionController.verifyEmail);

// Protected routes - require authentication
router.get('/', authenticate, institutionController.getAllInstitutions);
router.get('/:id', authenticate, institutionController.getInstitution);
router.patch('/:id', authenticate, upload.single('logo'), institutionController.updateInstitution);
router.delete('/:id', authenticate, institutionController.deleteInstitution);

// Settings routes
router.use('/', settingsRoutes);

module.exports = router;
