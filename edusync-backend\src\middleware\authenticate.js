const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');
const AppError = require('../utils/appError');
const config = require('../config');

const prisma = new PrismaClient();

/**
 * Middleware to authenticate requests using JWT
 */
exports.authenticate = async (req, res, next) => {
  try {
    console.log('🔍 Authenticate Middleware:');
    console.log('  - Method:', req.method);
    console.log('  - Path:', req.path);
    console.log('  - Headers:', Object.keys(req.headers));

    // Get token from authorization header
    const authHeader = req.headers.authorization;
    let token;

    console.log('  - Auth header present:', !!authHeader);
    console.log('  - Auth header value:', authHeader ? authHeader.substring(0, 20) + '...' : 'none');

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
    }

    if (!token) {
      console.log('❌ Authenticate: No token found');
      return next(new AppError('Not authenticated. Please log in.', 401));
    }

    console.log('  - Token found:', token ? token.substring(0, 20) + '...' : 'none');

    // Verify token
    const decoded = jwt.verify(token, config.jwt.access_secret);
    console.log('  - Token decoded successfully');
    console.log('  - Decoded user ID:', decoded.userId);

    // Check if user exists and get their institution/school associations
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: {
        institutions: {
          where: { isActive: true },
          select: {
            institutionId: true,
            role: true,
            institution: {
              select: {
                id: true,
                name: true,
                isActive: true
              }
            }
          }
        },
        schools: {
          where: { isActive: true },
          select: {
            schoolId: true,
            role: true,
            school: {
              select: {
                id: true,
                name: true,
                institutionId: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      console.log('❌ Authenticate: User not found in database');
      return next(new AppError('The user belonging to this token no longer exists.', 401));
    }

    console.log('  - User found:', user.email);
    console.log('  - User role:', user.role);
    console.log('  - User active:', user.isActive);
    console.log('  - User institutions:', user.institutions.length);
    console.log('  - User schools:', user.schools.length);

    // Check if user is active
    if (!user.isActive) {
      console.log('❌ Authenticate: User account deactivated');
      return next(new AppError('Your account has been deactivated. Please contact support.', 403));
    }

    // Determine primary institution and school
    let primaryInstitutionId = null;
    let primarySchoolId = null;

    // For users with institution associations, use the first active one
    if (user.institutions.length > 0) {
      primaryInstitutionId = user.institutions[0].institutionId;
    }

    // For users with school associations, use the first active one
    if (user.schools.length > 0) {
      primarySchoolId = user.schools[0].schoolId;
      // If no institution ID yet, get it from the school
      if (!primaryInstitutionId) {
        primaryInstitutionId = user.schools[0].school.institutionId;
      }
    }

    console.log('  - Primary institution ID:', primaryInstitutionId);
    console.log('  - Primary school ID:', primarySchoolId);

    // Attach user to request with enhanced context
    req.user = {
      id: user.id,
      email: user.email,
      role: user.role,
      isEmailVerified: user.isEmailVerified,
      institutionId: primaryInstitutionId,
      schoolId: primarySchoolId,
      institutions: user.institutions,
      schools: user.schools,
    };

    console.log('✅ Authenticate: User attached to request');
    console.log('  - Final user object:', {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role
    });

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return next(new AppError('Invalid token. Please log in again.', 401));
    }
    if (error.name === 'TokenExpiredError') {
      return next(new AppError('Your token has expired. Please log in again.', 401));
    }
    next(error);
  }
};

/**
 * Middleware to restrict access based on user roles
 * @param {Array} roles - Array of allowed roles
 */
exports.restrictTo = (...roles) => {
  return (req, res, next) => {
    console.log('🔍 RestrictTo Middleware:');
    console.log('  - Required roles:', roles);
    console.log('  - User exists:', !!req.user);
    console.log('  - User role:', req.user?.role);
    console.log('  - User ID:', req.user?.id);
    console.log('  - Request method:', req.method);
    console.log('  - Request path:', req.path);

    if (!req.user) {
      console.log('❌ RestrictTo: No user found');
      return next(new AppError('Not authenticated. Please log in.', 401));
    }

    if (!roles.includes(req.user.role)) {
      console.log('❌ RestrictTo: Role not allowed');
      console.log('  - User role:', req.user.role);
      console.log('  - Allowed roles:', roles);
      return next(new AppError('You do not have permission to perform this action.', 403));
    }

    console.log('✅ RestrictTo: Access granted');
    next();
  };
};

/**
 * Middleware to check if email is verified
 */
exports.requireEmailVerified = (req, res, next) => {
  if (!req.user.isEmailVerified) {
    return next(new AppError('Please verify your email address to access this resource.', 403));
  }
  next();
};
