"use client"

import { useState, useEffect, useRef } from "react"
import { motion, useInView } from "framer-motion"
import { Users, BookOpen, School } from "lucide-react"

export function StatisticsSection() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const [counts, setCounts] = useState({ schools: 0, students: 0, teachers: 0, courses: 0 })

  const targetCounts = { schools: 250, students: 50000, teachers: 5000, courses: 1200 }

  useEffect(() => {
    if (isInView) {
      const duration = 2000 // ms
      const frameDuration = 1000 / 60 // 60fps
      const totalFrames = Math.round(duration / frameDuration)

      let frame = 0
      const counter = setInterval(() => {
        frame++
        const progress = frame / totalFrames

        setCounts({
          schools: Math.floor(progress * targetCounts.schools),
          students: Math.floor(progress * targetCounts.students),
          teachers: Math.floor(progress * targetCounts.teachers),
          courses: Math.floor(progress * targetCounts.courses),
        })

        if (frame === totalFrames) {
          clearInterval(counter)
        }
      }, frameDuration)

      return () => clearInterval(counter)
    }
  }, [isInView])

  const stats = [
    { name: "Schools", value: counts.schools, icon: School, suffix: "+" },
    { name: "Students", value: counts.students.toLocaleString(), icon: Users, suffix: "+" },
    { name: "Teachers", value: counts.teachers.toLocaleString(), icon: Users, suffix: "+" },
    { name: "Courses", value: counts.courses, icon: BookOpen, suffix: "+" },
  ]

  return (
    <section ref={ref} className="py-20 bg-emerald-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900">Trusted by Schools Across Zambia</h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Join hundreds of educational institutions already using our platform
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white rounded-xl p-6 shadow-md text-center hover:shadow-lg transition-shadow"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-emerald-100 text-emerald-600 mb-4">
                {stat.icon && <stat.icon className="h-8 w-8" />}
              </div>
              <h3 className="text-4xl font-bold text-gray-900 mb-2">
                {stat.value}
                {stat.suffix}
              </h3>
              <p className="text-gray-600">{stat.name}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
