const express = require('express');
const router = express.Router();
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const {
  getAuditLogs,
  getAuditLogById,
  getAuditLogsByCategory,
  exportAuditLogs,
  getAuditStats
} = require('./audit-logs.controller');

// Apply authentication and super admin role requirement to all routes
router.use(authenticate);
router.use(restrictTo('SUPER_ADMIN'));

// Audit logs routes
router.get('/', getAuditLogs);
router.get('/stats', getAuditStats);
router.get('/export', exportAuditLogs);
router.get('/category/:category', getAuditLogsByCategory);
router.get('/:id', getAuditLogById);

module.exports = router;
