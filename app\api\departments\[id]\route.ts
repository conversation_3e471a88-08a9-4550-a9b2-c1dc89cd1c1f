import { NextRequest, NextResponse } from "next/server"
import { authenticateUser, createAuthHead<PERSON> } from "@/lib/auth"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function GET(
  request: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await authenticateUser(request)
    
    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error },
        { status: auth.status }
      )
    }

    const { id } = await params

    const response = await fetch(`${BACKEND_URL}/api/v1/departments/${id}`, {
      method: "GET",
      headers: await createAuthHeaders(request),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Failed to fetch department" },
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Department API error:", error)
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await authenticateUser(request)

    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error },
        { status: auth.status }
      )
    }

    const { id } = await params
    const body = await request.json()

    const response = await fetch(`${BACKEND_URL}/api/v1/departments/${id}`, {
      method: "PUT",
      headers: await createAuthHeaders(request),
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Failed to update department" },
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Update department API error:", error)
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await authenticateUser(request)

    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error },
        { status: auth.status }
      )
    }

    const { id } = await params

    const response = await fetch(`${BACKEND_URL}/api/v1/departments/${id}`, {
      method: "DELETE",
      headers: await createAuthHeaders(request),
    })

    if (!response.ok) {
      const data = await response.json()
      return NextResponse.json(
        { success: false, message: data.message || "Failed to delete department" },
        { status: response.status }
      )
    }

    return NextResponse.json({ success: true, message: "Department deleted successfully" })
  } catch (error) {
    console.error("Delete department API error:", error)
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    )
  }
}
