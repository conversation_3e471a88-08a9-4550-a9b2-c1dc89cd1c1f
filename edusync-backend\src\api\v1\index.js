const express = require('express');
const router = express.Router();

// Debug middleware to track all requests to v1 API
router.use((req, res, next) => {
  console.log('🔍🔍🔍 V1 ROUTER HIT');
  console.log('  - Method:', req.method);
  console.log('  - Path:', req.path);
  console.log('  - URL:', req.url);
  console.log('  - Original URL:', req.originalUrl);
  next();
});
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Import route modules
const authRoutes = require('./auth/auth.routes');
const userRoutes = require('./users/user.routes');
const institutionRoutes = require('./institutions/institution.routes');
const schoolRoutes = require('./schools/school.routes');

// Import dashboard route modules
const dashboardRoutes = require('./dashboard/dashboard.routes');
const studentsRoutes = require('./students/students.routes');
const teachersRoutes = require('./teachers/teachers.routes');
const staffRoutes = require('./staff/staff.routes');
const classesRoutes = require('./classes');
const academicYearsRoutes = require('./academic-years/academic-years.routes');
const subjectsRoutes = require('./subjects/subjects.routes');
const departmentsRoutes = require('./departments/departments.routes');
const gradesRoutes = require('./grades/grades.routes');
const attendanceRoutes = require('./attendance/attendance.routes');
const feesRoutes = require('./fees/fees.routes');
const examsRoutes = require('./exams/exams.routes');

// Import super-admin route modules
const superAdminDashboardRoutes = require('./super-admin/dashboard.routes');
const superAdminUsersRoutes = require('./super-admin/users.routes');
const superAdminInstitutionsRoutes = require('./super-admin/institutions.routes');
const superAdminAnalyticsRoutes = require('./super-admin/analytics.routes');
const superAdminAuditLogsRoutes = require('./super-admin/audit-logs.routes');
const superAdminSecurityRoutes = require('./super-admin/security.routes');
const superAdminSettingsRoutes = require('./super-admin/settings.routes');
const superAdminSupportRoutes = require('./super-admin/support.routes');
const superAdminSubscriptionsRoutes = require('./super-admin/subscriptions.routes');
const superAdminContentRoutes = require('./super-admin/content.routes');

// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    // Test database connection
    await prisma.$queryRaw`SELECT 1`;

    res.status(200).json({
      status: 'success',
      message: 'Backend is healthy',
      timestamp: new Date().toISOString(),
      database: 'connected',
      version: '1.0.0'
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Backend health check failed',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error.message
    });
  }
});

// Register routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/institutions', institutionRoutes);
router.use('/schools', schoolRoutes);

// Register dashboard routes
router.use('/dashboard', dashboardRoutes);
router.use('/students', studentsRoutes);
router.use('/teachers', teachersRoutes);
router.use('/staff', staffRoutes);
router.use('/classes', classesRoutes);
router.use('/academic-years', academicYearsRoutes);
router.use('/subjects', subjectsRoutes);
router.use('/departments', departmentsRoutes);
router.use('/grades', gradesRoutes);
router.use('/attendance', attendanceRoutes);
router.use('/fees', feesRoutes);
router.use('/exams', examsRoutes);

// Register super-admin routes
router.use('/super-admin/dashboard', superAdminDashboardRoutes);
router.use('/super-admin/users', superAdminUsersRoutes);
router.use('/super-admin/institutions', superAdminInstitutionsRoutes);
router.use('/super-admin/analytics', superAdminAnalyticsRoutes);
router.use('/super-admin/audit-logs', superAdminAuditLogsRoutes);
router.use('/super-admin/security', superAdminSecurityRoutes);
router.use('/super-admin/settings', superAdminSettingsRoutes);
router.use('/super-admin/support', superAdminSupportRoutes);
router.use('/super-admin/subscriptions', superAdminSubscriptionsRoutes);
router.use('/super-admin/content', superAdminContentRoutes);

module.exports = router;
