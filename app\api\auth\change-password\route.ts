import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    // Get auth token from cookies or headers
    const authToken =
      request.cookies.get("accessToken")?.value || request.headers.get("authorization")?.replace("Bearer ", "")

    if (!authToken) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    // Get the request body
    const { currentPassword, newPassword } = await request.json()

    if (!currentPassword || !newPassword) {
      return NextResponse.json({ error: "Current password and new password are required" }, { status: 400 })
    }

    // Forward request to Flask backend
    const backendUrl = process.env.BACKEND_URL || "http://localhost:4000"
    const response = await fetch(`${backendUrl}/api/v1/auth/change-password`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        currentPassword,
        newPassword,
      }),
    })

    if (!response.ok) {
      if (response.status === 401) {
        return NextResponse.json({ error: "Invalid current password" }, { status: 401 })
      }
      if (response.status === 400) {
        const errorData = await response.json()
        return NextResponse.json({ error: errorData.message || "Invalid data" }, { status: 400 })
      }
      throw new Error("Failed to change password")
    }

    const result = await response.json()
    return NextResponse.json(result)
  } catch (error) {
    console.error("Error changing password:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
