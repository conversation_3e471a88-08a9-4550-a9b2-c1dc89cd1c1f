import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:4000';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Settings API: Starting request processing');
    console.log('🔍 Settings API: BACKEND_URL:', BACKEND_URL);

    const cookieStore = await cookies();
    const token = cookieStore.get('accessToken')?.value;

    if (!token) {
      console.log('❌ Settings API: No access token found');
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('✅ Settings API: Access token found');

    // Get search parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category') || '';
    const type = searchParams.get('type') || 'general';

    console.log('🔍 Settings API: Request params - category:', category, 'type:', type);

    let endpoint = '/api/v1/super-admin/settings';
    
    // Handle different types of settings requests
    switch (type) {
      case 'app':
        endpoint = '/api/v1/super-admin/settings/app';
        break;
      case 'email':
        endpoint = '/api/v1/super-admin/settings/email';
        break;
      case 'backup':
        endpoint = '/api/v1/super-admin/settings/backup';
        break;
      case 'system-info':
        endpoint = '/api/v1/super-admin/settings/system-info';
        break;
      default:
        endpoint = '/api/v1/super-admin/settings';
        if (category) {
          endpoint += `?category=${category}`;
        }
    }

    console.log(`🔍 Settings API: Fetching from ${BACKEND_URL}${endpoint}`);

    const response = await fetch(`${BACKEND_URL}${endpoint}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    console.log(`📡 Settings API: Response status: ${response.status}`);

    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorData = await response.json();
        console.error('❌ Settings API: Backend error response:', errorData);
        errorMessage = errorData.message || errorData.error || errorMessage;
      } catch (parseError) {
        console.error('❌ Settings API: Could not parse error response:', parseError);
      }
      throw new Error(errorMessage);
    }

    const data = await response.json();
    console.log('✅ Settings API: Success response received');
    return NextResponse.json(data);

  } catch (error) {
    console.error('❌ Settings API error:', error);

    // Check if it's a connection error (backend not running)
    if (error instanceof Error && (
      error.message.includes('ECONNREFUSED') ||
      error.message.includes('fetch failed') ||
      error.message.includes('Failed to fetch')
    )) {
      console.error('❌ Settings API: Backend server appears to be down');

      // Return mock data when backend is not available
      const mockSettings = {
        success: true,
        data: {
          general: {
            appName: 'EduSync',
            appVersion: '1.0.0',
            maintenanceMode: false,
            allowRegistration: false,
            maxFileUploadSize: 10485760,
            sessionTimeout: 3600,
            defaultLanguage: 'en',
            timezone: 'UTC',
            dateFormat: 'DD/MM/YYYY',
            timeFormat: '24h'
          },
          email: {
            smtpHost: '',
            smtpPort: 587,
            smtpSecure: false,
            smtpUser: '',
            smtpPassword: '',
            fromEmail: '',
            fromName: 'EduSync',
            enableEmailNotifications: true
          },
          notifications: {
            emailNotifications: true,
            pushNotifications: false,
            smsNotifications: false,
            systemAlertNotifications: true,
            securityAlertNotifications: true,
            dailyReportEnabled: true,
            weeklyReportEnabled: true,
            monthlyReportEnabled: false
          },
          security: {
            enforceSSL: true,
            enableTwoFactor: false,
            sessionTimeout: 30,
            passwordMinLength: 8,
            passwordRequireSpecialChar: true,
            passwordRequireNumber: true,
            passwordRequireUppercase: true,
            maxLoginAttempts: 5,
            lockoutDuration: 15,
            enableAuditLog: true
          }
        }
      };

      console.log('🔄 Settings API: Returning mock data due to backend unavailability');
      return NextResponse.json(mockSettings);
    }

    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch settings',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get('accessToken')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'general';

    let endpoint = '/api/v1/super-admin/settings';
    
    // Handle different types of settings updates
    switch (type) {
      case 'app':
        endpoint = '/api/v1/super-admin/settings/app';
        break;
      case 'email':
        endpoint = '/api/v1/super-admin/settings/email';
        break;
      case 'backup':
        endpoint = '/api/v1/super-admin/settings/backup';
        break;
      default:
        endpoint = '/api/v1/super-admin/settings';
    }

    const response = await fetch(`${BACKEND_URL}${endpoint}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Update settings API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to update settings',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get('accessToken')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || '';

    let endpoint = '/api/v1/super-admin/settings';
    
    // Handle different actions
    switch (action) {
      case 'test-email':
        endpoint = '/api/v1/super-admin/settings/email/test';
        break;
      case 'trigger-backup':
        endpoint = '/api/v1/super-admin/settings/backup/trigger';
        break;
      default:
        endpoint = '/api/v1/super-admin/settings';
    }

    const response = await fetch(`${BACKEND_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Settings action API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to perform action',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}