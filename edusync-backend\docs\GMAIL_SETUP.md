# Gmail Email Configuration Guide

This guide will help you configure Gmail for sending emails from the EduSync application.

## Prerequisites

1. A Gmail account
2. Two-Factor Authentication (2FA) enabled on your Gmail account

## Step 1: Enable Two-Factor Authentication

1. Go to your [Google Account settings](https://myaccount.google.com/)
2. Navigate to "Security" in the left sidebar
3. Under "Signing in to Google", click on "2-Step Verification"
4. Follow the prompts to enable 2FA

## Step 2: Generate an App Password

1. Once 2FA is enabled, go back to the "Security" section
2. Under "Signing in to Google", click on "App passwords"
3. You may need to sign in again
4. In the "Select app" dropdown, choose "Mail"
5. In the "Select device" dropdown, choose "Other (custom name)"
6. Enter "EduSync" as the custom name
7. Click "Generate"
8. Copy the 16-character password that appears (it will look like: `abcd efgh ijkl mnop`)

## Step 3: Update Your .env File

Update your backend `.env` file with the following Gmail settings:

```env
# Email Settings for Gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password-here
EMAIL_FROM=<EMAIL>
```

**Important Notes:**
- Use your Gmail email address for both `EMAIL_USER` and `EMAIL_FROM`
- Use the 16-character App Password (not your regular Gmail password) for `EMAIL_PASSWORD`
- Remove spaces from the App Password when entering it in the .env file

## Step 4: Test Your Configuration

Run the email test script to verify your configuration:

```bash
cd backend
npm run test-email
```

Or test with a specific email address:

```bash
npm run test-email <EMAIL>
```

## Troubleshooting

### Common Issues and Solutions

1. **Authentication Error (535)**
   - Make sure you're using the App Password, not your regular Gmail password
   - Verify that 2FA is enabled on your Gmail account
   - Double-check that the App Password is entered correctly (no spaces)

2. **Connection Timeout**
   - Check your network connection
   - Verify that port 587 is not blocked by your firewall
   - Try using port 465 with `secure: true` if 587 doesn't work

3. **"Less Secure Apps" Error**
   - This error occurs when trying to use your regular password
   - Always use App Passwords instead of enabling "Less secure app access"

4. **Rate Limiting**
   - Gmail has sending limits (500 emails per day for free accounts)
   - For production use, consider using a dedicated email service like SendGrid, Mailgun, or Amazon SES

### Alternative Configuration (Port 465)

If port 587 doesn't work, try this configuration in your .env file:

```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=465
EMAIL_USE_TLS=False
```

And update the emailService.js to use `secure: true` for port 465.

## Security Best Practices

1. **Never commit your App Password to version control**
2. **Use environment variables for all sensitive information**
3. **Regularly rotate your App Passwords**
4. **Consider using a dedicated email service for production**

## Production Considerations

For production environments, consider:

1. **Using a dedicated email service** (SendGrid, Mailgun, Amazon SES)
2. **Setting up SPF, DKIM, and DMARC records** for better deliverability
3. **Monitoring email delivery rates and bounce rates**
4. **Implementing email queuing** for high-volume applications

## Testing Email Templates

You can test individual email templates by creating test functions:

```javascript
// Test password reset email
const { sendPasswordResetEmail } = require('./src/services/emailService');
sendPasswordResetEmail('<EMAIL>', 'test-token-123');

// Test verification email
const { sendVerificationEmail } = require('./src/services/emailService');
sendVerificationEmail('<EMAIL>', 'test-token-456');
```

## Support

If you continue to have issues:

1. Check the application logs for detailed error messages
2. Verify your Gmail account settings
3. Test with a different email address
4. Consider using a different email provider for testing

For Gmail-specific issues, refer to the [Gmail SMTP documentation](https://support.google.com/mail/answer/7126229).
