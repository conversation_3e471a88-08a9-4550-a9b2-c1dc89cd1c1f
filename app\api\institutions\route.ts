import { NextResponse } from "next/server"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    // Forward the request to the backend API
    const response = await fetch(`${BACKEND_URL}/api/v1/institutions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: request.headers.get("authorization") || "",
        Cookie: request.headers.get("cookie") || "",
      },
      body: JSON.stringify(body),
    })

    const responseData = await response.json()

    if (!response.ok) {
      return NextResponse.json(responseData, { status: response.status })
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error("Institutions API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    // Build query string
    const queryString = new URLSearchParams(queryParams).toString()
    
    // Forward the request to the backend API
    const response = await fetch(`${BACKEND_URL}/api/v1/institutions${queryString ? `?${queryString}` : ''}`, {
      headers: {
        Authorization: request.headers.get("authorization") || "",
        Cookie: request.headers.get("cookie") || "",
      },
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Institutions API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
