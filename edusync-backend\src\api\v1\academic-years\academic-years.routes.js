const express = require('express');
const { authenticate } = require('../../../middleware/authenticate');
const { authorize } = require('../../../middleware/authorization');
const {
  getAcademicYears,
  getAcademicYearById,
  createAcademicYear,
  updateAcademicYear,
  deleteAcademicYear,
  setActiveAcademicYear
} = require('./academic-years.controller');

const router = express.Router();

// Get all academic years
router.get('/', authenticate, getAcademicYears);

// Get academic year by ID
router.get('/:id', authenticate, getAcademicYearById);

// Create new academic year
router.post('/', authenticate, authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN']), createAcademicYear);

// Update academic year
router.patch('/:id', authenticate, authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN']), updateAcademicYear);

// Delete academic year
router.delete('/:id', authenticate, authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN']), deleteAcademicYear);

// Set academic year as active
router.patch('/:id/activate', authenticate, authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN']), setActiveAcademicYear);

module.exports = router;
