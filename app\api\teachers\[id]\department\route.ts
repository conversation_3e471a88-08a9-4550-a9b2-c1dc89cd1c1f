import { NextRequest, NextResponse } from "next/server"
import { authenticateUser, createAuthHeaders } from "@/lib/auth-utils"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await authenticateUser(request)

    if (!auth.user) {
      return NextResponse.json(
        { success: false, message: auth.error },
        { status: auth.status }
      )
    }

    const { id } = await params

    const response = await fetch(`${BACKEND_URL}/api/v1/teachers/${id}/department`, {
      method: "DELETE",
      headers: await createAuthHeaders(request),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || "Failed to remove department from teacher" },
        { status: response.status }
      )
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Teacher department removal API error:", error)
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    )
  }
}
