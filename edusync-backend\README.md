# School Management System - Backend API

This is the backend API for a multi-tenant SaaS School Management System. It provides comprehensive user authentication and school management functionality.

## Features

- Multi-tenant architecture
- User authentication and authorization
- Role-based access control
- Institution and school management
- Email verification
- Password reset functionality
- Refresh token mechanism
- Audit logging
- Rate limiting

## Tech Stack

- Node.js
- Express.js
- MySQL
- Prisma ORM
- JWT for authentication
- bcrypt for password hashing
- Nodemailer for email functionality
- Winston for logging

## API Structure

The API follows a versioned structure:

```
/api/v1/auth - Authentication endpoints
/api/v1/users - User management
/api/v1/institutions - Institution management
/api/v1/schools - School management
```

## Getting Started

### Prerequisites

- Node.js (v14+)
- MySQL database

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Set up your environment variables in a `.env` file:
   ```
   NODE_ENV=development
   PORT=4000
   DATABASE_URL="mysql://username:password@host:port/database"

   # Secret Keys
   SECRET_KEY=your_secret_key
   JWT_ACCESS_SECRET=your_jwt_secret
   JWT_REFRESH_SECRET=your_jwt_refresh_secret
   JWT_ACCESS_EXPIRATION=15m
   JWT_REFRESH_EXPIRATION=7d

   # MySQL database credentials
   MYSQL_USER=your_mysql_user
   MYSQL_PASSWORD=your_mysql_password
   MYSQL_HOST=your_mysql_host
   MYSQL_DB=your_mysql_database
   MYSQL_PORT=3306

   # Email Settings
   EMAIL_HOST=smtp.example.com
   EMAIL_PORT=587
   EMAIL_USE_TLS=True
   EMAIL_USER=<EMAIL>
   EMAIL_PASSWORD=your_email_password
   EMAIL_FROM=<EMAIL>

   # Frontend URL
   FRONTEND_URL=http://localhost:3000
   ```
4. Generate Prisma client:
   ```
   npm run prisma:generate
   ```
5. Run database migrations:
   ```
   npm run prisma:migrate
   ```
6. Initialize the database with default data:
   ```
   npm run init:db
   ```
7. Start the development server:
   ```
   npm run dev
   ```

## Default Login Credentials

After running the database initialization, the following users will be created:

- Super Admin:
  - Email: <EMAIL>
  - Password: Admin@123

- Institution Admin:
  - Email: <EMAIL>
  - Password: Admin@123

- School Admin:
  - Email: <EMAIL>
  - Password: Admin@123

## Database Schema

The database schema includes the following main entities:

- User
- Institution
- School
- InstitutionUser (junction table)
- SchoolUser (junction table)
- RefreshToken
- AuditLog

## Authentication Flow

1. User registers via `/api/v1/auth/register`
2. User receives email verification link
3. After verification, user can log in via `/api/v1/auth/login`
4. Login returns JWT access token and refresh token
5. Access token is used for API requests
6. When access token expires, refresh token can be used to get a new one

## Contributing

1. Create a feature branch
2. Make your changes
3. Submit a pull request

## License

This project is licensed under the MIT License.
