import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const refreshToken = cookieStore.get("refreshToken")?.value

    if (!refreshToken) {
      return NextResponse.json(
        { error: "No refresh token available" },
        { status: 401 }
      )
    }

    // Call backend refresh endpoint
    const response = await fetch(`${BACKEND_URL}/api/v1/auth/refresh-token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Cookie: `refreshToken=${refreshToken}`,
      },
    })

    const data = await response.json()

    if (!response.ok) {
      // Clear invalid refresh token
      cookieStore.delete("refreshToken")
      cookieStore.delete("accessToken")
      cookieStore.delete("session")
      
      return NextResponse.json(
        { error: "Failed to refresh token" },
        { status: 401 }
      )
    }

    // Store new access token
    const newAccessToken = data.data?.accessToken
    if (newAccessToken) {
      cookieStore.set("accessToken", newAccessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 15 * 60, // 15 minutes
      })
    }

    return NextResponse.json({
      success: true,
      accessToken: newAccessToken,
    })
  } catch (error) {
    console.error("Token refresh error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
