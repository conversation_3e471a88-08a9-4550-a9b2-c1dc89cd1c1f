import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function POST(request: NextRequest) {
  console.log("🔄 Refresh API: Starting token refresh...")

  try {
    const cookieStore = await cookies()
    const refreshToken = cookieStore.get("refreshToken")?.value

    if (!refreshToken) {
      console.log("❌ Refresh API: No refresh token available")
      return NextResponse.json(
        {
          success: false,
          error: "No refresh token available",
          code: "NO_REFRESH_TOKEN"
        },
        { status: 401 }
      )
    }

    console.log("🔄 Refresh API: Calling backend refresh endpoint...")
    // Call backend refresh endpoint
    const response = await fetch(`${BACKEND_URL}/api/v1/auth/refresh-token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Cookie: `refreshToken=${refreshToken}`,
      },
      signal: AbortSignal.timeout(10000), // 10 second timeout
    })

    const data = await response.json()

    if (!response.ok) {
      console.error("❌ Refresh API: Backend refresh failed:", response.status, data)

      // Clear invalid refresh token and related cookies
      cookieStore.delete("refreshToken")
      cookieStore.delete("accessToken")
      cookieStore.delete("session")

      return NextResponse.json(
        {
          success: false,
          error: data.message || "Failed to refresh token",
          code: response.status === 401 ? "REFRESH_TOKEN_EXPIRED" : "REFRESH_FAILED"
        },
        { status: 401 }
      )
    }

    // Store new access token
    const newAccessToken = data.data?.accessToken
    if (!newAccessToken) {
      console.error("❌ Refresh API: No access token in response")
      return NextResponse.json(
        {
          success: false,
          error: "No access token received",
          code: "INVALID_RESPONSE"
        },
        { status: 500 }
      )
    }

    console.log("✅ Refresh API: Setting new access token cookie...")
    cookieStore.set("accessToken", newAccessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 15 * 60, // 15 minutes
    })

    console.log("✅ Refresh API: Token refresh successful")
    return NextResponse.json({
      success: true,
      accessToken: newAccessToken,
      message: "Token refreshed successfully"
    })
  } catch (error) {
    console.error("❌ Refresh API: Unexpected error:", error)

    const errorMessage = error instanceof Error ? error.message : "Unknown error"
    const isNetworkError = errorMessage.includes("fetch") || errorMessage.includes("timeout")

    return NextResponse.json(
      {
        success: false,
        error: isNetworkError ? "Network error during token refresh" : "Internal server error",
        code: "REFRESH_ERROR",
        details: process.env.NODE_ENV === "development" ? errorMessage : undefined
      },
      { status: 500 }
    )
  }
}
