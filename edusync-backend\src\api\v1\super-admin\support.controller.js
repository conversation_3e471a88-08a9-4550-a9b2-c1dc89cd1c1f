const { PrismaClient } = require('@prisma/client');
const asyncHandler = require('../../../utils/asyncHandler');

const prisma = new PrismaClient();

/**
 * Get support tickets with filtering and pagination
 */
const getSupportTickets = asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    status, 
    priority, 
    category, 
    search,
    sortBy = 'createdAt',
    order = 'desc'
  } = req.query;

  const skip = (page - 1) * limit;

  // Build filters
  const where = {};
  if (status) where.status = status;
  if (priority) where.priority = priority;
  if (category) where.category = category;
  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
      { user: { email: { contains: search, mode: 'insensitive' } } }
    ];
  }

  // Get total count
  const total = await prisma.supportTicket.count({ where });

  // Get tickets with user information
  const tickets = await prisma.supportTicket.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true
        }
      },
      assignedTo: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true
        }
      }
    },
    orderBy: {
      [sortBy]: order.toLowerCase()
    },
    skip: parseInt(skip),
    take: parseInt(limit)
  });

  res.status(200).json({
    success: true,
    data: tickets,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / parseInt(limit))
    }
  });
});

/**
 * Get support ticket by ID
 */
const getSupportTicketById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const ticket = await prisma.supportTicket.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true,
          phoneNumber: true
        }
      },
      assignedTo: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true
        }
      },
      messages: {
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              role: true
            }
          }
        },
        orderBy: {
          createdAt: 'asc'
        }
      }
    }
  });

  if (!ticket) {
    return res.status(404).json({
      success: false,
      message: 'Support ticket not found'
    });
  }

  res.status(200).json({
    success: true,
    data: ticket
  });
});

/**
 * Create support ticket
 */
const createSupportTicket = asyncHandler(async (req, res) => {
  const {
    title,
    description,
    category,
    priority = 'Medium',
    userId
  } = req.body;

  const ticket = await prisma.supportTicket.create({
    data: {
      title,
      description,
      category,
      priority,
      status: 'Open',
      userId: userId || req.user.id
    },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true
        }
      }
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Support Ticket Created',
      details: `Support ticket created: ${ticket.title}`,
      category: 'Support',
      severity: 'Info',
      entityType: 'SupportTicket',
      entityId: ticket.id,
      entityName: ticket.title,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(201).json({
    success: true,
    message: 'Support ticket created successfully',
    data: ticket
  });
});

/**
 * Update support ticket
 */
const updateSupportTicket = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    title,
    description,
    category,
    priority,
    status,
    assignedToId
  } = req.body;

  const ticket = await prisma.supportTicket.findUnique({
    where: { id }
  });

  if (!ticket) {
    return res.status(404).json({
      success: false,
      message: 'Support ticket not found'
    });
  }

  const updatedTicket = await prisma.supportTicket.update({
    where: { id },
    data: {
      ...(title && { title }),
      ...(description && { description }),
      ...(category && { category }),
      ...(priority && { priority }),
      ...(status && { status }),
      ...(assignedToId && { assignedToId }),
      updatedAt: new Date()
    },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true
        }
      },
      assignedTo: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true
        }
      }
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Support Ticket Updated',
      details: `Support ticket updated: ${updatedTicket.title}`,
      category: 'Support',
      severity: 'Info',
      entityType: 'SupportTicket',
      entityId: updatedTicket.id,
      entityName: updatedTicket.title,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Support ticket updated successfully',
    data: updatedTicket
  });
});

/**
 * Add message to support ticket
 */
const addTicketMessage = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { message, isInternal = false } = req.body;

  const ticket = await prisma.supportTicket.findUnique({
    where: { id }
  });

  if (!ticket) {
    return res.status(404).json({
      success: false,
      message: 'Support ticket not found'
    });
  }

  const ticketMessage = await prisma.supportTicketMessage.create({
    data: {
      ticketId: id,
      userId: req.user.id,
      message,
      isInternal
    },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true
        }
      }
    }
  });

  // Update ticket's last activity
  await prisma.supportTicket.update({
    where: { id },
    data: {
      updatedAt: new Date()
    }
  });

  res.status(201).json({
    success: true,
    message: 'Message added successfully',
    data: ticketMessage
  });
});

/**
 * Delete support ticket
 */
const deleteSupportTicket = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const ticket = await prisma.supportTicket.findUnique({
    where: { id }
  });

  if (!ticket) {
    return res.status(404).json({
      success: false,
      message: 'Support ticket not found'
    });
  }

  // Delete related messages first
  await prisma.supportTicketMessage.deleteMany({
    where: { ticketId: id }
  });

  // Delete the ticket
  await prisma.supportTicket.delete({
    where: { id }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Support Ticket Deleted',
      details: `Support ticket deleted: ${ticket.title}`,
      category: 'Support',
      severity: 'Important',
      entityType: 'SupportTicket',
      entityId: ticket.id,
      entityName: ticket.title,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Support ticket deleted successfully'
  });
});

/**
 * Get support statistics
 */
const getSupportStats = asyncHandler(async (req, res) => {
  const { period = 30 } = req.query;
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - parseInt(period));

  // Get ticket counts by status
  const statusStats = await prisma.supportTicket.groupBy({
    by: ['status'],
    _count: {
      id: true
    }
  });

  // Get ticket counts by priority
  const priorityStats = await prisma.supportTicket.groupBy({
    by: ['priority'],
    _count: {
      id: true
    }
  });

  // Get recent ticket counts
  const recentTickets = await prisma.supportTicket.count({
    where: {
      createdAt: {
        gte: startDate
      }
    }
  });

  // Get resolution stats
  const resolvedTickets = await prisma.supportTicket.count({
    where: {
      status: 'Resolved',
      updatedAt: {
        gte: startDate
      }
    }
  });

  // Get average response time (mock calculation)
  const avgResponseTime = Math.floor(Math.random() * 24) + 1; // Mock: 1-24 hours

  res.status(200).json({
    success: true,
    data: {
      period: parseInt(period),
      totalTickets: await prisma.supportTicket.count(),
      recentTickets,
      resolvedTickets,
      avgResponseTimeHours: avgResponseTime,
      statusBreakdown: statusStats.reduce((acc, item) => {
        acc[item.status] = item._count.id;
        return acc;
      }, {}),
      priorityBreakdown: priorityStats.reduce((acc, item) => {
        acc[item.priority] = item._count.id;
        return acc;
      }, {})
    }
  });
});

/**
 * Get knowledge base articles
 */
const getKnowledgeBase = asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    category, 
    search,
    isPublished = true 
  } = req.query;

  const skip = (page - 1) * limit;

  // Build filters
  const where = { isPublished: isPublished === 'true' };
  if (category) where.category = category;
  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { content: { contains: search, mode: 'insensitive' } },
      { tags: { hasSome: [search] } }
    ];
  }

  // Get total count
  const total = await prisma.knowledgeBaseArticle.count({ where });

  // Get articles
  const articles = await prisma.knowledgeBaseArticle.findMany({
    where,
    include: {
      author: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true
        }
      }
    },
    orderBy: {
      updatedAt: 'desc'
    },
    skip: parseInt(skip),
    take: parseInt(limit)
  });

  res.status(200).json({
    success: true,
    data: articles,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / parseInt(limit))
    }
  });
});

/**
 * Create knowledge base article
 */
const createKnowledgeBaseArticle = asyncHandler(async (req, res) => {
  const {
    title,
    content,
    category,
    tags = [],
    isPublished = false
  } = req.body;

  const article = await prisma.knowledgeBaseArticle.create({
    data: {
      title,
      content,
      category,
      tags,
      isPublished,
      authorId: req.user.id
    },
    include: {
      author: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true
        }
      }
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Knowledge Base Article Created',
      details: `Knowledge base article created: ${article.title}`,
      category: 'Support',
      severity: 'Info',
      entityType: 'KnowledgeBaseArticle',
      entityId: article.id,
      entityName: article.title,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(201).json({
    success: true,
    message: 'Knowledge base article created successfully',
    data: article
  });
});

/**
 * Update knowledge base article
 */
const updateKnowledgeBaseArticle = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    title,
    content,
    category,
    tags,
    isPublished
  } = req.body;

  const article = await prisma.knowledgeBaseArticle.findUnique({
    where: { id }
  });

  if (!article) {
    return res.status(404).json({
      success: false,
      message: 'Knowledge base article not found'
    });
  }

  const updatedArticle = await prisma.knowledgeBaseArticle.update({
    where: { id },
    data: {
      ...(title && { title }),
      ...(content && { content }),
      ...(category && { category }),
      ...(tags && { tags }),
      ...(isPublished !== undefined && { isPublished }),
      updatedAt: new Date()
    },
    include: {
      author: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true
        }
      }
    }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Knowledge Base Article Updated',
      details: `Knowledge base article updated: ${updatedArticle.title}`,
      category: 'Support',
      severity: 'Info',
      entityType: 'KnowledgeBaseArticle',
      entityId: updatedArticle.id,
      entityName: updatedArticle.title,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Knowledge base article updated successfully',
    data: updatedArticle
  });
});

/**
 * Delete knowledge base article
 */
const deleteKnowledgeBaseArticle = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const article = await prisma.knowledgeBaseArticle.findUnique({
    where: { id }
  });

  if (!article) {
    return res.status(404).json({
      success: false,
      message: 'Knowledge base article not found'
    });
  }

  await prisma.knowledgeBaseArticle.delete({
    where: { id }
  });

  // Create audit log
  await prisma.auditLog.create({
    data: {
      action: 'Knowledge Base Article Deleted',
      details: `Knowledge base article deleted: ${article.title}`,
      category: 'Support',
      severity: 'Important',
      entityType: 'KnowledgeBaseArticle',
      entityId: article.id,
      entityName: article.title,
      userId: req.user.id,
      userEmail: req.user.email,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date()
    }
  });

  res.status(200).json({
    success: true,
    message: 'Knowledge base article deleted successfully'
  });
});

module.exports = {
  getSupportTickets,
  getSupportTicketById,
  createSupportTicket,
  updateSupportTicket,
  addTicketMessage,
  deleteSupportTicket,
  getSupportStats,
  getKnowledgeBase,
  createKnowledgeBaseArticle,
  updateKnowledgeBaseArticle,
  deleteKnowledgeBaseArticle
};
