import { NextResponse } from "next/server"

export async function POST(request: Request, { params }: { params: { id: string } }) {
  try {
    const body = await request.json()

    const response = await fetch(`${process.env.BACKEND_URL}/api/v1/super-admin/schools/${params.id}/reject`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: request.headers.get("Authorization") || "",
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: data.error || "Failed to reject school" }, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("School rejection API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
