import type { NextRequest } from "next/server"

export async function GET(request: NextRequest) {
  try {
    // Get auth token from cookies or headers
    const authToken =
      request.cookies.get("accessToken")?.value || request.headers.get("authorization")?.replace("Bearer ", "")

    if (!authToken) {
      return new Response("Authentication required", { status: 401 })
    }

    // Create SSE stream
    const stream = new ReadableStream({
      start(controller) {
        // Send initial connection message
        controller.enqueue(`data: ${JSON.stringify({ type: "connected" })}\n\n`)

        // Set up interval to keep connection alive
        const keepAlive = setInterval(() => {
          controller.enqueue(`data: ${JSON.stringify({ type: "ping" })}\n\n`)
        }, 30000) // 30 seconds

        // Clean up on close
        request.signal.addEventListener("abort", () => {
          clearInterval(keepAlive)
          controller.close()
        })
      },
    })

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      },
    })
  } catch (error) {
    console.error("Error setting up notification stream:", error)
    return new Response("Internal server error", { status: 500 })
  }
}
