const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createInstitution() {
  const institution = await prisma.institution.create({
    data: {
      name: 'Test Institution',
      email: '<EMAIL>',
      logo: null,
      subscriptionStatus: 'TRIAL',
      subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      isActive: true,
      address: '123 Test Street',
      city: 'Test City',
      state: 'Test State',
      country: 'Test Country',
      phoneNumber: '+1234567890',
      website: 'https://testinstitution.com'
    }
  });
  
  console.log('Created test institution:', institution.name);
  return institution;
}

async function createUser(userData, institutionId = null) {
  const passwordHash = await bcrypt.hash(userData.password, 12);
  
  const user = await prisma.user.create({
    data: {
      email: userData.email,
      passwordHash,
      firstName: userData.firstName,
      lastName: userData.lastName,
      role: userData.role,
      isEmailVerified: true,
      isActive: true
    }
  });

  // Associate user with institution if provided
  if (institutionId && userData.role !== 'SUPER_ADMIN') {
    await prisma.institutionUser.create({
      data: {
        userId: user.id,
        institutionId: institutionId,
        role: userData.role,
      }
    });
  }

  // Create role-specific profile
  switch (userData.role) {
    case 'TEACHER':
      await prisma.teacher.create({
        data: {
          userId: user.id,
          employeeId: `T${Math.floor(Math.random() * 10000)}`,
          qualification: 'Master\'s Degree',
          experience: 5,
          institutionId: institutionId
        }
      });
      break;
    case 'STUDENT':
      await prisma.student.create({
        data: {
          userId: user.id,
          admissionNumber: `S${Math.floor(Math.random() * 10000)}`,
          studentStatus: 'ACTIVE',
          institutionId: institutionId
        }
      });
      break;
    case 'PARENT':
      await prisma.parent.create({
        data: {
          userId: user.id,
          occupation: 'Professional'
        }
      });
      break;
    case 'STAFF':
      await prisma.staff.create({
        data: {
          userId: user.id,
          employeeId: `ST${Math.floor(Math.random() * 10000)}`,
          jobTitle: 'Administrator',
          institutionId: institutionId
        }
      });
      break;
  }

  return user;
}

async function createTestUsers() {
  try {
    // Create test institution first
    const institution = await createInstitution();
    
    const users = [
      {
        email: '<EMAIL>',
        password: 'SuperAdmin123!',
        firstName: 'Super',
        lastName: 'Admin',
        role: 'SUPER_ADMIN'
      },
      {
        email: '<EMAIL>',
        password: 'InstAdmin123!',
        firstName: 'Institution',
        lastName: 'Admin',
        role: 'INSTITUTION_ADMIN'
      },
      {
        email: '<EMAIL>',
        password: 'SchoolAdmin123!',
        firstName: 'School',
        lastName: 'Admin',
        role: 'SCHOOL_ADMIN'
      },
      {
        email: '<EMAIL>',
        password: 'Teacher123!',
        firstName: 'Test',
        lastName: 'Teacher',
        role: 'TEACHER'
      },
      {
        email: '<EMAIL>',
        password: 'Student123!',
        firstName: 'Test',
        lastName: 'Student',
        role: 'STUDENT'
      },
      {
        email: '<EMAIL>',
        password: 'Parent123!',
        firstName: 'Test',
        lastName: 'Parent',
        role: 'PARENT'
      },
      {
        email: '<EMAIL>',
        password: 'Staff123!',
        firstName: 'Test',
        lastName: 'Staff',
        role: 'STAFF'
      }
    ];

    for (const userData of users) {
      const user = await createUser(userData, institution.id);
      console.log(`Created ${userData.role} user:`, user.email);
    }

    console.log('All test users created successfully!');
    console.log('Test institution ID:', institution.id);
  } catch (error) {
    console.error('Error creating test users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUsers();