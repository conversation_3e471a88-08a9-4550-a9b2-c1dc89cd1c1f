const { PrismaClient } = require('@prisma/client');
const { logger } = require('../../../utils/logger');
const catchAsync = require('../../../utils/catchAsync');
const AppError = require('../../../utils/appError');
const { createAuditLog } = require('../../../services/auditService');

const prisma = new PrismaClient();

/**
 * Get attendance records with filtering and pagination
 */
const getAttendance = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    studentId,
    classId,
    schoolId,
    date,
    status,
    startDate,
    endDate,
    sortBy = 'date',
    sortOrder = 'desc'
  } = req.query;

  const skip = (parseInt(page) - 1) * parseInt(limit);
  const take = parseInt(limit);

  // Build where clause
  const where = {};

  if (schoolId) {
    where.student = { schoolId };
  }

  if (studentId) {
    where.studentId = studentId;
  }

  if (classId) {
    where.classId = classId;
  }

  if (status) {
    where.status = status;
  }

  if (date) {
    where.date = new Date(date);
  } else if (startDate && endDate) {
    where.date = {
      gte: new Date(startDate),
      lte: new Date(endDate)
    };
  }

  // Get attendance records
  const [attendanceRecords, totalCount] = await Promise.all([
    prisma.attendance.findMany({
      where,
      skip,
      take,
      orderBy: { [sortBy]: sortOrder },
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            studentId: true
          }
        },
        class: {
          select: {
            id: true,
            name: true,
            grade: true
          }
        },
        markedBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    }),
    prisma.attendance.count({ where })
  ]);

  const totalPages = Math.ceil(totalCount / take);

  res.status(200).json({
    status: 'success',
    data: {
      attendanceRecords,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    }
  });
});

/**
 * Get attendance record by ID
 */
const getAttendanceById = catchAsync(async (req, res) => {
  const { id } = req.params;

  const attendanceRecord = await prisma.attendance.findUnique({
    where: { id },
    include: {
      student: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          studentId: true
        }
      },
      class: {
        select: {
          id: true,
          name: true,
          grade: true
        }
      },
      markedBy: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true
        }
      }
    }
  });

  if (!attendanceRecord) {
    throw new AppError('Attendance record not found', 404);
  }

  res.status(200).json({
    status: 'success',
    data: { attendanceRecord }
  });
});

/**
 * Create new attendance record
 */
const createAttendanceRecord = catchAsync(async (req, res) => {
  const { studentId, classId, date, status, notes } = req.body;

  // Check if attendance record already exists for this student on this date
  const existingRecord = await prisma.attendance.findFirst({
    where: {
      studentId,
      date: new Date(date)
    }
  });

  if (existingRecord) {
    throw new AppError('Attendance record already exists for this student on this date', 400);
  }

  // Verify student exists and belongs to the class
  const student = await prisma.student.findUnique({
    where: { id: studentId },
    include: { classes: true }
  });

  if (!student) {
    throw new AppError('Student not found', 404);
  }

  const isStudentInClass = student.classes.some(cls => cls.id === classId);
  if (!isStudentInClass) {
    throw new AppError('Student is not enrolled in this class', 400);
  }

  const attendanceRecord = await prisma.attendance.create({
    data: {
      studentId,
      classId,
      date: new Date(date),
      status,
      notes,
      markedById: req.user.id
    },
    include: {
      student: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          studentId: true
        }
      },
      class: {
        select: {
          id: true,
          name: true,
          grade: true
        }
      }
    }
  });

  // Create audit log
  await createAuditLog({
    userId: req.user.id,
    action: 'ATTENDANCE_RECORD_CREATED',
    details: `Created attendance record for student ${student.firstName} ${student.lastName}`,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'Attendance',
    resourceId: attendanceRecord.id
  });

  res.status(201).json({
    status: 'success',
    data: { attendanceRecord }
  });
});

/**
 * Update attendance record
 */
const updateAttendanceRecord = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { status, notes } = req.body;

  const existingRecord = await prisma.attendance.findUnique({
    where: { id },
    include: { student: true }
  });

  if (!existingRecord) {
    throw new AppError('Attendance record not found', 404);
  }

  const attendanceRecord = await prisma.attendance.update({
    where: { id },
    data: {
      ...(status && { status }),
      ...(notes !== undefined && { notes }),
      updatedAt: new Date()
    },
    include: {
      student: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          studentId: true
        }
      },
      class: {
        select: {
          id: true,
          name: true,
          grade: true
        }
      }
    }
  });

  // Create audit log
  await createAuditLog({
    userId: req.user.id,
    action: 'ATTENDANCE_RECORD_UPDATED',
    details: `Updated attendance record for student ${existingRecord.student.firstName} ${existingRecord.student.lastName}`,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'Attendance',
    resourceId: id
  });

  res.status(200).json({
    status: 'success',
    data: { attendanceRecord }
  });
});

/**
 * Delete attendance record
 */
const deleteAttendanceRecord = catchAsync(async (req, res) => {
  const { id } = req.params;

  const existingRecord = await prisma.attendance.findUnique({
    where: { id },
    include: { student: true }
  });

  if (!existingRecord) {
    throw new AppError('Attendance record not found', 404);
  }

  await prisma.attendance.delete({
    where: { id }
  });

  // Create audit log
  await createAuditLog({
    userId: req.user.id,
    action: 'ATTENDANCE_RECORD_DELETED',
    details: `Deleted attendance record for student ${existingRecord.student.firstName} ${existingRecord.student.lastName}`,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'Attendance',
    resourceId: id
  });

  res.status(200).json({
    status: 'success',
    message: 'Attendance record deleted successfully'
  });
});

/**
 * Get attendance statistics
 */
const getAttendanceStats = catchAsync(async (req, res) => {
  const { classId, schoolId, startDate, endDate } = req.query;

  const where = {};
  
  if (schoolId) {
    where.student = { schoolId };
  }
  
  if (classId) {
    where.classId = classId;
  }
  
  if (startDate && endDate) {
    where.date = {
      gte: new Date(startDate),
      lte: new Date(endDate)
    };
  }

  const stats = await prisma.attendance.groupBy({
    by: ['status'],
    where,
    _count: {
      status: true
    }
  });

  const totalRecords = await prisma.attendance.count({ where });

  const formattedStats = {
    total: totalRecords,
    present: stats.find(s => s.status === 'PRESENT')?._count?.status || 0,
    absent: stats.find(s => s.status === 'ABSENT')?._count?.status || 0,
    late: stats.find(s => s.status === 'LATE')?._count?.status || 0,
    excused: stats.find(s => s.status === 'EXCUSED')?._count?.status || 0
  };

  // Calculate percentages
  formattedStats.presentPercentage = totalRecords > 0 ? (formattedStats.present / totalRecords * 100).toFixed(2) : 0;
  formattedStats.absentPercentage = totalRecords > 0 ? (formattedStats.absent / totalRecords * 100).toFixed(2) : 0;

  res.status(200).json({
    status: 'success',
    data: { stats: formattedStats }
  });
});

/**
 * Get student attendance summary
 */
const getStudentAttendanceSummary = catchAsync(async (req, res) => {
  const { studentId } = req.params;
  const { startDate, endDate } = req.query;

  const where = { studentId };
  
  if (startDate && endDate) {
    where.date = {
      gte: new Date(startDate),
      lte: new Date(endDate)
    };
  }

  const attendanceRecords = await prisma.attendance.findMany({
    where,
    orderBy: { date: 'desc' },
    include: {
      class: {
        select: {
          id: true,
          name: true,
          grade: true
        }
      }
    }
  });

  const stats = {
    total: attendanceRecords.length,
    present: attendanceRecords.filter(r => r.status === 'PRESENT').length,
    absent: attendanceRecords.filter(r => r.status === 'ABSENT').length,
    late: attendanceRecords.filter(r => r.status === 'LATE').length,
    excused: attendanceRecords.filter(r => r.status === 'EXCUSED').length
  };

  stats.presentPercentage = stats.total > 0 ? (stats.present / stats.total * 100).toFixed(2) : 0;
  stats.absentPercentage = stats.total > 0 ? (stats.absent / stats.total * 100).toFixed(2) : 0;

  res.status(200).json({
    status: 'success',
    data: {
      summary: stats,
      attendanceRecords
    }
  });
});

/**
 * Bulk mark attendance for multiple students
 */
const bulkMarkAttendance = catchAsync(async (req, res) => {
  const { classId, date, attendanceRecords } = req.body;

  // Verify the class exists
  const classExists = await prisma.class.findUnique({
    where: { id: classId }
  });

  if (!classExists) {
    throw new AppError('Class not found', 404);
  }

  // Check for existing records on this date
  const existingRecords = await prisma.attendance.findMany({
    where: {
      classId,
      date: new Date(date),
      studentId: { in: attendanceRecords.map(r => r.studentId) }
    }
  });

  if (existingRecords.length > 0) {
    throw new AppError('Some attendance records already exist for this date', 400);
  }

  // Create attendance records
  const createdRecords = await prisma.$transaction(
    attendanceRecords.map(record => 
      prisma.attendance.create({
        data: {
          studentId: record.studentId,
          classId,
          date: new Date(date),
          status: record.status,
          notes: record.notes || null,
          markedById: req.user.id
        },
        include: {
          student: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              studentId: true
            }
          }
        }
      })
    )
  );

  // Create audit log
  await createAuditLog({
    userId: req.user.id,
    action: 'BULK_ATTENDANCE_MARKED',
    details: `Marked attendance for ${createdRecords.length} students in class ${classExists.name}`,
    ipAddress: req.ip,
    userAgent: req.headers['user-agent'],
    resourceType: 'Attendance',
    resourceId: classId
  });

  res.status(201).json({
    status: 'success',
    data: { 
      attendanceRecords: createdRecords,
      count: createdRecords.length
    }
  });
});

module.exports = {
  getAttendance,
  getAttendanceById,
  createAttendanceRecord,
  updateAttendanceRecord,
  deleteAttendanceRecord,
  getAttendanceStats,
  getStudentAttendanceSummary,
  bulkMarkAttendance
};
