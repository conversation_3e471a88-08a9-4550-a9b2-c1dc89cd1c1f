"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON>er } from "next/navigation"
import { <PERSON>, Download, Printer, ChevronLeft, ChevronRight } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { InstitutionProtectedPage } from "@/components/auth/protected-page"

// Mock data for the class
const mockClass = {
  id: "1",
  name: "Class 10-A",
  section: "A",
  grade: "10",
  academicYear: "2023-2024",
  roomNumber: "301",
  classTeacher: "<PERSON>. <PERSON>",
  schedule: [
    {
      day: "Monday",
      periods: [
        { time: "8:00 - 8:45", subject: "Mathematics", teacher: "Mr. <PERSON>", room: "301" },
        { time: "8:50 - 9:35", subject: "Physics", teacher: "Dr. <PERSON>", room: "301" },
        { time: "9:40 - 10:25", subject: "English", teacher: "Ms. <PERSON>", room: "301" },
        { time: "10:40 - 11:25", subject: "History", teacher: "Dr. Michael <PERSON>", room: "301" },
        { time: "11:30 - 12:15", subject: "Computer Science", teacher: "Mr. David Lee", room: "405" },
      ],
    },
    {
      day: "Tuesday",
      periods: [
        { time: "8:00 - 8:45", subject: "Chemistry", teacher: "Ms. Lisa Rodriguez", room: "301" },
        { time: "8:50 - 9:35", subject: "Mathematics", teacher: "Mr. Robert Chen", room: "301" },
        { time: "9:40 - 10:25", subject: "Physical Education", teacher: "Mr. Thomas Martinez", room: "Gym" },
        { time: "10:40 - 11:25", subject: "Geography", teacher: "Ms. Patricia Miller", room: "301" },
        { time: "11:30 - 12:15", subject: "English", teacher: "Ms. Sarah Johnson", room: "301" },
      ],
    },
    {
      day: "Wednesday",
      periods: [
        { time: "8:00 - 8:45", subject: "Physics", teacher: "Dr. Emily Watson", room: "301" },
        { time: "8:50 - 9:35", subject: "Biology", teacher: "Mr. James Wilson", room: "301" },
        { time: "9:40 - 10:25", subject: "Mathematics", teacher: "Mr. Robert Chen", room: "301" },
        { time: "10:40 - 11:25", subject: "Art", teacher: "Ms. Jennifer Taylor", room: "201" },
        { time: "11:30 - 12:15", subject: "Language", teacher: "Ms. Elizabeth Garcia", room: "301" },
      ],
    },
    {
      day: "Thursday",
      periods: [
        { time: "8:00 - 8:45", subject: "English", teacher: "Ms. Sarah Johnson", room: "301" },
        { time: "8:50 - 9:35", subject: "Chemistry", teacher: "Ms. Lisa Rodriguez", room: "301" },
        { time: "9:40 - 10:25", subject: "History", teacher: "Dr. Michael Brown", room: "301" },
        { time: "10:40 - 11:25", subject: "Mathematics", teacher: "Mr. Robert Chen", room: "301" },
        { time: "11:30 - 12:15", subject: "Music", teacher: "Mr. Christopher Davis", room: "105" },
      ],
    },
    {
      day: "Friday",
      periods: [
        { time: "8:00 - 8:45", subject: "Mathematics", teacher: "Mr. Robert Chen", room: "301" },
        { time: "8:50 - 9:35", subject: "Physics", teacher: "Dr. Emily Watson", room: "301" },
        { time: "9:40 - 10:25", subject: "Computer Science", teacher: "Mr. David Lee", room: "405" },
        { time: "10:40 - 11:25", subject: "Biology", teacher: "Mr. James Wilson", room: "301" },
        { time: "11:30 - 12:15", subject: "Ethics", teacher: "Dr. Susan Anderson", room: "301" },
      ],
    },
  ],
  events: [
    { id: "1", title: "Mathematics Quiz", date: "2023-10-15", time: "9:00 AM", teacher: "Mr. Robert Chen" },
    { id: "2", title: "Physics Lab", date: "2023-10-17", time: "10:30 AM", teacher: "Dr. Emily Watson" },
    { id: "3", title: "English Presentation", date: "2023-10-19", time: "11:00 AM", teacher: "Ms. Sarah Johnson" },
    { id: "4", title: "History Field Trip", date: "2023-10-25", time: "8:30 AM", teacher: "Dr. Michael Brown" },
    { id: "5", title: "Science Fair", date: "2023-10-30", time: "All Day", teacher: "Multiple Teachers" },
  ],
}

// Color mapping for subjects
const subjectColors: Record<string, string> = {
  Mathematics: "bg-blue-100 text-blue-800 border-blue-300",
  Physics: "bg-purple-100 text-purple-800 border-purple-300",
  Chemistry: "bg-green-100 text-green-800 border-green-300",
  Biology: "bg-teal-100 text-teal-800 border-teal-300",
  English: "bg-red-100 text-red-800 border-red-300",
  History: "bg-amber-100 text-amber-800 border-amber-300",
  Geography: "bg-lime-100 text-lime-800 border-lime-300",
  "Computer Science": "bg-cyan-100 text-cyan-800 border-cyan-300",
  "Physical Education": "bg-orange-100 text-orange-800 border-orange-300",
  Art: "bg-pink-100 text-pink-800 border-pink-300",
  Music: "bg-indigo-100 text-indigo-800 border-indigo-300",
  Language: "bg-violet-100 text-violet-800 border-violet-300",
  Ethics: "bg-rose-100 text-rose-800 border-rose-300",
}

function ClassScheduleContent() {
  const params = useParams()
  const router = useRouter()
  const [currentWeek, setCurrentWeek] = useState("October 15-21, 2023")

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Class Schedule</h1>
          <p className="text-muted-foreground">View and manage the schedule for {mockClass.name}</p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Class
          </Button>
          <Button variant="outline" size="sm">
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Tabs defaultValue="weekly">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="weekly">Weekly Schedule</TabsTrigger>
          <TabsTrigger value="events">Upcoming Events</TabsTrigger>
        </TabsList>

        <TabsContent value="weekly" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                <CardTitle>Weekly Timetable</CardTitle>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="icon">
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <div className="font-medium">{currentWeek}</div>
                  <Button variant="outline" size="icon">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardDescription>
                Room: {mockClass.roomNumber} | Class Teacher: {mockClass.classTeacher}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="py-3 px-4 text-left font-medium min-w-[100px]">Time</th>
                      {mockClass.schedule.map((day) => (
                        <th key={day.day} className="py-3 px-4 text-left font-medium min-w-[180px]">
                          {day.day}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {mockClass.schedule[0].periods.map((_, periodIndex) => (
                      <tr key={periodIndex} className="border-b last:border-0">
                        <td className="py-3 px-4 font-medium">{mockClass.schedule[0].periods[periodIndex].time}</td>
                        {mockClass.schedule.map((day) => {
                          const period = day.periods[periodIndex]
                          const colorClass =
                            subjectColors[period.subject] || "bg-gray-100 text-gray-800 border-gray-300"

                          return (
                            <td key={`${day.day}-${periodIndex}`} className="py-3 px-4">
                              <div className={`border rounded-md p-2 ${colorClass}`}>
                                <div className="font-medium">{period.subject}</div>
                                <div className="text-sm">{period.teacher}</div>
                                <div className="text-xs mt-1">Room: {period.room}</div>
                              </div>
                            </td>
                          )
                        })}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="mt-6 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
                {Object.entries(subjectColors).map(([subject, colorClass]) => (
                  <div key={subject} className="flex items-center">
                    <div className={`w-4 h-4 rounded-full mr-2 ${colorClass.split(" ")[0]}`}></div>
                    <span className="text-sm truncate">{subject}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                <CardTitle>Upcoming Events</CardTitle>
                <Select defaultValue="october">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select month" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="september">September 2023</SelectItem>
                    <SelectItem value="october">October 2023</SelectItem>
                    <SelectItem value="november">November 2023</SelectItem>
                    <SelectItem value="december">December 2023</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <CardDescription>Tests, quizzes, and special activities for {mockClass.name}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockClass.events.map((event) => (
                  <div key={event.id} className="flex items-start border-b pb-4 last:border-0 last:pb-0">
                    <div className="bg-muted rounded-md p-3 mr-4 text-center min-w-[60px]">
                      <div className="text-sm font-medium">{event.date.split("-")[2]}</div>
                      <div className="text-xs text-muted-foreground">Oct</div>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{event.title}</h4>
                      <div className="flex flex-wrap gap-2 mt-1">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Clock className="mr-1 h-3 w-3" />
                          {event.time}
                        </div>
                        <div className="text-sm text-muted-foreground">{event.teacher}</div>
                      </div>
                    </div>
                    <Badge variant="outline" className="ml-2">
                      {event.title.includes("Quiz")
                        ? "Quiz"
                        : event.title.includes("Lab")
                          ? "Lab"
                          : event.title.includes("Presentation")
                            ? "Presentation"
                            : event.title.includes("Field Trip")
                              ? "Field Trip"
                              : event.title.includes("Fair")
                                ? "Event"
                                : "Other"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default function ClassSchedulePage() {
  return (
    <InstitutionProtectedPage>
      <ClassScheduleContent />
    </InstitutionProtectedPage>
  )
}
