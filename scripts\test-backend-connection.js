#!/usr/bin/env node

/**
 * Test script to check backend connectivity
 */
const fetch = require('node-fetch');

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:4000';

async function testBackendConnection() {
  try {
    console.log(`Testing connection to backend at ${BACKEND_URL}...`);
    
    const response = await fetch(`${BACKEND_URL}/api/v1/health`);
    const data = await response.json();
    
    console.log('Backend health check result:', data);
    console.log('Connection successful!');
    
    return true;
  } catch (error) {
    console.error('Backend connection failed:', error.message);
    return false;
  }
}

async function main() {
  const connected = await testBackendConnection();
  
  if (!connected) {
    console.log('\nTroubleshooting tips:');
    console.log('1. Make sure the backend server is running');
    console.log('2. Check the BACKEND_URL environment variable');
    console.log('3. Verify network connectivity and firewall settings');
    console.log('4. Check the backend logs for any errors');
    process.exit(1);
  }
  
  process.exit(0);
}

main();
