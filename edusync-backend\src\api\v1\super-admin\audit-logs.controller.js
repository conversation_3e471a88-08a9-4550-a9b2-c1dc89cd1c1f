const { PrismaClient } = require('@prisma/client');
const asyncHandler = require('../../../utils/asyncHandler');

const prisma = new PrismaClient();

/**
 * Get audit logs with pagination and filtering
 */
const getAuditLogs = asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    search, 
    category, 
    severity, 
    userId,
    startDate,
    endDate,
    action
  } = req.query;

  const skip = (page - 1) * limit;
  const where = {};

  // Add search filter
  if (search) {
    where.OR = [
      { action: { contains: search, mode: 'insensitive' } },
      { details: { contains: search, mode: 'insensitive' } },
      { ipAddress: { contains: search, mode: 'insensitive' } }
    ];
  }

  // Add category filter
  if (category && category !== 'all') {
    where.category = category;
  }

  // Add severity filter
  if (severity && severity !== 'all') {
    where.severity = severity;
  }

  // Add user filter
  if (userId && userId !== 'all') {
    where.userId = userId;
  }

  // Add action filter
  if (action) {
    where.action = action;
  }

  // Add date range filter
  if (startDate || endDate) {
    where.createdAt = {};
    if (startDate) {
      where.createdAt.gte = new Date(startDate);
    }
    if (endDate) {
      where.createdAt.lte = new Date(endDate);
    }
  }

  const [auditLogs, totalLogs] = await Promise.all([
    prisma.auditLog.findMany({
      where,
      skip: parseInt(skip),
      take: parseInt(limit),
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.auditLog.count({ where })
  ]);

  res.status(200).json({
    success: true,
    data: {
      logs: auditLogs.map(log => ({
        id: log.id,
        action: log.action,
        user: log.user ? {
          id: log.user.id,
          name: `${log.user.firstName} ${log.user.lastName}`,
          email: log.user.email
        } : null,
        details: log.details,
        category: log.category || 'System',
        severity: log.severity || 'Info',
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        timestamp: log.createdAt,
        entityType: log.entityType,
        entityId: log.entityId
      })),
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalLogs / limit),
        totalLogs,
        hasNextPage: skip + auditLogs.length < totalLogs,
        hasPrevPage: page > 1
      }
    }
  });
});

/**
 * Get audit log by ID
 */
const getAuditLogById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const auditLog = await prisma.auditLog.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true
        }
      }
    }
  });

  if (!auditLog) {
    return res.status(404).json({
      success: false,
      error: 'Audit log not found'
    });
  }

  res.status(200).json({
    success: true,
    data: {
      id: auditLog.id,
      action: auditLog.action,
      user: auditLog.user ? {
        id: auditLog.user.id,
        name: `${auditLog.user.firstName} ${auditLog.user.lastName}`,
        email: auditLog.user.email,
        role: auditLog.user.role
      } : null,
      details: auditLog.details,
      category: auditLog.category,
      severity: auditLog.severity,
      ipAddress: auditLog.ipAddress,
      userAgent: auditLog.userAgent,
      timestamp: auditLog.createdAt,
      entityType: auditLog.entityType,
      entityId: auditLog.entityId
    }
  });
});

/**
 * Get audit logs by category
 */
const getAuditLogsByCategory = asyncHandler(async (req, res) => {
  const { category } = req.params;
  const { page = 1, limit = 20 } = req.query;

  const skip = (page - 1) * limit;

  const [logs, totalLogs] = await Promise.all([
    prisma.auditLog.findMany({
      where: { category },
      skip: parseInt(skip),
      take: parseInt(limit),
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.auditLog.count({ where: { category } })
  ]);

  res.status(200).json({
    success: true,
    data: {
      category,
      logs: logs.map(log => ({
        id: log.id,
        action: log.action,
        user: log.user ? `${log.user.firstName} ${log.user.lastName}` : 'System',
        details: log.details,
        severity: log.severity,
        timestamp: log.createdAt
      })),
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalLogs / limit),
        totalLogs
      }
    }
  });
});

/**
 * Get audit log statistics
 */
const getAuditStats = asyncHandler(async (req, res) => {
  const { period = '7d' } = req.query;

  let startDate;
  switch (period) {
    case '24h':
      startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  }

  const [
    totalLogs,
    recentLogs,
    categoryStats,
    severityStats,
    topUsers
  ] = await Promise.all([
    prisma.auditLog.count({
      where: {
        createdAt: { gte: startDate }
      }
    }),
    prisma.auditLog.count(),
    prisma.auditLog.groupBy({
      by: ['category'],
      _count: { id: true },
      where: {
        createdAt: { gte: startDate }
      }
    }),
    prisma.auditLog.groupBy({
      by: ['severity'],
      _count: { id: true },
      where: {
        createdAt: { gte: startDate }
      }
    }),
    prisma.auditLog.groupBy({
      by: ['userId'],
      _count: { id: true },
      where: {
        createdAt: { gte: startDate },
        userId: { not: null }
      },
      orderBy: {
        _count: { id: 'desc' }
      },
      take: 5
    })
  ]);

  // Get user details for top users
  const userIds = topUsers.map(item => item.userId);
  const users = await prisma.user.findMany({
    where: { id: { in: userIds } },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true
    }
  });

  const topUsersWithDetails = topUsers.map(item => {
    const user = users.find(u => u.id === item.userId);
    return {
      userId: item.userId,
      user: user ? `${user.firstName} ${user.lastName}` : 'Unknown',
      email: user?.email || 'Unknown',
      actionCount: item._count.id
    };
  });

  res.status(200).json({
    success: true,
    data: {
      period,
      totalLogs,
      recentLogsCount: recentLogs,
      categoryDistribution: categoryStats.reduce((acc, item) => {
        acc[item.category || 'Unknown'] = item._count.id;
        return acc;
      }, {}),
      severityDistribution: severityStats.reduce((acc, item) => {
        acc[item.severity || 'Info'] = item._count.id;
        return acc;
      }, {}),
      topUsers: topUsersWithDetails
    }
  });
});

/**
 * Export audit logs
 */
const exportAuditLogs = asyncHandler(async (req, res) => {
  const { format = 'csv', category, severity, startDate, endDate } = req.query;

  const where = {};

  if (category && category !== 'all') {
    where.category = category;
  }

  if (severity && severity !== 'all') {
    where.severity = severity;
  }

  if (startDate || endDate) {
    where.createdAt = {};
    if (startDate) {
      where.createdAt.gte = new Date(startDate);
    }
    if (endDate) {
      where.createdAt.lte = new Date(endDate);
    }
  }

  // This would generate actual export file
  // For now, return a success message
  res.status(200).json({
    success: true,
    message: `Audit logs exported successfully in ${format} format`,
    downloadUrl: `/exports/audit-logs-${Date.now()}.${format}`,
    filters: { category, severity, startDate, endDate }
  });
});

module.exports = {
  getAuditLogs,
  getAuditLogById,
  getAuditLogsByCategory,
  getAuditStats,
  exportAuditLogs
};
