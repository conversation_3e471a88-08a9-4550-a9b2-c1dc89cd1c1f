import { type NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  try {
    // Get auth token from cookies or headers
    const authToken =
      request.cookies.get("accessToken")?.value || request.headers.get("authorization")?.replace("Bearer ", "")

    if (!authToken) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    // Forward request to Flask backend
    const backendUrl = process.env.BACKEND_URL || "http://localhost:4000"
    const response = await fetch(`${backendUrl}/api/v1/institutions/my-institutions`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error("Failed to fetch institutions")
    }

    const institutions = await response.json()
    return NextResponse.json(institutions)
  } catch (error) {
    console.error("Error fetching user institutions:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
