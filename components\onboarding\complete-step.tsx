"use client"

import { <PERSON><PERSON><PERSON><PERSON>, ArrowR<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useOnboarding } from "@/contexts/onboarding-context"

export function CompleteStep() {
  const { onboardingData, completeOnboarding } = useOnboarding()

  return (
    <Card className="border-0 shadow-none">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-emerald-100">
          <CheckCircle className="h-8 w-8 text-emerald-600" />
        </div>
        <CardTitle className="text-2xl">Setup Complete!</CardTitle>
        <CardDescription className="text-base">Your school management system is now ready to use</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="rounded-lg border p-4">
          <h3 className="font-medium">Institution Details</h3>
          <div className="mt-2 grid grid-cols-2 gap-2 text-sm">
            <div className="text-muted-foreground">Name:</div>
            <div>{onboardingData.institutionName}</div>
            <div className="text-muted-foreground">Type:</div>
            <div>{onboardingData.institutionType}</div>
            <div className="text-muted-foreground">Location:</div>
            <div>
              {onboardingData.city}, {onboardingData.country}
            </div>
          </div>
        </div>

        <div className="rounded-lg border p-4">
          <h3 className="font-medium">Administrator Account</h3>
          <div className="mt-2 grid grid-cols-2 gap-2 text-sm">
            <div className="text-muted-foreground">Name:</div>
            <div>
              {onboardingData.firstName} {onboardingData.lastName}
            </div>
            <div className="text-muted-foreground">Email:</div>
            <div>{onboardingData.email}</div>
          </div>
        </div>

        {onboardingData.invitedUsers.length > 0 && (
          <div className="rounded-lg border p-4">
            <h3 className="font-medium">Invited Users</h3>
            <p className="text-xs text-muted-foreground mt-1">
              {onboardingData.invitedUsers.length} user(s) will be invited to join your institution
            </p>
          </div>
        )}

        <div className="rounded-lg bg-muted p-4">
          <p className="text-sm font-medium">What's Next?</p>
          <ul className="mt-2 space-y-1 text-xs text-muted-foreground">
            <li>• Set up classes and subjects</li>
            <li>• Add students and parents</li>
            <li>• Configure timetables</li>
            <li>• Customize your dashboard</li>
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex justify-center">
        <Button onClick={completeOnboarding} className="gap-2">
          Go to Dashboard
          <ArrowRight className="h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  )
}
