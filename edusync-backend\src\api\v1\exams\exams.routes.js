const express = require('express');
const { body, param, query } = require('express-validator');
const examController = require('./exams.controller');
const { validateRequest } = require('../../../middleware/validateRequest');
const { authenticate, restrictTo } = require('../../../middleware/authenticate');

const router = express.Router();

/**
 * @route GET /api/v1/exams
 * @desc Get all exams (with pagination and filtering)
 * @access Private
 */
router.get(
  '/',
  authenticate,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('schoolId').optional().isUUID().withMessage('Invalid school ID'),
  query('academicYearId').optional().isUUID().withMessage('Invalid academic year ID'),
  validateRequest,
  examController.getExams
);

/**
 * @route GET /api/v1/exams/stats
 * @desc Get exam statistics
 * @access Private
 */
router.get(
  '/stats',
  authenticate,
  query('schoolId').optional().isUUID().withMessage('Invalid school ID'),
  query('academicYearId').optional().isUUID().withMessage('Invalid academic year ID'),
  validateRequest,
  examController.getExamStats
);

/**
 * @route GET /api/v1/exams/:id
 * @desc Get exam by ID
 * @access Private
 */
router.get(
  '/:id',
  authenticate,
  param('id').isUUID().withMessage('Invalid exam ID'),
  validateRequest,
  examController.getExamById
);

/**
 * @route GET /api/v1/exams/:id/schedules
 * @desc Get exam schedules
 * @access Private
 */
router.get(
  '/:id/schedules',
  authenticate,
  param('id').isUUID().withMessage('Invalid exam ID'),
  query('date').optional().isISO8601().withMessage('Invalid date format'),
  query('subjectId').optional().isUUID().withMessage('Invalid subject ID'),
  validateRequest,
  examController.getExamSchedules
);

/**
 * @route POST /api/v1/exams
 * @desc Create new exam
 * @access Private (Admin/Teacher)
 */
router.post(
  '/',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'),
  [
    body('name')
      .trim()
      .notEmpty()
      .withMessage('Exam name is required')
      .isLength({ min: 2, max: 100 })
      .withMessage('Exam name must be between 2 and 100 characters'),
    body('type')
      .trim()
      .notEmpty()
      .withMessage('Exam type is required')
      .isIn(['MIDTERM', 'FINAL', 'QUIZ', 'UNIT_TEST', 'ANNUAL', 'MONTHLY', 'WEEKLY'])
      .withMessage('Invalid exam type'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must not exceed 500 characters'),
    body('startDate')
      .isISO8601()
      .withMessage('Invalid start date format'),
    body('endDate')
      .isISO8601()
      .withMessage('Invalid end date format'),
    body('totalMarks')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Total marks must be a positive integer'),
    body('passingMarks')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Passing marks must be a positive integer'),
    body('schoolId')
      .isUUID()
      .withMessage('Invalid school ID'),
    body('academicYearId')
      .isUUID()
      .withMessage('Invalid academic year ID'),
    body('academicTermId')
      .optional()
      .isUUID()
      .withMessage('Invalid academic term ID'),
    body('classes')
      .optional()
      .isArray()
      .withMessage('Classes must be an array'),
    body('classes.*')
      .optional()
      .isUUID()
      .withMessage('Invalid class ID'),
    body('subjects')
      .optional()
      .isArray()
      .withMessage('Subjects must be an array'),
    body('subjects.*.subjectId')
      .isUUID()
      .withMessage('Invalid subject ID'),
    body('subjects.*.maxMarks')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Max marks must be a positive integer'),
    body('subjects.*.duration')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Duration must be a positive integer')
  ],
  validateRequest,
  examController.createExam
);

/**
 * @route PATCH /api/v1/exams/:id
 * @desc Update exam
 * @access Private (Admin/Teacher)
 */
router.patch(
  '/:id',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'),
  [
    param('id').isUUID().withMessage('Invalid exam ID'),
    body('name')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Exam name cannot be empty')
      .isLength({ min: 2, max: 100 })
      .withMessage('Exam name must be between 2 and 100 characters'),
    body('type')
      .optional()
      .trim()
      .notEmpty()
      .withMessage('Exam type cannot be empty')
      .isIn(['MIDTERM', 'FINAL', 'QUIZ', 'UNIT_TEST', 'ANNUAL', 'MONTHLY', 'WEEKLY'])
      .withMessage('Invalid exam type'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must not exceed 500 characters'),
    body('startDate')
      .optional()
      .isISO8601()
      .withMessage('Invalid start date format'),
    body('endDate')
      .optional()
      .isISO8601()
      .withMessage('Invalid end date format'),
    body('totalMarks')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Total marks must be a positive integer'),
    body('passingMarks')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Passing marks must be a positive integer'),
    body('status')
      .optional()
      .isIn(['SCHEDULED', 'ONGOING', 'COMPLETED', 'CANCELLED'])
      .withMessage('Invalid exam status'),
    body('academicTermId')
      .optional()
      .isUUID()
      .withMessage('Invalid academic term ID'),
    body('classes')
      .optional()
      .isArray()
      .withMessage('Classes must be an array'),
    body('classes.*')
      .optional()
      .isUUID()
      .withMessage('Invalid class ID'),
    body('subjects')
      .optional()
      .isArray()
      .withMessage('Subjects must be an array'),
    body('subjects.*.subjectId')
      .isUUID()
      .withMessage('Invalid subject ID'),
    body('subjects.*.maxMarks')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Max marks must be a positive integer'),
    body('subjects.*.duration')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Duration must be a positive integer')
  ],
  validateRequest,
  examController.updateExam
);

/**
 * @route DELETE /api/v1/exams/:id
 * @desc Delete exam
 * @access Private (Admin only)
 */
router.delete(
  '/:id',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'),
  param('id').isUUID().withMessage('Invalid exam ID'),
  validateRequest,
  examController.deleteExam
);

/**
 * @route POST /api/v1/exams/:id/schedules
 * @desc Create exam schedule
 * @access Private (Admin/Teacher)
 */
router.post(
  '/:id/schedules',
  authenticate,
  restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'),
  [
    param('id').isUUID().withMessage('Invalid exam ID'),
    body('subjectId')
      .isUUID()
      .withMessage('Invalid subject ID'),
    body('date')
      .isISO8601()
      .withMessage('Invalid date format'),
    body('startTime')
      .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('Invalid start time format (HH:MM)'),
    body('endTime')
      .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('Invalid end time format (HH:MM)'),
    body('venue')
      .optional()
      .trim()
      .isLength({ max: 100 })
      .withMessage('Venue must not exceed 100 characters')
  ],
  validateRequest,
  examController.createExamSchedule
);

module.exports = router;
