"use client"

import { useState, useEffect } from "react"
import {
  PlusCircle,
  Search,
  Filter,
  MoreHorizontal,
  Download,
  Trash2,
  Edit,
  BookOpen,
  Users,
  Clock,
  Loader2,
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardDescription, CardHeader, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { subjectService, safeArrayFromResponse } from "@/lib/backend-api"
import { enhancedApiClient } from "@/lib/api-client-enhanced"
import { useToast } from "@/hooks/use-toast"
import { InstitutionProtectedPage } from "@/components/auth/protected-page"
import { useTenant } from "@/contexts/tenant-context"

// TypeScript interfaces for Subject
interface Department {
  id: string
  name: string
}

interface Subject {
  id: string
  name: string
  code?: string
  description?: string
  credits?: number
  isCore: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
  schoolId: string
  departmentId?: string
  department?: Department
  _count?: {
    classes: number
    teacherAssignments: number
  }
}

interface SubjectFormData {
  name: string
  code: string
  description: string
  credits: number
  isCore: boolean
  departmentId: string
}

interface Teacher {
  id: string
  user: {
    firstName: string
    lastName: string
    email: string
  }
  employeeId?: string
  specialization?: string
}

interface TeacherAssignment {
  id: string
  teacherId: string
  subjectId: string
  isClassTeacher: boolean
  isActive: boolean
  teacher: Teacher
}

function SubjectsContent() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddSubjectOpen, setIsAddSubjectOpen] = useState(false)
  const [isEditSubjectOpen, setIsEditSubjectOpen] = useState(false)
  const [isAssignTeachersOpen, setIsAssignTeachersOpen] = useState(false)
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null)
  const [assigningSubject, setAssigningSubject] = useState<Subject | null>(null)
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [selectedTeachers, setSelectedTeachers] = useState<string[]>([])
  const [assignedTeachers, setAssignedTeachers] = useState<string[]>([])
  const [filteredSubjects, setFilteredSubjects] = useState<Subject[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoadingAssignments, setIsLoadingAssignments] = useState(false)
  const [formData, setFormData] = useState<SubjectFormData>({
    name: "",
    code: "",
    description: "",
    credits: 1,
    isCore: true,
    departmentId: "",
  })

  const { toast } = useToast()
  const { currentInstitution } = useTenant()

  // Fetch departments from API
  const fetchDepartments = async () => {
    try {
      // Build URL with institution context
      const url = new URL('/api/departments', window.location.origin)
      if (currentInstitution?.id) {
        url.searchParams.append('institutionId', currentInstitution.id)
      }

      const response = await fetch(url.toString(), {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data) {
          setDepartments(result.data)
        }
      }
    } catch (error) {
      console.error("Error fetching departments:", error)
      // Use fallback departments if API fails
      setDepartments([
        { id: "1", name: "Mathematics" },
        { id: "2", name: "Language Arts" },
        { id: "3", name: "Science" },
        { id: "4", name: "Social Studies" },
        { id: "5", name: "Physical Education" },
        { id: "6", name: "Arts" },
        { id: "7", name: "Foreign Languages" },
        { id: "8", name: "Computer Science" },
        { id: "9", name: "Special Education" },
      ])
    }
  }

  // Fetch subjects from API
  const fetchSubjects = async () => {
    try {
      setIsLoading(true)
      console.log("🔄 Fetching subjects for institution:", currentInstitution?.id)

      // Prepare query parameters with institution context
      const params: any = {}
      if (currentInstitution?.id) {
        params.institutionId = currentInstitution.id
      }

      // Try enhanced API client first, fallback to legacy if needed
      let response = await enhancedApiClient.get("/api/subjects", params)

      // If enhanced client fails, try legacy method
      if (!response.success) {
        console.log("Enhanced API failed, trying legacy method...")
        response = await subjectService.getSubjectsLegacy(params)
      }

      const subjectsData = safeArrayFromResponse(response)
      console.log("📊 Raw response:", response)
      console.log("📊 Extracted subjects data:", subjectsData)
      console.log("📊 Fetched subjects:", subjectsData.length, "subjects")
      setSubjects(subjectsData)
      setFilteredSubjects(subjectsData)

      if (response && !response.success) {
        toast({
          title: "Error",
          description: response.error || "Failed to fetch subjects",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error fetching subjects:", error)
      // Arrays are already set to safe values above
      toast({
        title: "Error",
        description: "Failed to fetch subjects",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch teachers from API
  const fetchTeachers = async () => {
    try {
      // Build URL with institution context
      const url = new URL('/api/teachers', window.location.origin)
      if (currentInstitution?.id) {
        url.searchParams.append('institutionId', currentInstitution.id)
      }

      const response = await fetch(url.toString(), {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json()
        console.log("📊 Teachers API response:", result)

        if (result.success && result.data) {
          // Backend returns teachers directly in result.data (not result.data.teachers)
          if (Array.isArray(result.data)) {
            console.log("📊 Found teachers array:", result.data.length, "teachers")
            setTeachers(result.data)
          } else {
            console.log("📊 Teachers data is not an array:", result.data)
            setTeachers([])
          }
        } else {
          console.log("📊 Teachers API response not successful:", result)
          setTeachers([])
        }
      } else {
        console.log("📊 Teachers API response not ok:", response.status)
        setTeachers([])
      }
    } catch (error) {
      console.error("Error fetching teachers:", error)
      setTeachers([])
    }
  }

  // Fetch existing assignments for a subject
  const fetchSubjectAssignments = async (subjectId: string) => {
    try {
      setIsLoadingAssignments(true)
      const response = await fetch(`/api/subjects/${subjectId}/assignments`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json()
        console.log("📊 Subject assignments response:", result)

        if (result.success && result.data && result.data.assignments) {
          const assignedTeacherIds = result.data.assignments.map((assignment: any) => assignment.teacher.id)
          console.log("📊 Found assigned teachers:", assignedTeacherIds)
          setAssignedTeachers(assignedTeacherIds)
          return assignedTeacherIds
        } else {
          console.log("📊 No assignments found for subject")
          setAssignedTeachers([])
          return []
        }
      } else {
        console.log("📊 Failed to fetch assignments:", response.status)
        setAssignedTeachers([])
        return []
      }
    } catch (error) {
      console.error("Error fetching subject assignments:", error)
      setAssignedTeachers([])
      return []
    } finally {
      setIsLoadingAssignments(false)
    }
  }

  // Handle assign teachers to subject
  const handleAssignTeachers = async (subject: Subject) => {
    setAssigningSubject(subject)
    setSelectedTeachers([])
    setIsAssignTeachersOpen(true)

    // Fetch teachers and existing assignments
    await Promise.all([
      fetchTeachers(),
      fetchSubjectAssignments(subject.id)
    ])
  }

  // Handle teacher assignment submission
  const handleSubmitTeacherAssignments = async () => {
    if (!assigningSubject || selectedTeachers.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please select at least one teacher",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)

      // Filter out teachers that are already assigned
      const newTeachers = selectedTeachers.filter(teacherId => !assignedTeachers.includes(teacherId))
      const alreadyAssigned = selectedTeachers.filter(teacherId => assignedTeachers.includes(teacherId))

      if (alreadyAssigned.length > 0) {
        const alreadyAssignedNames = teachers
          .filter(teacher => alreadyAssigned.includes(teacher.id))
          .map(teacher => `${teacher.user.firstName} ${teacher.user.lastName}`)
          .join(", ")

        toast({
          title: "Some teachers already assigned",
          description: `${alreadyAssignedNames} ${alreadyAssigned.length === 1 ? 'is' : 'are'} already assigned to this subject`,
          variant: "destructive",
        })
      }

      if (newTeachers.length === 0) {
        return // No new assignments to create
      }

      // Create assignments for each new teacher
      const assignmentPromises = newTeachers.map(teacherId =>
        enhancedApiClient.post("/api/teachers/assignments", {
          teacherId,
          subjectId: assigningSubject.id,
          isClassTeacher: false
        })
      )

      const results = await Promise.all(assignmentPromises)

      // Check if all assignments were successful
      const failedAssignments = results.filter(result => !result.success)
      const successfulAssignments = results.filter(result => result.success)

      if (successfulAssignments.length > 0) {
        const successfulTeacherNames = teachers
          .filter(teacher => newTeachers.includes(teacher.id))
          .slice(0, successfulAssignments.length)
          .map(teacher => `${teacher.user.firstName} ${teacher.user.lastName}`)
          .join(", ")

        toast({
          title: "Assignment Successful",
          description: `Successfully assigned ${successfulTeacherNames} to ${assigningSubject.name}`,
        })

        // Update the assigned teachers list
        setAssignedTeachers([...assignedTeachers, ...newTeachers.slice(0, successfulAssignments.length)])

        // Refresh the subjects list to show updated assignment counts
        await fetchSubjects()
      }

      if (failedAssignments.length > 0) {
        toast({
          title: failedAssignments.length === results.length ? "Assignment Failed" : "Partial Success",
          description: `${failedAssignments.length} assignment(s) failed. Please try again.`,
          variant: "destructive",
        })
      }

      // Close dialog only if all new assignments were successful
      if (failedAssignments.length === 0) {
        setIsAssignTeachersOpen(false)
        setAssigningSubject(null)
        setSelectedTeachers([])
        setAssignedTeachers([])
      }
    } catch (error) {
      console.error("Error creating teacher assignments:", error)
      toast({
        title: "Error",
        description: "Failed to assign teachers. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Load subjects, departments, and teachers when institution context is available
  useEffect(() => {
    if (currentInstitution?.id) {
      fetchSubjects()
      fetchDepartments()
      fetchTeachers()
    }
  }, [currentInstitution?.id])

  // Update filtered subjects when search term or subjects change
  useEffect(() => {
    if (searchTerm === "") {
      setFilteredSubjects(subjects)
      return
    }

    const filtered = subjects.filter(
      (subject) =>
        subject.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (subject.code && subject.code.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (subject.department?.name && subject.department.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (subject.description && subject.description.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    setFilteredSubjects(filtered)
  }, [searchTerm, subjects])

  // Handle form input changes
  const handleInputChange = (field: keyof SubjectFormData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  // Handle create subject
  const handleCreateSubject = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Subject name is required",
        variant: "destructive",
      })
      return
    }

    if (!currentInstitution?.id) {
      toast({
        title: "Error",
        description: "No institution context available. Please refresh the page.",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)

      // Include institution context in the form data
      const subjectData = {
        ...formData,
        institutionId: currentInstitution.id
      }

      console.log("🔄 Creating subject with data:", subjectData)

      // Try enhanced API client first, fallback to legacy if needed
      let response = await enhancedApiClient.post("/api/subjects", subjectData)

      // If enhanced client fails, try legacy method
      if (!response.success) {
        console.log("Enhanced API failed for create, trying legacy method...")
        response = await subjectService.createSubject(subjectData)
      }

      if (response.success) {
        toast({
          title: "Success",
          description: "Subject created successfully",
        })
        setIsAddSubjectOpen(false)
        setFormData({
          name: "",
          code: "",
          description: "",
          credits: 1,
          isCore: true,
          departmentId: "",
        })
        await fetchSubjects() // Refresh the list
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to create subject",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error creating subject:", error)
      toast({
        title: "Error",
        description: "Failed to create subject",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle edit subject
  const handleEditSubject = (subject: Subject) => {
    setEditingSubject(subject)
    setFormData({
      name: subject.name,
      code: subject.code || "",
      description: subject.description || "",
      credits: subject.credits || 1,
      isCore: subject.isCore,
      departmentId: subject.departmentId || "",
    })
    setIsEditSubjectOpen(true)
  }

  // Handle update subject
  const handleUpdateSubject = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Subject name is required",
        variant: "destructive",
      })
      return
    }

    if (!editingSubject) return

    try {
      setIsSubmitting(true)

      // Try enhanced API client first, fallback to legacy if needed
      let response = await enhancedApiClient.put(`/api/subjects/${editingSubject.id}`, formData)

      // If enhanced client fails, try legacy method
      if (!response.success) {
        console.log("Enhanced API failed for update, trying legacy method...")
        const legacyResponse = await fetch(`/api/subjects/${editingSubject.id}`, {
          method: 'PUT',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        })
        const result = await legacyResponse.json()
        response = {
          success: result.success,
          error: result.message || result.error,
          status: legacyResponse.status
        }
      }

      if (response.success) {
        toast({
          title: "Success",
          description: "Subject updated successfully",
        })
        setIsEditSubjectOpen(false)
        setEditingSubject(null)
        setFormData({
          name: "",
          code: "",
          description: "",
          credits: 1,
          isCore: true,
          departmentId: "",
        })
        await fetchSubjects() // Refresh the list
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to update subject",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error updating subject:", error)
      toast({
        title: "Error",
        description: "Failed to update subject",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle delete subject
  const handleDeleteSubject = async (id: string) => {
    if (!confirm("Are you sure you want to delete this subject? This action cannot be undone.")) {
      return
    }

    try {
      // Try enhanced API client first, fallback to legacy if needed
      let response = await enhancedApiClient.delete(`/api/subjects/${id}`)

      // If enhanced client fails, try legacy method
      if (!response.success) {
        console.log("Enhanced API failed for delete, trying legacy method...")
        response = await subjectService.deleteSubject(id)
      }

      if (response.success) {
        toast({
          title: "Success",
          description: "Subject deleted successfully",
        })
        await fetchSubjects() // Refresh the list
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to delete subject",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting subject:", error)
      toast({
        title: "Error",
        description: "Failed to delete subject",
        variant: "destructive",
      })
    }
  }

  // Filter subjects based on search term
  const handleSearch = (term: string) => {
    setSearchTerm(term)
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Subjects Management</h2>
          <p className="text-muted-foreground">Manage subjects, curricula, and assign teachers</p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={isAddSubjectOpen} onOpenChange={setIsAddSubjectOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Subject
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>Add New Subject</DialogTitle>
                <DialogDescription>
                  Enter the details of the new subject. Click save when you're done.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="subject-name" className="text-right">
                    Subject Name
                  </Label>
                  <Input 
                    id="subject-name" 
                    className="col-span-3"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Enter subject name"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="subject-code" className="text-right">
                    Subject Code
                  </Label>
                  <Input 
                    id="subject-code" 
                    className="col-span-3"
                    value={formData.code}
                    onChange={(e) => handleInputChange("code", e.target.value)}
                    placeholder="e.g., MATH101"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="credits" className="text-right">
                    Credits
                  </Label>
                  <Input 
                    id="credits" 
                    type="number"
                    min="1"
                    max="10"
                    className="col-span-3"
                    value={formData.credits}
                    onChange={(e) => handleInputChange("credits", parseInt(e.target.value) || 1)}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="department" className="text-right">
                    Department
                  </Label>
                  <Select value={formData.departmentId} onValueChange={(value) => handleInputChange("departmentId", value)}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id}>
                          {dept.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="description" className="text-right pt-2">
                    Description
                  </Label>
                  <Textarea 
                    id="description" 
                    className="col-span-3" 
                    rows={3}
                    value={formData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    placeholder="Enter subject description"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddSubjectOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateSubject} disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create Subject"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Edit Subject Dialog */}
          <Dialog open={isEditSubjectOpen} onOpenChange={setIsEditSubjectOpen}>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>Edit Subject</DialogTitle>
                <DialogDescription>
                  Update the subject details. Click save when you're done.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="edit-name"
                    className="col-span-3"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Enter subject name"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-code" className="text-right">
                    Code
                  </Label>
                  <Input
                    id="edit-code"
                    className="col-span-3"
                    value={formData.code}
                    onChange={(e) => handleInputChange("code", e.target.value)}
                    placeholder="Enter subject code (optional)"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-credits" className="text-right">
                    Credits
                  </Label>
                  <Input
                    id="edit-credits"
                    type="number"
                    min="1"
                    max="10"
                    className="col-span-3"
                    value={formData.credits}
                    onChange={(e) => handleInputChange("credits", parseInt(e.target.value) || 1)}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-department" className="text-right">
                    Department
                  </Label>
                  <Select value={formData.departmentId} onValueChange={(value) => handleInputChange("departmentId", value)}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id}>
                          {dept.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="edit-description" className="text-right pt-2">
                    Description
                  </Label>
                  <Textarea
                    id="edit-description"
                    className="col-span-3"
                    value={formData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    placeholder="Enter subject description (optional)"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-isCore" className="text-right">
                    Core Subject
                  </Label>
                  <div className="col-span-3">
                    <Select
                      value={formData.isCore ? "true" : "false"}
                      onValueChange={(value) => handleInputChange("isCore", value === "true")}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="true">Yes</SelectItem>
                        <SelectItem value="false">No</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsEditSubjectOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleUpdateSubject} disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Update Subject"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Subject Directory</CardTitle>
          <CardDescription>View and manage all subjects in your institution</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0 mb-4">
            <div className="relative w-full md:w-96">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search subjects..."
                className="w-full pl-8"
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Subject Name</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Credits</TableHead>
                  <TableHead>Teachers</TableHead>
                  <TableHead>Classes</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex items-center justify-center">
                        <Loader2 className="h-6 w-6 animate-spin mr-2" />
                        Loading subjects...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredSubjects.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      {searchTerm ? "No subjects found matching your search." : "No subjects found. Create your first subject to get started."}
                    </TableCell>
                  </TableRow>
                ) : (
                  (Array.isArray(filteredSubjects) ? filteredSubjects : []).map((subject) => (
                    <TableRow key={subject.id}>
                      <TableCell className="font-medium">{subject.name}</TableCell>
                      <TableCell>{subject.code || "-"}</TableCell>
                      <TableCell>{subject.department?.name || "-"}</TableCell>
                      <TableCell>{subject.credits || "-"}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{subject._count?.teacherAssignments || 0}</span>
                          {(subject._count?.teacherAssignments || 0) > 0 && (
                            <div className="flex items-center space-x-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-xs text-green-600">assigned</span>
                            </div>
                          )}
                          {(subject._count?.teacherAssignments || 0) === 0 && (
                            <div className="flex items-center space-x-1">
                              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                              <span className="text-xs text-orange-600">unassigned</span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{subject._count?.classes || 0}</TableCell>
                      <TableCell>
                        <Badge variant={subject.isActive ? "default" : "secondary"}>
                          {subject.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditSubject(subject)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <BookOpen className="mr-2 h-4 w-4" />
                              View Curriculum
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleAssignTeachers(subject)}>
                              <Users className="mr-2 h-4 w-4" />
                              Assign Teachers
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Clock className="mr-2 h-4 w-4" />
                              Set Schedule
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className="text-destructive"
                              onClick={() => handleDeleteSubject(subject.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Assign Teachers Dialog */}
      <Dialog open={isAssignTeachersOpen} onOpenChange={setIsAssignTeachersOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Assign Teachers to {assigningSubject?.name}</DialogTitle>
            <DialogDescription>
              Select teachers to assign to this subject. Teachers will be able to manage classes for this subject.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            {/* Currently Assigned Teachers */}
            {assignedTeachers.length > 0 && (
              <div className="space-y-3">
                <Label className="text-base font-semibold">Currently Assigned Teachers</Label>
                <div className="border rounded-md p-4 bg-muted/50">
                  <div className="space-y-2">
                    {teachers
                      .filter(teacher => assignedTeachers.includes(teacher.id))
                      .map((teacher) => (
                        <div key={teacher.id} className="flex items-center space-x-3 p-2 bg-background rounded border">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <div className="flex-1">
                            <p className="font-medium text-green-700">
                              {teacher.user.firstName} {teacher.user.lastName}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {teacher.user.email}
                              {teacher.specialization && ` • ${teacher.specialization}`}
                            </p>
                          </div>
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                            Assigned
                          </span>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            )}

            {/* Available Teachers */}
            <div className="space-y-3">
              <Label className="text-base font-semibold">
                {assignedTeachers.length > 0 ? "Available Teachers" : "Select Teachers to Assign"}
              </Label>
              {isLoadingAssignments ? (
                <div className="border rounded-md p-4 text-center">
                  <p className="text-sm text-muted-foreground">Loading teachers and assignments...</p>
                </div>
              ) : teachers.length === 0 ? (
                <div className="border rounded-md p-4 text-center">
                  <p className="text-sm text-muted-foreground">No teachers available</p>
                </div>
              ) : (
                <div className="max-h-60 overflow-y-auto border rounded-md p-4 space-y-2">
                  {teachers
                    .filter(teacher => !assignedTeachers.includes(teacher.id))
                    .map((teacher) => (
                      <div key={teacher.id} className="flex items-center space-x-3 p-2 hover:bg-muted/50 rounded">
                        <Checkbox
                          id={teacher.id}
                          checked={selectedTeachers.includes(teacher.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedTeachers([...selectedTeachers, teacher.id])
                            } else {
                              setSelectedTeachers(selectedTeachers.filter(id => id !== teacher.id))
                            }
                          }}
                        />
                        <Label htmlFor={teacher.id} className="flex-1 cursor-pointer">
                          <div>
                            <p className="font-medium">
                              {teacher.user.firstName} {teacher.user.lastName}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {teacher.user.email}
                              {teacher.specialization && ` • ${teacher.specialization}`}
                            </p>
                          </div>
                        </Label>
                      </div>
                    ))}
                  {teachers.filter(teacher => !assignedTeachers.includes(teacher.id)).length === 0 && (
                    <p className="text-sm text-muted-foreground text-center py-4">
                      All available teachers are already assigned to this subject
                    </p>
                  )}
                </div>
              )}
              {selectedTeachers.length > 0 && (
                <div className="flex items-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <p className="text-sm text-blue-700 font-medium">
                    {selectedTeachers.length} new teacher(s) selected for assignment
                  </p>
                </div>
              )}
            </div>
          </div>
          <DialogFooter className="flex justify-between items-center">
            <div className="flex-1">
              {assignedTeachers.length > 0 && (
                <p className="text-sm text-muted-foreground">
                  {assignedTeachers.length} teacher(s) currently assigned
                </p>
              )}
            </div>
            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsAssignTeachersOpen(false)
                  setAssigningSubject(null)
                  setSelectedTeachers([])
                  setAssignedTeachers([])
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleSubmitTeacherAssignments}
                disabled={isSubmitting || selectedTeachers.length === 0 || isLoadingAssignments}
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Assigning...
                  </>
                ) : (
                  `Assign ${selectedTeachers.length} Teacher${selectedTeachers.length !== 1 ? 's' : ''}`
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default function SubjectsPage() {
  return (
    <InstitutionProtectedPage>
      <SubjectsContent />
    </InstitutionProtectedPage>
  )
}
