const express = require('express');
const { authenticate } = require('../../../middleware/authenticate');
const { PrismaClient } = require('@prisma/client');

const router = express.Router();
const prisma = new PrismaClient();

// Get all classes for a school
router.get('/', authenticate, async (req, res) => {
  try {
    console.log('🔍 Backend Classes GET: Request received from user:', req.user?.id, 'role:', req.user?.role);

    // Get pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get classes from database
    const [classes, totalCount] = await Promise.all([
      prisma.class.findMany({
        skip,
        take: limit,
        where: {
          isActive: true
        },
        include: {
          school: {
            select: {
              id: true,
              name: true
            }
          },
          academicYear: {
            select: {
              id: true,
              name: true,
              isActive: true
            }
          },
          _count: {
            select: {
              studentEnrollments: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.class.count({
        where: {
          isActive: true
        }
      })
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    console.log(`✅ Backend Classes GET: Found ${classes.length} classes (page ${page}/${totalPages})`);

    res.status(200).json({
      success: true,
      data: {
        classes,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });

  } catch (error) {
    console.error('❌ Backend Classes GET: Error fetching classes:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch classes. Please try again.'
    });
  }
});

// Create a new class
router.post('/', authenticate, async (req, res) => {
  try {
    console.log('🔍 Backend Classes POST: Request received from user:', req.user?.id, 'role:', req.user?.role);
    console.log('🔍 Backend Classes POST: Request body:', req.body);

    const {
      name,
      gradeLevel,
      section,
      capacity,
      roomNumber,
      schoolId,
      academicYearId,
      subjects = []
    } = req.body;

    // Validate required fields
    if (!name || !gradeLevel || !academicYearId) {
      console.log('❌ Backend Classes POST: Missing required fields');
      return res.status(400).json({
        success: false,
        error: 'Name, grade level, and academic year are required'
      });
    }

    // For now, let's use a default school ID if not provided
    let finalSchoolId = schoolId;
    if (!finalSchoolId) {
      // Try to get the first school from the database
      const firstSchool = await prisma.school.findFirst({
        where: { isActive: true }
      });
      if (firstSchool) {
        finalSchoolId = firstSchool.id;
        console.log('🔍 Backend Classes POST: Using default school ID:', finalSchoolId);
      } else {
        return res.status(400).json({
          success: false,
          error: 'No active school found. Please specify a school ID.'
        });
      }
    }

    // Create the class
    const newClass = await prisma.class.create({
      data: {
        name: name.trim(),
        gradeLevel: gradeLevel.trim(),
        section: section?.trim() || null,
        capacity: capacity ? parseInt(capacity) : null,
        roomNumber: roomNumber?.trim() || null,
        schoolId: finalSchoolId,
        academicYearId: academicYearId,
        isActive: true
      },
      include: {
        school: {
          select: {
            id: true,
            name: true
          }
        },
        academicYear: {
          select: {
            id: true,
            name: true,
            isActive: true
          }
        }
      }
    });

    console.log('✅ Backend Classes POST: Class created successfully:', newClass.id);

    res.status(201).json({
      success: true,
      data: newClass,
      message: 'Class created successfully'
    });

  } catch (error) {
    console.error('❌ Backend Classes POST: Error creating class:', error);

    // Handle specific Prisma errors
    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        error: 'A class with this name already exists in the selected academic year'
      });
    }

    if (error.code === 'P2003') {
      return res.status(400).json({
        success: false,
        error: 'Invalid school ID or academic year ID provided'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to create class. Please try again.'
    });
  }
});

module.exports = router;
