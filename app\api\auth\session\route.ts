import { NextRequest, NextResponse } from "next/server"
import { getValidAuthToken } from "@/lib/auth-utils"
import { cookies } from "next/headers"
import { verify } from "jsonwebtoken"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get("session")?.value

    // First try to use session token (local JWT) for quick validation
    if (sessionToken) {
      try {
        const decoded = verify(sessionToken, process.env.JWT_SECRET!) as any

        // Also verify with backend to ensure token is still valid
        const token = await getValidAuthToken(request)
        if (token) {
          const backendResponse = await fetch(`${BACKEND_URL}/api/v1/auth/me`, {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            signal: AbortSignal.timeout(5000),
          })

          if (backendResponse.ok) {
            const backendData = await backendResponse.json()
            const user = backendData.data?.user || backendData.user

            return NextResponse.json({
              authenticated: true,
              user: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                isEmailVerified: user.isEmailVerified,
                institutions: user.institutions || [],
                schools: user.schools || []
              },
              sessionValid: true
            })
          }
        }

        // If backend validation fails, return session data but mark as potentially invalid
        return NextResponse.json({
          authenticated: true,
          user: {
            id: decoded.id,
            email: decoded.email,
            firstName: decoded.firstName || "",
            lastName: decoded.lastName || "",
            role: decoded.role,
            isEmailVerified: decoded.isEmailVerified || false,
          },
          sessionValid: false,
          warning: "Session token valid but backend validation failed"
        })
      } catch (jwtError) {
        console.error("Session token verification failed:", jwtError)
        // Continue to try access token only
      }
    }

    // Fallback to access token validation
    const token = await getValidAuthToken(request)
    if (!token) {
      return NextResponse.json({
        authenticated: false,
        user: null,
        sessionValid: false,
        error: "No valid authentication token"
      }, { status: 401 })
    }

    // Verify with backend
    const response = await fetch(`${BACKEND_URL}/api/v1/auth/me`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      signal: AbortSignal.timeout(5000),
    })

    if (!response.ok) {
      return NextResponse.json({
        authenticated: false,
        user: null,
        sessionValid: false,
        error: "Backend authentication failed"
      }, { status: 401 })
    }

    const data = await response.json()
    const user = data.data?.user || data.user

    if (!user) {
      return NextResponse.json({
        authenticated: false,
        user: null,
        sessionValid: false,
        error: "User data not found"
      }, { status: 401 })
    }

    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        institutions: user.institutions || [],
        schools: user.schools || []
      },
      sessionValid: true
    })
  } catch (error) {
    console.error("Session check error:", error)
    return NextResponse.json({
      authenticated: false,
      user: null,
      sessionValid: false,
      error: "Session check failed"
    }, { status: 500 })
  }
}