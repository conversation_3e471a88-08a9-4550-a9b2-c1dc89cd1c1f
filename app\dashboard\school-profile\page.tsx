"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { useTenant } from "@/contexts/tenant-context"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"
import { Loader2, School, MapPin, Phone, Mail, Globe, Users, GraduationCap } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface SchoolProfile {
  id: string
  name: string
  type: string
  address?: string
  city?: string
  state?: string
  country?: string
  postalCode?: string
  phoneNumber?: string
  email?: string
  website?: string
  isActive: boolean
  institutionId: string
  institution?: {
    id: string
    name: string
  }
}

export default function SchoolProfilePage() {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [schoolProfile, setSchoolProfile] = useState<SchoolProfile | null>(null)
  const [formData, setFormData] = useState<Partial<SchoolProfile>>({})
  
  const { toast } = useToast()
  const { currentUser, currentSchool, isSchoolAdmin } = useTenant()
  const { user } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // Check if user is authenticated and is a school admin
    if (!user) {
      console.log("User not authenticated, redirecting to login")
      router.push('/auth/login')
      return
    }

    if (!isSchoolAdmin) {
      console.log("User is not a school admin, redirecting to dashboard")
      router.push('/dashboard')
      return
    }

    console.log("School profile: currentSchool changed:", currentSchool)
    console.log("School profile: isSchoolAdmin:", isSchoolAdmin)
    console.log("School profile: user role:", user?.role)

    if (currentSchool?.id) {
      console.log("School profile: Fetching school data for ID:", currentSchool.id)
      fetchSchoolProfile()
    } else {
      console.log("School profile: No currentSchool available")
      setLoading(false)
    }
  }, [currentSchool, user, isSchoolAdmin, router])

  const fetchSchoolProfile = async () => {
    if (!currentSchool?.id) {
      console.log("No school ID available")
      return
    }

    setLoading(true)
    try {
      console.log("Fetching school profile from API:", `/api/schools/${currentSchool.id}`)
      const response = await fetch(`/api/schools/${currentSchool.id}`, {
        credentials: 'include',
      })

      console.log("API response status:", response.status)
      console.log("API response ok:", response.ok)

      if (!response.ok) {
        const errorText = await response.text()
        console.error("API error response:", errorText)
        throw new Error(`Failed to fetch school profile: ${response.status} - ${errorText}`)
      }

      const data = await response.json()
      console.log("Raw API response data:", data)

      const school = data.data?.school || data.school || data

      console.log("Extracted school profile:", school)
      setSchoolProfile(school)
      setFormData(school)
    } catch (error) {
      console.error('Error fetching school profile:', error)
      console.log('Using currentSchool data as fallback:', currentSchool)

      // Use currentSchool data as fallback
      if (currentSchool) {
        setSchoolProfile(currentSchool)
        setFormData(currentSchool)
        toast.warning('Using cached school data. Some information may be limited.')
      } else {
        toast.error(`Failed to load school profile: ${error.message}`)
      }
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof SchoolProfile, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    if (!currentSchool?.id) {
      toast.error('No school selected')
      return
    }

    setSaving(true)
    try {
      const response = await fetch(`/api/schools/${currentSchool.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        throw new Error(`Failed to update school profile: ${response.status}`)
      }

      const data = await response.json()
      const updatedSchool = data.data?.school || data.school || data
      
      setSchoolProfile(updatedSchool)
      toast.success('School profile updated successfully')
    } catch (error) {
      console.error('Error updating school profile:', error)
      toast.error('Failed to update school profile')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading school profile...</p>
        </div>
      </div>
    )
  }

  if (!currentSchool) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <School className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No School Available</h3>
          <p className="text-muted-foreground">
            You don't have access to any school profile.
          </p>
          <p className="text-xs text-muted-foreground mt-2">
            Debug: isSchoolAdmin={isSchoolAdmin.toString()}, user role={user?.role}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">School Profile</h1>
          <p className="text-muted-foreground">
            Manage your school information and settings
          </p>
        </div>
      </div>

      <Tabs defaultValue="information" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
          <TabsTrigger value="information">Information</TabsTrigger>
     
        </TabsList>

        <TabsContent value="information" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <School className="h-5 w-5" />
                School Information
              </CardTitle>
              <CardDescription>
                Update your school's basic information and contact details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name">School Name</Label>
                  <Input
                    id="name"
                    value={formData.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter school name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">School Type</Label>
                  <Select
                    value={formData.type || ''}
                    onValueChange={(value) => handleInputChange('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select school type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PRIMARY">Primary School</SelectItem>
                      <SelectItem value="SECONDARY">Secondary School</SelectItem>
                      <SelectItem value="TERTIARY">Tertiary Institution</SelectItem>
                      <SelectItem value="KINDERGARTEN">Kindergarten</SelectItem>
                      <SelectItem value="VOCATIONAL">Vocational School</SelectItem>
                      <SelectItem value="SPECIAL_NEEDS">Special Needs School</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email || ''}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    value={formData.phoneNumber || ''}
                    onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                    placeholder="+260 XXX XXX XXX"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={formData.website || ''}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    placeholder="https://www.school.com"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  value={formData.address || ''}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="Enter school address"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={formData.city || ''}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    placeholder="City"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="state">State/Province</Label>
                  <Input
                    id="state"
                    value={formData.state || ''}
                    onChange={(e) => handleInputChange('state', e.target.value)}
                    placeholder="State or Province"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value={formData.country || ''}
                    onChange={(e) => handleInputChange('country', e.target.value)}
                    placeholder="Country"
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSave} disabled={saving}>
                  {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Save Changes
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* <TabsContent value="statistics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                <GraduationCap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  Enrolled students
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Teachers</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  Active teachers
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Classes</CardTitle>
                <School className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  Active classes
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent> */}
      </Tabs>
    </div>
  )
}
