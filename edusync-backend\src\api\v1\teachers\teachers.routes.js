const express = require('express');
const router = express.Router();
const {
  getTeachers,
  getTeacherById,
  createTeacher,
  updateTeacher,
  deleteTeacher,
  getTeacherAssignments,
  getTeacherSchedule,
  getTeachersStats,
  createTeacherAssignment,
  updateTeacherAssignment,
  deleteTeacherAssignment,
  assignTeacherDepartment,
  removeTeacherDepartment
} = require('./teachers.controller');
const { authenticate, restrictTo } = require('../../../middleware/authenticate');

// Apply authentication to all routes
router.use(authenticate);

// GET /api/v1/teachers/stats - Get teachers statistics
router.get('/stats', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'), getTeachersStats);

// GET /api/v1/teachers/:id/assignments - Get teacher assignments
router.get('/:id/assignments', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'), getTeacherAssignments);

// POST /api/v1/teachers/assignments - Create teacher assignment (assign subject to teacher)
router.post('/assignments', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'), createTeacherAssignment);

// PUT /api/v1/teachers/assignments/:id - Update teacher assignment
router.put('/assignments/:id', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'), updateTeacherAssignment);

// DELETE /api/v1/teachers/assignments/:id - Delete teacher assignment
router.delete('/assignments/:id', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'), deleteTeacherAssignment);

// POST /api/v1/teachers/department - Assign department to teacher
router.post('/department', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'), assignTeacherDepartment);

// DELETE /api/v1/teachers/:id/department - Remove department from teacher
router.delete('/:id/department', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'), removeTeacherDepartment);

// GET /api/v1/teachers/:id/schedule - Get teacher schedule
router.get('/:id/schedule', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER'), getTeacherSchedule);

// GET /api/v1/teachers - Get all teachers with filtering and pagination
router.get('/', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'), getTeachers);

// GET /api/v1/teachers/:id - Get teacher by ID
router.get('/:id', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'), getTeacherById);

// POST /api/v1/teachers - Create new teacher (admin only)
router.post('/', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'), createTeacher);

// PUT /api/v1/teachers/:id - Update teacher (admin only)
router.put('/:id', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN'), updateTeacher);

// DELETE /api/v1/teachers/:id - Delete teacher (admin only)
router.delete('/:id', restrictTo('SUPER_ADMIN', 'INSTITUTION_ADMIN'), deleteTeacher);

module.exports = router;
