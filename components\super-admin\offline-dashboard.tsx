"use client"

import { <PERSON><PERSON><PERSON>riangle, Server, Database, Users, Building2, <PERSON>ting<PERSON>, BarChart3 } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"

export function OfflineDashboard() {
  const mockStats = [
    {
      title: "Total Institutions",
      value: "---",
      description: "Backend required",
      icon: Building2,
    },
    {
      title: "Total Users",
      value: "---",
      description: "Backend required",
      icon: Users,
    },
    {
      title: "Active Subscriptions",
      value: "---",
      description: "Backend required",
      icon: BarChart3,
    },
    {
      title: "System Status",
      value: "Offline",
      description: "Backend not available",
      icon: Server,
    },
  ]

  return (
    <div className="space-y-6">
      {/* Backend Status Alert */}
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <div className="font-medium">Backend Server Not Available</div>
          <div className="text-sm mt-1">
            The super admin dashboard requires the backend API to be running. 
            Please start the backend server to access real data and functionality.
          </div>
        </AlertDescription>
      </Alert>

      {/* Mock Dashboard */}
      <div>
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Super Admin Dashboard</h1>
          <Badge variant="destructive" className="flex items-center gap-1">
            <Database className="h-3 w-3" />
            Offline Mode
          </Badge>
        </div>
        <p className="text-muted-foreground mt-2">
          System overview and management (Demo Mode - Backend Required)
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {mockStats.map((stat, index) => (
          <Card key={index} className="relative">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium opacity-50">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground opacity-50" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-muted-foreground">
                {stat.value}
              </div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
            <div className="absolute inset-0 bg-gray-50/50 dark:bg-gray-900/50 rounded-lg pointer-events-none" />
          </Card>
        ))}
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Getting Started
          </CardTitle>
          <CardDescription>
            To access the full super admin dashboard functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium">1. Start the Backend Server</h4>
            <p className="text-sm text-muted-foreground">
              Make sure your backend API server is running and accessible at the configured URL.
            </p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">2. Verify Database Connection</h4>
            <p className="text-sm text-muted-foreground">
              Ensure your database is running and the backend can connect to it.
            </p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">3. Check Environment Variables</h4>
            <p className="text-sm text-muted-foreground">
              Verify that BACKEND_URL and other required environment variables are properly configured.
            </p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">4. Create Super Admin User</h4>
            <p className="text-sm text-muted-foreground">
              Use the backend API or database seeding to create a super admin user account.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Available Features */}
      <Card>
        <CardHeader>
          <CardTitle>Available Features (Offline Mode)</CardTitle>
          <CardDescription>
            These features work without the backend
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm">
            <li className="flex items-center gap-2">
              <div className="h-2 w-2 bg-green-500 rounded-full" />
              User interface and navigation
            </li>
            <li className="flex items-center gap-2">
              <div className="h-2 w-2 bg-green-500 rounded-full" />
              Theme switching (light/dark mode)
            </li>
            <li className="flex items-center gap-2">
              <div className="h-2 w-2 bg-green-500 rounded-full" />
              Responsive design testing
            </li>
            <li className="flex items-center gap-2">
              <div className="h-2 w-2 bg-red-500 rounded-full" />
              Data fetching and API calls
            </li>
            <li className="flex items-center gap-2">
              <div className="h-2 w-2 bg-red-500 rounded-full" />
              Institution management
            </li>
            <li className="flex items-center gap-2">
              <div className="h-2 w-2 bg-red-500 rounded-full" />
              User management
            </li>
            <li className="flex items-center gap-2">
              <div className="h-2 w-2 bg-red-500 rounded-full" />
              Analytics and reporting
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
