const fs = require('fs');
const path = require('path');

function testDirectorySetup() {
  console.log('Testing directory setup...');

  const uploadsDir = path.join(process.cwd(), 'uploads');
  const institutionsDir = path.join(uploadsDir, 'institutions');

  console.log('Uploads directory:', uploadsDir);
  console.log('Exists:', fs.existsSync(uploadsDir));

  console.log('Institutions directory:', institutionsDir);
  console.log('Exists:', fs.existsSync(institutionsDir));

  // Test file creation
  try {
    const testFile = path.join(uploadsDir, 'test.txt');
    fs.writeFileSync(testFile, 'test content');
    console.log('✅ Can write to uploads directory');
    fs.unlinkSync(testFile);
    console.log('✅ Can delete from uploads directory');
  } catch (error) {
    console.log('❌ Cannot write to uploads directory:', error.message);
  }
}

// Run the test
testDirectorySetup();
