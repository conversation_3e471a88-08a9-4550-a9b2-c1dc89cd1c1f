"use client"

import { useRef } from "react"
import { motion, useInView } from "framer-motion"
import {
  Users,
  BookOpen,
  Calendar,
  CreditCard,
  BarChart,
  MessageSquare,
  Clock,
  Shield,
  Zap,
  Globe,
  Bell,
  FileText,
} from "lucide-react"
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import Image from "next/image"

export function FeaturesSection() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const featureCategories = [
    {
      id: "administration",
      name: "Administration",
      description: "Streamline your school's administrative processes",
      features: [
        {
          icon: <Users className="h-10 w-10 text-emerald-500" />,
          title: "Student Management",
          description: "Easily manage student records, attendance, and academic performance in one place.",
        },
        {
          icon: <Calendar className="h-10 w-10 text-emerald-500" />,
          title: "Timetable & Scheduling",
          description: "Create and manage class schedules, exams, and school events efficiently.",
        },
        {
          icon: <CreditCard className="h-10 w-10 text-emerald-500" />,
          title: "Fee Management",
          description: "Track fee payments, generate invoices, and manage financial records.",
        },
        {
          icon: <Clock className="h-10 w-10 text-emerald-500" />,
          title: "Attendance Tracking",
          description: "Record and monitor student and staff attendance with detailed reports.",
        },
      ],
      image: "/placeholder.svg?height=400&width=500",
    },
    {
      id: "academic",
      name: "Academic",
      description: "Enhance teaching and learning experiences",
      features: [
        {
          icon: <BookOpen className="h-10 w-10 text-emerald-500" />,
          title: "Curriculum Management",
          description: "Organize subjects, classes, and learning materials for different academic levels.",
        },
        {
          icon: <FileText className="h-10 w-10 text-emerald-500" />,
          title: "Assignment Management",
          description: "Create, distribute, and grade assignments digitally with ease.",
        },
        {
          icon: <BarChart className="h-10 w-10 text-emerald-500" />,
          title: "Performance Analytics",
          description: "Gain insights into student performance, attendance trends, and school metrics.",
        },
        {
          icon: <Globe className="h-10 w-10 text-emerald-500" />,
          title: "Resource Library",
          description: "Centralized repository for educational resources and learning materials.",
        },
      ],
      image: "/placeholder.svg?height=400&width=500",
    },
    {
      id: "communication",
      name: "Communication",
      description: "Connect your entire school community",
      features: [
        {
          icon: <MessageSquare className="h-10 w-10 text-emerald-500" />,
          title: "Communication Tools",
          description: "Connect teachers, students, and parents through notifications and messaging.",
        },
        {
          icon: <Bell className="h-10 w-10 text-emerald-500" />,
          title: "Announcements",
          description: "Broadcast important announcements to specific groups or the entire school.",
        },
        {
          icon: <Zap className="h-10 w-10 text-emerald-500" />,
          title: "Real-time Updates",
          description: "Instant notifications for grades, attendance, and important events.",
        },
        {
          icon: <Shield className="h-10 w-10 text-emerald-500" />,
          title: "Multi-tenant Security",
          description: "Secure data isolation for each institution with role-based access control.",
        },
      ],
      image: "/placeholder.svg?height=400&width=500",
    },
  ]

  return (
    <section id="features" ref={ref} className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="text-3xl sm:text-4xl font-bold text-gray-900"
          >
            Comprehensive School Management Features
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto"
          >
            Everything you need to run your educational institution efficiently
          </motion.p>
        </div>

        <Tabs defaultValue="administration" className="max-w-5xl mx-auto">
          <TabsList className="grid grid-cols-3 mb-12">
            {featureCategories.map((category) => (
              <TabsTrigger
                key={category.id}
                value={category.id}
                className="data-[state=active]:bg-emerald-50 data-[state=active]:text-emerald-600 py-3"
              >
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>

          {featureCategories.map((category) => (
            <TabsContent key={category.id} value={category.id}>
              <div className="flex flex-col lg:flex-row gap-12 items-center">
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                  className="lg:w-1/2"
                >
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{category.description}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {category.features.map((feature, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        className="bg-gray-50 p-6 rounded-lg hover:shadow-md transition-shadow border border-gray-100"
                      >
                        <div className="mb-4">{feature.icon}</div>
                        <h4 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h4>
                        <p className="text-gray-600">{feature.description}</p>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                  className="lg:w-1/2"
                >
                  <div className="relative rounded-xl overflow-hidden shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-tr from-emerald-500/20 to-transparent z-10 rounded-xl" />
                    <Image
                      src={category.image || "/placeholder.svg"}
                      alt={category.name}
                      width={500}
                      height={400}
                      className="w-full h-auto object-cover rounded-xl"
                    />
                  </div>
                </motion.div>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  )
}
