import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import { sign } from "jsonwebtoken"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { email, password, mfaToken } = body

    // Call your backend API for authentication
    const response = await fetch(`${process.env.BACKEND_URL}/api/v1/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email, password, mfaToken }),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        {
          success: false,
          message: data.message || "Login failed",
          mfaRequired: data.mfaRequired,
        },
        { status: response.status }
      )
    }

    // Store the access token from backend
    const cookieStore = await cookies()
    const accessToken = data.data?.accessToken || data.accessToken
    console.log("🔍 Login: Access token from backend:", accessToken ? "✅ Found" : "❌ Not found")
    if (accessToken) {
      console.log("🔍 Login: Setting accessToken cookie...")
      cookieStore.set("accessToken", accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 15 * 60, // 15 minutes to match backend token expiration
      })
      console.log("✅ Login: accessToken cookie set successfully")
    } else {
      console.log("❌ Login: No access token to store")
    }

    // Store refresh token from backend response cookies
    const setCookieHeader = response.headers.get('set-cookie')
    if (setCookieHeader) {
      const refreshTokenMatch = setCookieHeader.match(/refreshToken=([^;]+)/)
      if (refreshTokenMatch) {
        cookieStore.set("refreshToken", refreshTokenMatch[1], {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: "strict",
          maxAge: 7 * 24 * 60 * 60, // 7 days
        })
      }
    }

    // Create session token
    const sessionToken = sign(
      {
        id: data.data.user.id,
        email: data.data.user.email,
        role: data.data.user.role,
      },
      process.env.JWT_SECRET!,
      { expiresIn: "1d" }
    )

    // Set session cookie
    cookieStore.set("session", sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 24 * 60 * 60, // 1 day
    })

    return NextResponse.json({
      success: true,
      user: {
        id: data.data.user.id,
        email: data.data.user.email,
        firstName: data.data.user.firstName,
        lastName: data.data.user.lastName,
        role: data.data.user.role,
        isEmailVerified: data.data.user.isEmailVerified,
      },
    })
  } catch (error) {
    console.error("Login error:", error)
    return NextResponse.json(
      { success: false, message: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}