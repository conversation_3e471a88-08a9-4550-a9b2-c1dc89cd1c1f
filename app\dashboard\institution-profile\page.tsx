"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { InstitutionProfileHeader } from "@/components/institutions/institution-profile-header"
import { InstitutionInformationForm } from "@/components/institutions/institution-information-form"
import { InstitutionSettingsForm } from "@/components/institutions/institution-settings-form"
import { useToast } from "@/hooks/use-toast"
import { useTenant } from "@/contexts/tenant-context"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"
import { Loader2 } from "lucide-react"

interface Institution {
  id: string
  name: string
  domain?: string
  logo?: string
  primaryColor?: string
  secondaryColor?: string
  isActive: boolean
  subscriptionStatus: string
  subscriptionEndDate?: string
  address?: string
  city?: string
  state?: string
  country?: string
  postalCode?: string
  phoneNumber?: string
  email?: string
  website?: string
  verificationStatus: boolean
  studentCount?: number
  teacherCount?: number
  referralSource?: string
  specialRequirements?: string
  createdAt: string
  updatedAt: string
}

export default function InstitutionProfilePage() {
  const [institution, setInstitution] = useState<Institution | null>(null)
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()
  const { currentInstitution } = useTenant()
  const { user } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      console.log("User not authenticated, redirecting to login")
      router.push('/auth/login')
      return
    }

    console.log("Institution profile: currentInstitution changed:", currentInstitution)
    if (currentInstitution?.id) {
      fetchInstitutionProfile()
    } else {
      console.log("Institution profile: No currentInstitution available")
      setLoading(false)
      // Don't use fallback - let the user see the "no institution" message
    }
  }, [currentInstitution, user, router])

  const fetchInstitutionProfile = async () => {
    if (!currentInstitution?.id) {
      console.log("No institution ID available")
      return
    }

    try {
      setLoading(true)

      console.log("Fetching institution profile for ID:", currentInstitution.id)

      const response = await fetch(`/api/institutions/${currentInstitution.id}`, {
        credentials: 'include',
      })

      console.log("Institution response status:", response.status)

      if (!response.ok) {
        throw new Error(`Failed to fetch institution: ${response.status}`)
      }

      const data = await response.json()
      console.log("Institution data:", data)

      // Handle the response format from backend
      const institution = data.data?.institution || data.institution || data
      setInstitution(institution)
    } catch (error) {
      console.error("Error fetching institution profile:", error)
      toast({
        title: "Error",
        description: "Failed to load institution profile. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleProfileUpdate = async (updatedData: Partial<Institution>) => {
    try {
      if (!institution?.id) {
        throw new Error("No institution ID available")
      }

      console.log("Updating institution with ID:", institution.id)
      console.log("Update data:", updatedData)

      const response = await fetch(`/api/institutions/${institution.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: 'include',
        body: JSON.stringify(updatedData),
      })

      console.log("Update response status:", response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error("Update failed:", errorText)
        throw new Error("Failed to update institution profile")
      }

      const data = await response.json()
      console.log("Update response data:", data)
      
      // Handle the response format from backend
      const institutionData = data.data?.institution || data.institution || data
      setInstitution(institutionData)
      
      toast({
        title: "Success",
        description: "Institution profile updated successfully.",
      })
    } catch (error) {
      console.error("Error updating institution profile:", error)
      toast({
        title: "Error",
        description: "Failed to update institution profile. Please try again.",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading institution profile...</span>
        </div>
      </div>
    )
  }

  // Show message if user has no institution association
  if (!currentInstitution) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center max-w-md">
          <div className="mb-4">
            <svg className="h-16 w-16 mx-auto text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold mb-2">No Institution Access</h3>
          <p className="text-muted-foreground mb-4">
            You are not currently associated with any institution. Please contact your administrator to get access to an institution.
          </p>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>If you believe this is an error:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>Contact your institution administrator</li>
              <li>Verify your account has been properly set up</li>
              <li>Try logging out and logging back in</li>
            </ul>
          </div>
        </div>
      </div>
    )
  }

  if (!institution) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <p className="text-lg font-medium">Institution profile not found</p>
          <p className="text-muted-foreground">Unable to load institution profile.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Institution Profile</h1>
          <p className="text-muted-foreground">Manage your institution information, settings, and view statistics.</p>
        </div>
      </div>

      <InstitutionProfileHeader institution={institution} onUpdate={handleProfileUpdate} />

      <Tabs defaultValue="information" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="information">Information</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="information" className="space-y-4">
          <InstitutionInformationForm 
            institution={institution} 
            onUpdate={handleProfileUpdate} 
          />
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <InstitutionSettingsForm 
            institution={institution} 
            onUpdate={handleProfileUpdate} 
          />
        </TabsContent>
      </Tabs>
    </div>
  )
} 