"use client"

import { useState } from "react"
import { format } from "date-fns"
import { CalendarIcon, Check, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { cn } from "@/lib/utils"

// Mock data for classes
const classes = [
  { id: 1, name: "Class 10A" },
  { id: 2, name: "Class 9B" },
  { id: 3, name: "Class 8C" },
  { id: 4, name: "Class 7D" },
]

// Mock data for students
const students = [
  { id: 1, name: "<PERSON>", rollNo: "101", attendance: {} },
  { id: 2, name: "<PERSON>", rollNo: "102", attendance: {} },
  { id: 3, name: "<PERSON>", rollNo: "103", attendance: {} },
  { id: 4, name: "<PERSON>", roll<PERSON>o: "104", attendance: {} },
  { id: 5, name: "<PERSON> Norton", rollNo: "105", attendance: {} },
  { id: 6, name: "Fiona Gallagher", rollNo: "106", attendance: {} },
  { id: 7, name: "<PERSON>", rollNo: "107", attendance: {} },
  { id: 8, name: "Hannah Baker", rollNo: "108", attendance: {} },
]

export default function AttendancePage() {
  const [date, setDate] = useState<Date>(new Date())
  const [selectedClass, setSelectedClass] = useState<string>("1")
  const [attendanceData, setAttendanceData] = useState<Record<string, Record<string, boolean>>>({})

  const dateString = format(date, "yyyy-MM-dd")

  // Initialize attendance data if not exists
  if (!attendanceData[dateString]) {
    const initialData: Record<string, boolean> = {}
    students.forEach((student) => {
      initialData[student.id.toString()] = true // Default present
    })
    setAttendanceData((prev) => ({ ...prev, [dateString]: initialData }))
  }

  const handleAttendanceChange = (studentId: number, isPresent: boolean) => {
    setAttendanceData((prev) => ({
      ...prev,
      [dateString]: {
        ...prev[dateString],
        [studentId]: isPresent,
      },
    }))
  }

  const saveAttendance = () => {
    // In a real app, this would save to a database
    console.log("Saving attendance for", dateString, attendanceData[dateString])
    // Show success message
    alert("Attendance saved successfully!")
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Attendance Management</h2>
          <p className="text-muted-foreground">Track and manage student attendance records</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Mark Attendance</CardTitle>
          <CardDescription>Select a class and date to mark attendance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 md:flex-row md:items-end md:space-x-4 md:space-y-0">
            <div className="space-y-2">
              <label className="text-sm font-medium">Class</label>
              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select class" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id.toString()}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn("w-[240px] justify-start text-left font-normal", !date && "text-muted-foreground")}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar mode="single" selected={date} onSelect={(date) => date && setDate(date)} initialFocus />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="mt-6">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Roll No</TableHead>
                  <TableHead>Student Name</TableHead>
                  <TableHead className="text-center">Status</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {students.map((student) => (
                  <TableRow key={student.id}>
                    <TableCell>{student.rollNo}</TableCell>
                    <TableCell>{student.name}</TableCell>
                    <TableCell className="text-center">
                      {attendanceData[dateString]?.[student.id] ? (
                        <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                          Present
                        </span>
                      ) : (
                        <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                          Absent
                        </span>
                      )}
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex justify-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className={cn(
                            "h-8 w-8 p-0",
                            attendanceData[dateString]?.[student.id] && "bg-green-100 text-green-800",
                          )}
                          onClick={() => handleAttendanceChange(student.id, true)}
                        >
                          <Check className="h-4 w-4" />
                          <span className="sr-only">Present</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className={cn(
                            "h-8 w-8 p-0",
                            !attendanceData[dateString]?.[student.id] && "bg-red-100 text-red-800",
                          )}
                          onClick={() => handleAttendanceChange(student.id, false)}
                        >
                          <X className="h-4 w-4" />
                          <span className="sr-only">Absent</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <div className="mt-6 flex justify-end">
            <Button onClick={saveAttendance}>Save Attendance</Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Attendance Statistics</CardTitle>
          <CardDescription>View attendance statistics for the selected class</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="rounded-lg border p-4">
              <div className="text-sm font-medium text-muted-foreground">Present Today</div>
              <div className="text-2xl font-bold">
                {Object.values(attendanceData[dateString] || {}).filter(Boolean).length}
                <span className="text-sm text-muted-foreground">/{students.length}</span>
              </div>
            </div>
            <div className="rounded-lg border p-4">
              <div className="text-sm font-medium text-muted-foreground">Absent Today</div>
              <div className="text-2xl font-bold">
                {students.length - Object.values(attendanceData[dateString] || {}).filter(Boolean).length}
                <span className="text-sm text-muted-foreground">/{students.length}</span>
              </div>
            </div>
            <div className="rounded-lg border p-4">
              <div className="text-sm font-medium text-muted-foreground">Attendance Rate</div>
              <div className="text-2xl font-bold">
                {Math.round(
                  (Object.values(attendanceData[dateString] || {}).filter(Boolean).length / students.length) * 100,
                )}
                %
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
