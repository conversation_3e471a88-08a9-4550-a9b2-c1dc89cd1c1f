"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  Settings,
  Mail,
  Bell,
  Globe,
  FileText,
  Database,
  HardDrive,
  Upload,
  Save,
  AlertTriangle,
  Info,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { superAdminService } from "@/lib/backend-api"

// TypeScript interfaces
interface GeneralSettings {
  systemName: string
  supportEmail: string
  contactPhone: string
  defaultLanguage: string
  defaultTimezone: string
  maintenanceMode: boolean
  enableRegistration: boolean
  requireApproval: boolean
  maxFileUploadSize: number
  defaultCurrency: string
}

interface EmailSettings {
  smtpServer: string
  smtpPort: number
  smtpUsername: string
  smtpPassword: string
  smtpSecure: boolean
  fromEmail: string
  fromName: string
  enableEmailNotifications: boolean
}

interface NotificationSettings {
  enablePushNotifications: boolean
  enableEmailNotifications: boolean
  enableSMSNotifications: boolean
  notifyOnNewRegistration: boolean
  notifyOnPayment: boolean
  notifyOnSystemError: boolean
  dailyReportEnabled: boolean
  weeklyReportEnabled: boolean
  monthlyReportEnabled: boolean
}

interface SecuritySettings {
  enforceSSL: boolean
  enableTwoFactor: boolean
  sessionTimeout: number
  passwordMinLength: number
  passwordRequireSpecialChar: boolean
  passwordRequireNumber: boolean
  passwordRequireUppercase: boolean
  maxLoginAttempts: number
  lockoutDuration: number
  enableAuditLog: boolean
}

interface SystemSettings {
  general: GeneralSettings
  email: EmailSettings
  notifications: NotificationSettings
  security: SecuritySettings
}

export default function SystemSettingsPage() {
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [isSaving, setIsSaving] = useState<boolean>(false)
  
  const [generalSettings, setGeneralSettings] = useState<GeneralSettings>({
    systemName: "EduManage",
    supportEmail: "<EMAIL>",
    contactPhone: "+260 97 1234567",
    defaultLanguage: "en",
    defaultTimezone: "Africa/Lusaka",
    maintenanceMode: false,
    enableRegistration: true,
    requireApproval: true,
    maxFileUploadSize: 10,
    defaultCurrency: "ZMW",
  })
  
  const [emailSettings, setEmailSettings] = useState<EmailSettings>({
    smtpServer: "smtp.example.com",
    smtpPort: 587,
    smtpUsername: "<EMAIL>",
    smtpPassword: "••••••••••••",
    smtpSecure: true,
    fromEmail: "<EMAIL>",
    fromName: "EduManage System",
    enableEmailNotifications: true,
  })
  
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    enablePushNotifications: true,
    enableEmailNotifications: true,
    enableSMSNotifications: false,
    notifyOnNewRegistration: true,
    notifyOnPayment: true,
    notifyOnSystemError: true,
    dailyReportEnabled: true,
    weeklyReportEnabled: true,
    monthlyReportEnabled: false,
  })
  
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    enforceSSL: true,
    enableTwoFactor: false,
    sessionTimeout: 30,
    passwordMinLength: 8,
    passwordRequireSpecialChar: true,
    passwordRequireNumber: true,
    passwordRequireUppercase: true,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    enableAuditLog: true,
  })
  
  const { toast } = useToast()

  const fetchSettings = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await superAdminService.getSystemSettings()
      
      if (response.success && response.data) {
        const { general, email, notifications, security } = response.data
        
        if (general) {
          setGeneralSettings(prev => ({ ...prev, ...general }))
        }
        if (email) {
          setEmailSettings(prev => ({ ...prev, ...email }))
        }
        if (notifications) {
          setNotificationSettings(prev => ({ ...prev, ...notifications }))
        }
        if (security) {
          setSecuritySettings(prev => ({ ...prev, ...security }))
        }
      } else {
        if (response.error) {
          setError(response.error)
          toast({
            title: "Warning",
            description: "Failed to fetch system settings. Using default values.",
            variant: "destructive",
          })
        }
      }
    } catch (err: any) {
      console.error('Error fetching settings:', err)
      setError(err?.message || 'Failed to fetch settings')
      
      toast({
        title: "Error",
        description: "Failed to fetch system settings. Using default values.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const saveSettings = async (settingsType: keyof SystemSettings, settings: any) => {
    try {
      setIsSaving(true)
      
      const settingsData = {
        [settingsType]: settings
      }
      
      const response = await superAdminService.updateSystemSettings(settingsData)
      
      if (response.success) {
        toast({
          title: "Success",
          description: `${settingsType} settings updated successfully`,
        })
      } else {
        toast({
          title: "Error",
          description: response.error || `Failed to update ${settingsType} settings`,
          variant: "destructive",
        })
      }
    } catch (err: any) {
      console.error(`Error saving ${settingsType} settings:`, err)
      toast({
        title: "Error",
        description: err?.message || `Failed to update ${settingsType} settings`,
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  useEffect(() => {
    fetchSettings()
  }, [])

  useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true)
      try {
        // Fetch all settings
        await Promise.all([
          fetchSettings('app'),
          fetchSettings('email'),
          fetchSettings() // general settings
        ])
      } finally {
        setIsLoading(false)
      }
    }

    loadSettings()
  }, [])

  const handleGeneralSettingChange = (setting: string, value: any) => {
    setGeneralSettings({
      ...generalSettings,
      [setting]: value,
    })

    toast({
      title: "Setting updated",
      description: "General setting has been updated successfully.",
    })
  }

  const handleEmailSettingChange = (setting: string, value: any) => {
    setEmailSettings({
      ...emailSettings,
      [setting]: value,
    })

    toast({
      title: "Setting updated",
      description: "Email setting has been updated successfully.",
    })
  }

  const handleNotificationSettingChange = (setting: string, value: any) => {
    setNotificationSettings({
      ...notificationSettings,
      [setting]: value,
    })

    toast({
      title: "Setting updated",
      description: "Notification setting has been updated successfully.",
    })
  }

  const handleSaveSettings = async (type: string = 'app') => {
    try {
      let settingsData
      switch (type) {
        case 'app':
          settingsData = generalSettings
          break
        case 'email':
          settingsData = emailSettings
          break
        case 'notifications':
          settingsData = notificationSettings
          break
        default:
          settingsData = { category: type, settings: generalSettings }
      }

      const response = await fetch(`/api/super-admin/settings?type=${type}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settingsData),
      })

      if (!response.ok) {
        throw new Error('Failed to save settings')
      }

      const data = await response.json()
      
      if (data.success) {
        toast({
          title: "Settings saved",
          description: `${type.charAt(0).toUpperCase() + type.slice(1)} settings have been saved successfully.`,
        })
      } else {
        throw new Error(data.message || 'Failed to save settings')
      }
    } catch (error) {
      console.error('Error saving settings:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to save settings',
        variant: "destructive",
      })
    }
  }

  const handleTestEmail = async () => {
    try {
      const response = await fetch('/api/super-admin/settings?action=test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          testEmail: emailSettings.senderEmail || '<EMAIL>' 
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to send test email')
      }

      const data = await response.json()
      
      if (data.success) {
        toast({
          title: "Test email sent",
          description: "A test email has been sent successfully.",
        })
      } else {
        throw new Error(data.message || 'Failed to send test email')
      }
    } catch (error) {
      console.error('Error sending test email:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to send test email',
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>Loading system settings...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-2">Error: {error}</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">System Settings</h1>
        <p className="text-muted-foreground">Configure global system settings and preferences</p>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="storage">Storage</TabsTrigger>
          <TabsTrigger value="localization">Localization</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="mr-2 h-5 w-5" />
                General Settings
              </CardTitle>
              <CardDescription>Configure basic system settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="system-name">System Name</Label>
                  <Input
                    id="system-name"
                    value={generalSettings.systemName}
                    onChange={(e) => handleGeneralSettingChange("systemName", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="support-email">Support Email</Label>
                  <Input
                    id="support-email"
                    type="email"
                    value={generalSettings.supportEmail}
                    onChange={(e) => handleGeneralSettingChange("supportEmail", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contact-phone">Contact Phone</Label>
                  <Input
                    id="contact-phone"
                    value={generalSettings.contactPhone}
                    onChange={(e) => handleGeneralSettingChange("contactPhone", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="default-currency">Default Currency</Label>
                  <Select
                    value={generalSettings.defaultCurrency}
                    onValueChange={(value) => handleGeneralSettingChange("defaultCurrency", value)}
                  >
                    <SelectTrigger id="default-currency">
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ZMW">Zambian Kwacha (ZMW)</SelectItem>
                      <SelectItem value="USD">US Dollar (USD)</SelectItem>
                      <SelectItem value="EUR">Euro (EUR)</SelectItem>
                      <SelectItem value="GBP">British Pound (GBP)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">System Access</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="maintenance-mode">Maintenance Mode</Label>
                    <p className="text-sm text-muted-foreground">
                      Put the system in maintenance mode (only admins can access)
                    </p>
                  </div>
                  <Switch
                    id="maintenance-mode"
                    checked={generalSettings.maintenanceMode}
                    onCheckedChange={(checked) => handleGeneralSettingChange("maintenanceMode", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="enable-registration">Enable Registration</Label>
                    <p className="text-sm text-muted-foreground">Allow new institutions to register</p>
                  </div>
                  <Switch
                    id="enable-registration"
                    checked={generalSettings.enableRegistration}
                    onCheckedChange={(checked) => handleGeneralSettingChange("enableRegistration", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="require-approval">Require Approval</Label>
                    <p className="text-sm text-muted-foreground">
                      Require admin approval for new institution registrations
                    </p>
                  </div>
                  <Switch
                    id="require-approval"
                    checked={generalSettings.requireApproval}
                    onCheckedChange={(checked) => handleGeneralSettingChange("requireApproval", checked)}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="max-file-upload">Maximum File Upload Size (MB)</Label>
                <div className="flex items-center gap-4">
                  <Slider
                    id="max-file-upload"
                    min={1}
                    max={50}
                    step={1}
                    value={[generalSettings.maxFileUploadSize]}
                    onValueChange={([value]) => handleGeneralSettingChange("maxFileUploadSize", value)}
                    className="flex-1"
                  />
                  <span className="w-12 text-center">{generalSettings.maxFileUploadSize} MB</span>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={() => handleSaveSettings('app')}>Save General Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="email" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="mr-2 h-5 w-5" />
                Email Configuration
              </CardTitle>
              <CardDescription>Configure email server settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Information</AlertTitle>
                <AlertDescription>
                  These settings are used for sending system emails such as notifications, password resets, and
                  invitations.
                </AlertDescription>
              </Alert>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="smtp-server">SMTP Server</Label>
                  <Input
                    id="smtp-server"
                    value={emailSettings.smtpServer}
                    onChange={(e) => handleEmailSettingChange("smtpServer", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="smtp-port">SMTP Port</Label>
                  <Input
                    id="smtp-port"
                    type="number"
                    value={emailSettings.smtpPort}
                    onChange={(e) => handleEmailSettingChange("smtpPort", Number.parseInt(e.target.value))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="smtp-username">SMTP Username</Label>
                  <Input
                    id="smtp-username"
                    value={emailSettings.smtpUsername}
                    onChange={(e) => handleEmailSettingChange("smtpUsername", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="smtp-password">SMTP Password</Label>
                  <Input
                    id="smtp-password"
                    type="password"
                    value={emailSettings.smtpPassword}
                    onChange={(e) => handleEmailSettingChange("smtpPassword", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sender-name">Sender Name</Label>
                  <Input
                    id="sender-name"
                    value={emailSettings.senderName}
                    onChange={(e) => handleEmailSettingChange("senderName", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sender-email">Sender Email</Label>
                  <Input
                    id="sender-email"
                    type="email"
                    value={emailSettings.senderEmail}
                    onChange={(e) => handleEmailSettingChange("senderEmail", e.target.value)}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="enable-ssl"
                  checked={emailSettings.enableSsl}
                  onCheckedChange={(checked) => handleEmailSettingChange("enableSsl", checked)}
                />
                <Label htmlFor="enable-ssl">Enable SSL/TLS</Label>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="email-template">Email Template</Label>
                <Select
                  value={emailSettings.emailTemplate}
                  onValueChange={(value) => handleEmailSettingChange("emailTemplate", value)}
                >
                  <SelectTrigger id="email-template">
                    <SelectValue placeholder="Select template" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default Template</SelectItem>
                    <SelectItem value="minimal">Minimal Template</SelectItem>
                    <SelectItem value="branded">Branded Template</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-2">
                <Button onClick={handleTestEmail} variant="outline">
                  Send Test Email
                </Button>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={() => handleSaveSettings('email')}>Save Email Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Bell className="mr-2 h-5 w-5" />
                Notification Settings
              </CardTitle>
              <CardDescription>Configure system-wide notification preferences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Notification Channels</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-notifications">Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">Send notifications via email</p>
                  </div>
                  <Switch
                    id="email-notifications"
                    checked={notificationSettings.enableEmailNotifications}
                    onCheckedChange={(checked) => handleNotificationSettingChange("enableEmailNotifications", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="in-app-notifications">In-App Notifications</Label>
                    <p className="text-sm text-muted-foreground">Show notifications within the application</p>
                  </div>
                  <Switch
                    id="in-app-notifications"
                    checked={notificationSettings.enableInAppNotifications}
                    onCheckedChange={(checked) => handleNotificationSettingChange("enableInAppNotifications", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sms-notifications">SMS Notifications</Label>
                    <p className="text-sm text-muted-foreground">Send notifications via SMS (additional charges)</p>
                  </div>
                  <Switch
                    id="sms-notifications"
                    checked={notificationSettings.enableSmsNotifications}
                    onCheckedChange={(checked) => handleNotificationSettingChange("enableSmsNotifications", checked)}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Notification Types</h3>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="admin-alerts"
                      checked={notificationSettings.adminAlerts}
                      onCheckedChange={(checked) => handleNotificationSettingChange("adminAlerts", checked)}
                    />
                    <Label htmlFor="admin-alerts">Admin Alerts</Label>
                  </div>
                  <p className="text-sm text-muted-foreground pl-7">Important system alerts for administrators</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="user-registration"
                      checked={notificationSettings.userRegistrationNotifications}
                      onCheckedChange={(checked) =>
                        handleNotificationSettingChange("userRegistrationNotifications", checked)
                      }
                    />
                    <Label htmlFor="user-registration">User Registration</Label>
                  </div>
                  <p className="text-sm text-muted-foreground pl-7">Notifications about new user registrations</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="payment-notifications"
                      checked={notificationSettings.paymentNotifications}
                      onCheckedChange={(checked) => handleNotificationSettingChange("paymentNotifications", checked)}
                    />
                    <Label htmlFor="payment-notifications">Payment Notifications</Label>
                  </div>
                  <p className="text-sm text-muted-foreground pl-7">
                    Notifications about payments and subscription changes
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="system-updates"
                      checked={notificationSettings.systemUpdateNotifications}
                      onCheckedChange={(checked) =>
                        handleNotificationSettingChange("systemUpdateNotifications", checked)
                      }
                    />
                    <Label htmlFor="system-updates">System Updates</Label>
                  </div>
                  <p className="text-sm text-muted-foreground pl-7">
                    Notifications about system updates and maintenance
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="security-alerts"
                      checked={notificationSettings.securityAlertNotifications}
                      onCheckedChange={(checked) =>
                        handleNotificationSettingChange("securityAlertNotifications", checked)
                      }
                    />
                    <Label htmlFor="security-alerts">Security Alerts</Label>
                  </div>
                  <p className="text-sm text-muted-foreground pl-7">Notifications about security-related events</p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings}>Save Notification Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="storage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <HardDrive className="mr-2 h-5 w-5" />
                Storage Settings
              </CardTitle>
              <CardDescription>Configure system storage and file management</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Storage Provider</h3>

                <RadioGroup defaultValue="local" className="space-y-3">
                  <div className="flex items-start space-x-3 space-y-0">
                    <RadioGroupItem value="local" id="local" />
                    <div className="space-y-1">
                      <Label htmlFor="local" className="font-medium">
                        Local Storage
                      </Label>
                      <p className="text-sm text-muted-foreground">Store files on the local server</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 space-y-0">
                    <RadioGroupItem value="s3" id="s3" />
                    <div className="space-y-1">
                      <Label htmlFor="s3" className="font-medium">
                        Amazon S3
                      </Label>
                      <p className="text-sm text-muted-foreground">Store files on Amazon S3 cloud storage</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 space-y-0">
                    <RadioGroupItem value="gcs" id="gcs" />
                    <div className="space-y-1">
                      <Label htmlFor="gcs" className="font-medium">
                        Google Cloud Storage
                      </Label>
                      <p className="text-sm text-muted-foreground">Store files on Google Cloud Storage</p>
                    </div>
                  </div>
                </RadioGroup>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">File Management</h3>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="allowed-file-types">Allowed File Types</Label>
                    <Input id="allowed-file-types" defaultValue=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xls,.xlsx" />
                    <p className="text-sm text-muted-foreground">Comma-separated list of allowed file extensions</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="max-file-retention">File Retention Period (days)</Label>
                    <Input id="max-file-retention" type="number" defaultValue={365} />
                    <p className="text-sm text-muted-foreground">
                      Number of days to keep temporary files (0 for indefinite)
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="auto-delete" defaultChecked />
                  <Label htmlFor="auto-delete">Auto-delete temporary files</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="compress-images" defaultChecked />
                  <Label htmlFor="compress-images">Automatically compress uploaded images</Label>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Storage Usage</h3>
                  <Badge variant="outline">75.2 GB / 100 GB</Badge>
                </div>

                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>Used Space</span>
                    <span>75.2 GB (75.2%)</span>
                  </div>
                  <div className="h-2 w-full bg-secondary rounded-full overflow-hidden">
                    <div className="h-full bg-primary rounded-full" style={{ width: "75.2%" }} />
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Documents</p>
                    <p className="text-sm text-muted-foreground">32.5 GB</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Images</p>
                    <p className="text-sm text-muted-foreground">28.7 GB</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Other Files</p>
                    <p className="text-sm text-muted-foreground">14.0 GB</p>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline">
                    <Upload className="mr-2 h-4 w-4" />
                    Increase Storage
                  </Button>
                  <Button variant="outline">
                    <Database className="mr-2 h-4 w-4" />
                    Manage Storage
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings}>Save Storage Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="localization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Globe className="mr-2 h-5 w-5" />
                Localization Settings
              </CardTitle>
              <CardDescription>Configure language, timezone, and regional settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="default-language">Default Language</Label>
                  <Select
                    value={generalSettings.defaultLanguage}
                    onValueChange={(value) => handleGeneralSettingChange("defaultLanguage", value)}
                  >
                    <SelectTrigger id="default-language">
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                      <SelectItem value="es">Spanish</SelectItem>
                      <SelectItem value="pt">Portuguese</SelectItem>
                      <SelectItem value="sw">Swahili</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="default-timezone">Default Timezone</Label>
                  <Select
                    value={generalSettings.defaultTimezone}
                    onValueChange={(value) => handleGeneralSettingChange("defaultTimezone", value)}
                  >
                    <SelectTrigger id="default-timezone">
                      <SelectValue placeholder="Select timezone" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Africa/Lusaka">Africa/Lusaka (GMT+2)</SelectItem>
                      <SelectItem value="Africa/Johannesburg">Africa/Johannesburg (GMT+2)</SelectItem>
                      <SelectItem value="Africa/Nairobi">Africa/Nairobi (GMT+3)</SelectItem>
                      <SelectItem value="Africa/Lagos">Africa/Lagos (GMT+1)</SelectItem>
                      <SelectItem value="Europe/London">Europe/London (GMT+0/+1)</SelectItem>
                      <SelectItem value="America/New_York">America/New_York (GMT-5/-4)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="date-format">Date Format</Label>
                  <Select defaultValue="dd/mm/yyyy">
                    <SelectTrigger id="date-format">
                      <SelectValue placeholder="Select date format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="dd/mm/yyyy">DD/MM/YYYY</SelectItem>
                      <SelectItem value="mm/dd/yyyy">MM/DD/YYYY</SelectItem>
                      <SelectItem value="yyyy-mm-dd">YYYY-MM-DD</SelectItem>
                      <SelectItem value="dd-mmm-yyyy">DD-MMM-YYYY</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="time-format">Time Format</Label>
                  <Select defaultValue="24h">
                    <SelectTrigger id="time-format">
                      <SelectValue placeholder="Select time format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="12h">12-hour (AM/PM)</SelectItem>
                      <SelectItem value="24h">24-hour</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Regional Settings</h3>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="first-day-week">First Day of Week</Label>
                    <Select defaultValue="monday">
                      <SelectTrigger id="first-day-week">
                        <SelectValue placeholder="Select first day" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="sunday">Sunday</SelectItem>
                        <SelectItem value="monday">Monday</SelectItem>
                        <SelectItem value="saturday">Saturday</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="number-format">Number Format</Label>
                    <Select defaultValue="comma">
                      <SelectTrigger id="number-format">
                        <SelectValue placeholder="Select number format" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="comma">1,234.56</SelectItem>
                        <SelectItem value="dot">1.234,56</SelectItem>
                        <SelectItem value="space">1 234,56</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Translation Management</h3>

                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Translation in progress</AlertTitle>
                  <AlertDescription>
                    Some translations may be incomplete. You can help improve translations by contributing.
                  </AlertDescription>
                </Alert>

                <div className="space-y-2">
                  <Label htmlFor="custom-translations">Custom Translations</Label>
                  <Textarea
                    id="custom-translations"
                    placeholder="Enter custom translations in JSON format"
                    className="min-h-[100px]"
                  />
                  <p className="text-sm text-muted-foreground">Override specific translations using JSON format</p>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline">
                    <FileText className="mr-2 h-4 w-4" />
                    Export Translations
                  </Button>
                  <Button variant="outline">
                    <Upload className="mr-2 h-4 w-4" />
                    Import Translations
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings}>
                <Save className="mr-2 h-4 w-4" />
                Save Localization Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
