"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/contexts/auth-context"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Refresh<PERSON>w, Al<PERSON><PERSON>riangle } from "lucide-react"

interface AuthRecoveryProps {
  children: React.ReactNode
  showRetryButton?: boolean
  autoRetry?: boolean
  maxRetries?: number
}

export function AuthRecovery({ 
  children, 
  showRetryButton = true, 
  autoRetry = true,
  maxRetries = 3 
}: AuthRecoveryProps) {
  const { user, isLoading, authError, clearError, retryAuth } = useAuth()
  const [retryCount, setRetryCount] = useState(0)
  const [isRetrying, setIsRetrying] = useState(false)

  // Auto-retry logic
  useEffect(() => {
    if (authError && autoRetry && retryCount < maxRetries) {
      const timer = setTimeout(async () => {
        console.log(`🔄 AuthRecovery: Auto-retry attempt ${retryCount + 1}/${maxRetries}`)
        setIsRetrying(true)
        try {
          await retryAuth()
          setRetryCount(0) // Reset on success
        } catch (error) {
          console.error("Auto-retry failed:", error)
          setRetryCount(prev => prev + 1)
        } finally {
          setIsRetrying(false)
        }
      }, Math.pow(2, retryCount) * 1000) // Exponential backoff

      return () => clearTimeout(timer)
    }
  }, [authError, autoRetry, retryCount, maxRetries, retryAuth])

  const handleManualRetry = async () => {
    setIsRetrying(true)
    clearError()
    try {
      await retryAuth()
      setRetryCount(0)
    } catch (error) {
      console.error("Manual retry failed:", error)
    } finally {
      setIsRetrying(false)
    }
  }

  const handleRefreshToken = async () => {
    setIsRetrying(true)
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include'
      })
      
      if (response.ok) {
        await retryAuth()
        setRetryCount(0)
      } else {
        throw new Error('Token refresh failed')
      }
    } catch (error) {
      console.error("Token refresh failed:", error)
    } finally {
      setIsRetrying(false)
    }
  }

  // Show loading state
  if (isLoading || isRetrying) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">
            {isRetrying ? "Retrying authentication..." : "Checking authentication..."}
          </p>
        </div>
      </div>
    )
  }

  // Show error state with recovery options
  if (authError && !user) {
    const isNetworkError = authError.includes("Network") || authError.includes("fetch")
    const isTokenError = authError.includes("token") || authError.includes("expired")
    
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="max-w-md w-full space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {authError}
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            {showRetryButton && (
              <Button 
                onClick={handleManualRetry} 
                disabled={isRetrying}
                className="w-full"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
                Retry Authentication
              </Button>
            )}

            {isTokenError && (
              <Button 
                onClick={handleRefreshToken} 
                disabled={isRetrying}
                variant="outline"
                className="w-full"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
                Refresh Token
              </Button>
            )}

            <Button 
              onClick={() => window.location.href = '/auth/login'} 
              variant="secondary"
              className="w-full"
            >
              Go to Login
            </Button>
          </div>

          {retryCount >= maxRetries && (
            <Alert>
              <AlertDescription>
                Maximum retry attempts reached. Please try refreshing the page or logging in again.
              </AlertDescription>
            </Alert>
          )}
        </div>
      </div>
    )
  }

  // Show warning for session issues but still render children
  if (authError && user) {
    return (
      <div>
        <Alert className="mb-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{authError}</span>
            <Button 
              onClick={clearError} 
              variant="ghost" 
              size="sm"
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
        {children}
      </div>
    )
  }

  // Normal authenticated state
  return <>{children}</>
}
