const express = require('express');
const router = express.Router();
const dashboardController = require('./dashboard.controller');
const { authenticate, restrictTo } = require('../../../middleware/authenticate');

/**
 * @route GET /api/v1/super-admin/dashboard
 * @desc Get super admin dashboard statistics
 * @access Private (Super Admin only)
 */
router.get('/', authenticate, restrictTo('SUPER_ADMIN'), dashboardController.getDashboardStats);

/**
 * @route GET /api/v1/super-admin/dashboard/pending-approvals
 * @desc Get pending institution approvals
 * @access Private (Super Admin only)
 */
router.get('/pending-approvals', authenticate, restrictTo('SUPER_ADMIN'), dashboardController.getPendingApprovals);

/**
 * @route POST /api/v1/super-admin/institutions/:id/approve
 * @desc Approve institution
 * @access Private (Super Admin only)
 */
router.post('/institutions/:id/approve', authenticate, restrictTo('SUPER_ADMIN'), dashboardController.approveInstitution);

/**
 * @route POST /api/v1/super-admin/institutions/:id/reject
 * @desc Reject institution
 * @access Private (Super Admin only)
 */
router.post('/institutions/:id/reject', authenticate, restrictTo('SUPER_ADMIN'), dashboardController.rejectInstitution);

module.exports = router;
