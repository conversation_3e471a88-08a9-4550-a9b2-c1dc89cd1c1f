"use client"

import Link from "next/link"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, X, Lightbulb, Users, BarChart3, MessageSquare, GraduationCap } from "lucide-react"
import { ThemeToggle } from "@/components/theme-toggle"
import { cn } from "@/lib/utils"

export function LandingHeader() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const [activeSection, setActiveSection] = useState("home")

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true)
      } else {
        setScrolled(false)
      }

      // Update active section based on scroll position
      const sections = ["features", "pricing", "testimonials", "contact"]
      for (const section of sections) {
        const element = document.getElementById(section)
        if (element) {
          const rect = element.getBoundingClientRect()
          if (rect.top <= 100 && rect.bottom >= 100) {
            setActiveSection(section)
            break
          } else if (window.scrollY < 300) {
            setActiveSection("home")
          }
        }
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (isMenuOpen && !target.closest(".mobile-menu-container")) {
        setIsMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isMenuOpen])

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }
    return () => {
      document.body.style.overflow = "auto"
    }
  }, [isMenuOpen])

  const navItems = [
    { name: "Features", href: "/features", icon: <Lightbulb className="h-4 w-4" /> },
    { name: "Pricing", href: "/pricing", icon: <BarChart3 className="h-4 w-4" /> },
    { name: "Testimonials", href: "/testimonials", icon: <Users className="h-4 w-4" /> },
    { name: "Contact", href: "/contact", icon: <MessageSquare className="h-4 w-4" /> },
  ]

  return (
    <header
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300 w-full backdrop-blur-md",
        scrolled ? "bg-background shadow-sm border-b border-border/40" : "bg-background",
      )}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div
          className={cn(
            "flex justify-between items-center transition-all duration-300",
            scrolled ? "h-16" : "h-20 md:h-24",
          )}
        >
          {/* Logo */}
          <div className="flex items-center">
            <Link
              href="/"
              className="flex items-center space-x-2 transition-all duration-300 hover:scale-105"
              onClick={() => setActiveSection("home")}
            >
              <GraduationCap
                className={cn("transition-all duration-300 text-primary", scrolled ? "h-6 w-6" : "h-8 w-8")}
              />
              <span
                className={cn(
                  "font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70 transition-all duration-300",
                  scrolled ? "text-lg md:text-xl" : "text-xl md:text-2xl",
                )}
              >
                EduManage
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
                  scrolled ? "py-1.5" : "py-2",
                  activeSection === item.href.substring(1)
                    ? "text-primary bg-primary/10"
                    : "text-foreground/70 hover:text-primary hover:bg-primary/5",
                )}
                onClick={() => {
                  setActiveSection(item.href.substring(1))
                }}
              >
                <span className="flex items-center gap-1.5">
                  {item.icon}
                  {item.name}
                </span>
              </Link>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <ThemeToggle />
            <Link href="/auth/login">
              <Button
                variant="outline"
                size="sm"
                className={cn("transition-all duration-300", scrolled ? "h-8" : "h-9")}
              >
                Log in
              </Button>
            </Link>
            <Link href="/auth/register-school">
              <Button
                size="sm"
                className={cn("bg-primary hover:bg-primary/90 transition-all duration-300", scrolled ? "h-8" : "h-9")}
              >
                Sign up
              </Button>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <div className="flex items-center md:hidden space-x-4">
            <ThemeToggle />
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-foreground/70 hover:text-primary hover:bg-primary/10 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary transition-colors"
              aria-expanded={isMenuOpen}
            >
              <span className="sr-only">{isMenuOpen ? "Close main menu" : "Open main menu"}</span>
              {isMenuOpen ? (
                <X className="block h-6 w-6" aria-hidden="true" />
              ) : (
                <Menu className="block h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={cn(
          "fixed inset-0 bg-background backdrop-blur-md z-40 md:hidden transition-all duration-300 mobile-menu-container",
          isMenuOpen ? "opacity-100 translate-x-0" : "opacity-0 translate-x-full pointer-events-none",
        )}
        style={{ top: scrolled ? "64px" : "80px" }}
      >
        <div className="flex flex-col h-full overflow-y-auto pb-20">
          <nav className="px-4 pt-8 pb-6 space-y-2">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "flex items-center px-4 py-3 text-base font-medium rounded-lg transition-all duration-200",
                  activeSection === item.href.substring(1)
                    ? "text-primary bg-primary/10"
                    : "text-foreground/70 hover:text-primary hover:bg-primary/5",
                )}
                onClick={() => {
                  setActiveSection(item.href.substring(1))
                  setIsMenuOpen(false)
                }}
              >
                <span className="flex items-center gap-3">
                  {item.icon}
                  {item.name}
                </span>
              </Link>
            ))}
          </nav>

          <div className="mt-auto px-4 py-6 border-t border-border/30">
            <div className="flex flex-col space-y-3">
              <Link href="/auth/login" className="w-full" onClick={() => setIsMenuOpen(false)}>
                <Button variant="outline" className="w-full justify-center">
                  Log in
                </Button>
              </Link>
              <Link href="/auth/register-school" className="w-full" onClick={() => setIsMenuOpen(false)}>
                <Button className="w-full justify-center bg-primary hover:bg-primary/90">Sign up</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
