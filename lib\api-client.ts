/**
 * Enhanced API client with automatic token refresh
 */

interface ApiResponse<T = any> {
  data?: T
  error?: string
  status: number
}

class ApiClient {
  private baseUrl: string
  private isRefreshing = false
  private failedQueue: Array<{
    resolve: (value: any) => void
    reject: (error: any) => void
  }> = []

  constructor(baseUrl = '') {
    this.baseUrl = baseUrl
  }

  private async processQueue(error: any, token: string | null = null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error)
      } else {
        resolve(token)
      }
    })
    
    this.failedQueue = []
  }

  private async refreshToken(): Promise<string | null> {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Token refresh failed')
      }

      const data = await response.json()
      return data.accessToken || null
    } catch (error) {
      console.error('Token refresh error:', error)
      return null
    }
  }

  async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`
    
    const config: RequestInit = {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    try {
      let response = await fetch(url, config)

      // If we get a 401, try to refresh the token
      if (response.status === 401 && !this.isRefreshing) {
        if (this.isRefreshing) {
          // If already refreshing, wait for it to complete
          return new Promise((resolve, reject) => {
            this.failedQueue.push({ resolve, reject })
          }).then(() => {
            // Retry the original request
            return fetch(url, config).then(res => ({
              data: res.ok ? res.json() : undefined,
              status: res.status,
              error: res.ok ? undefined : 'Request failed'
            }))
          })
        }

        this.isRefreshing = true

        try {
          const newToken = await this.refreshToken()
          
          if (newToken) {
            this.processQueue(null, newToken)
            
            // Retry the original request
            response = await fetch(url, config)
          } else {
            this.processQueue(new Error('Token refresh failed'), null)
            // Redirect to login
            window.location.href = '/auth/login'
            return { status: 401, error: 'Authentication failed' }
          }
        } catch (refreshError) {
          this.processQueue(refreshError, null)
          window.location.href = '/auth/login'
          return { status: 401, error: 'Authentication failed' }
        } finally {
          this.isRefreshing = false
        }
      }

      const data = response.ok ? await response.json() : undefined
      
      return {
        data,
        status: response.status,
        error: response.ok ? undefined : data?.message || 'Request failed'
      }
    } catch (error) {
      console.error('API request error:', error)
      return {
        status: 500,
        error: 'Network error'
      }
    }
  }

  // Convenience methods
  async get<T = any>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const url = new URL(endpoint, this.baseUrl)
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value))
        }
      })
    }
    
    return this.request<T>(url.pathname + url.search)
  }

  async post<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    })
  }
}

// Create a singleton instance
export const apiClient = new ApiClient()

// Export the class for custom instances
export { ApiClient }
