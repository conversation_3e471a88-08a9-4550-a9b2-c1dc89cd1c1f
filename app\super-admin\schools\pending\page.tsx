"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { AlertCircle, CheckCircle, XCircle, Eye, Mail, Phone, Building, Calendar, Users } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/contexts/auth-context"

interface PendingSchool {
  id: string
  name: string
  type: string
  address: string
  city: string
  country: string
  phone: string
  email: string
  website?: string
  logo_url?: string
  subscription_plan: string
  registration_date: string
  admin: {
    id: string
    name: string
    email: string
    phone?: string
    is_email_verified: boolean
  }
}

export default function PendingSchoolsPage() {
  const { user } = useAuth()
  const [schools, setSchools] = useState<PendingSchool[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")
  const [selectedSchool, setSelectedSchool] = useState<PendingSchool | null>(null)
  const [showApproveDialog, setShowApproveDialog] = useState(false)
  const [showRejectDialog, setShowRejectDialog] = useState(false)
  const [rejectionReason, setRejectionReason] = useState("")
  const [actionLoading, setActionLoading] = useState(false)

  useEffect(() => {
    fetchPendingSchools()
  }, [])

  const fetchPendingSchools = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/super-admin/schools/pending")
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch pending schools")
      }

      setSchools(data.institutions || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleApprove = async () => {
    if (!selectedSchool) return

    try {
      setActionLoading(true)
      const response = await fetch(`/api/super-admin/schools/${selectedSchool.id}/approve`, {
        method: "POST",
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to approve school")
      }

      // Remove from pending list
      setSchools((prev) => prev.filter((school) => school.id !== selectedSchool.id))
      setShowApproveDialog(false)
      setSelectedSchool(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to approve school")
    } finally {
      setActionLoading(false)
    }
  }

  const handleReject = async () => {
    if (!selectedSchool || !rejectionReason.trim()) return

    try {
      setActionLoading(true)
      const response = await fetch(`/api/super-admin/schools/${selectedSchool.id}/reject`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ reason: rejectionReason }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to reject school")
      }

      // Remove from pending list
      setSchools((prev) => prev.filter((school) => school.id !== selectedSchool.id))
      setShowRejectDialog(false)
      setSelectedSchool(null)
      setRejectionReason("")
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to reject school")
    } finally {
      setActionLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getSubscriptionPlanBadge = (plan: string) => {
    const colors = {
      basic: "bg-blue-100 text-blue-800",
      standard: "bg-green-100 text-green-800",
      premium: "bg-purple-100 text-purple-800",
      enterprise: "bg-orange-100 text-orange-800",
    }
    return colors[plan as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Pending School Registrations</h1>
        </div>
        <div className="grid gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Pending School Registrations</h1>
          <p className="text-gray-600">Review and approve new school applications</p>
        </div>
        <Badge variant="secondary" className="text-lg px-3 py-1">
          {schools.length} Pending
        </Badge>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {schools.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Pending Applications</h3>
            <p className="text-gray-600">All school registration applications have been reviewed.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {schools.map((school) => (
            <Card key={school.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    {school.logo_url ? (
                      <img
                        src={school.logo_url || "/placeholder.svg"}
                        alt={`${school.name} logo`}
                        className="w-12 h-12 rounded-lg object-contain border"
                      />
                    ) : (
                      <div className="w-12 h-12 rounded-lg bg-emerald-100 flex items-center justify-center">
                        <Building className="h-6 w-6 text-emerald-600" />
                      </div>
                    )}
                    <div>
                      <CardTitle className="text-xl">{school.name}</CardTitle>
                      <CardDescription className="flex items-center gap-4">
                        <span className="capitalize">{school.type.replace("_", " ")}</span>
                        <span>•</span>
                        <span>
                          {school.city}, {school.country}
                        </span>
                      </CardDescription>
                    </div>
                  </div>
                  <Badge className={getSubscriptionPlanBadge(school.subscription_plan)}>
                    {school.subscription_plan.charAt(0).toUpperCase() + school.subscription_plan.slice(1)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4 mb-4">
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm text-gray-700">Institution Details</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-400" />
                        <span>{school.email}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <span>{school.phone}</span>
                      </div>
                      {school.website && (
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4 text-gray-400" />
                          <a
                            href={school.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            {school.website}
                          </a>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm text-gray-700">Administrator</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span>{school.admin.name}</span>
                        {!school.admin.is_email_verified && (
                          <Badge variant="destructive" className="text-xs">
                            Unverified
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-400" />
                        <span>{school.admin.email}</span>
                      </div>
                      {school.admin.phone && (
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-gray-400" />
                          <span>{school.admin.phone}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Calendar className="h-4 w-4" />
                    <span>Applied {formatDate(school.registration_date)}</span>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => setSelectedSchool(school)}>
                      <Eye className="h-4 w-4 mr-1" />
                      View Details
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        setSelectedSchool(school)
                        setShowRejectDialog(true)
                      }}
                    >
                      <XCircle className="h-4 w-4 mr-1" />
                      Reject
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => {
                        setSelectedSchool(school)
                        setShowApproveDialog(true)
                      }}
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Approve
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Approve Dialog */}
      <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve School Registration</DialogTitle>
            <DialogDescription>
              Are you sure you want to approve the registration for <strong>{selectedSchool?.name}</strong>? This will
              activate their account and start their free trial.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-medium text-green-900 mb-2">What happens when approved:</h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Institution status changes to "Active"</li>
                <li>• Administrator account is activated</li>
                <li>• 30-day free trial begins</li>
                <li>• Welcome email is sent to administrator</li>
                <li>• School can start using the system immediately</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowApproveDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleApprove} disabled={actionLoading}>
              {actionLoading ? "Approving..." : "Approve School"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject School Registration</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting the registration for <strong>{selectedSchool?.name}</strong>. This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <label htmlFor="rejection-reason" className="block text-sm font-medium mb-2">
              Rejection Reason *
            </label>
            <Textarea
              id="rejection-reason"
              placeholder="Please explain why this application is being rejected..."
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              rows={4}
              required
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleReject} disabled={actionLoading || !rejectionReason.trim()}>
              {actionLoading ? "Rejecting..." : "Reject Application"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
