const express = require('express');
const router = express.Router();
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const {
  getAnalyticsStats,
  getInstitutionAnalytics,
  getUserAnalytics,
  getRevenueAnalytics,
  exportAnalytics
} = require('./analytics.controller');

// Protect all routes and restrict to SUPER_ADMIN
router.use(authenticate);
router.use(restrictTo('SUPER_ADMIN'));

// Analytics routes
router.get('/stats', getAnalyticsStats);
router.get('/institutions', getInstitutionAnalytics);
router.get('/users', getUserAnalytics);
router.get('/revenue', getRevenueAnalytics);
router.get('/export', exportAnalytics);

module.exports = router;
