const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

/**
 * Clear all data from the database
 */
async function clearDatabase() {
  try {
    console.log('Clearing existing database data...');
    
    // Define all tables to clear in order of dependencies
    const tables = [
      'auditLog',
      'fileUpload',
      'refreshToken',
      'schoolUser',
      'school',
      'institutionUser',
      'institution',
      'user'
    ];
    
    // Try to clear each table, skipping any that don't exist yet
    for (const table of tables) {
      try {
        console.log(`Clearing ${table} table...`);
        await prisma[table].deleteMany({});
      } catch (tableError) {
        // If table doesn't exist, just log and continue
        if (tableError.code === 'P2021') {
          console.log(`Table ${table} does not exist yet, skipping.`);
        } else {
          // For other errors, log and continue with next table
          console.error(`Error clearing ${table}:`, tableError.message);
        }
      }
    }
    
    console.log('Database cleared successfully.');
  } catch (error) {
    console.error('Error clearing database:', error);
  }
}

/**
 * Initialize database with seed data
 */
async function initializeDatabase() {
  try {
    console.log('Starting database initialization...');
    
    // Clear existing data first
    await clearDatabase();

    // Create a super admin user if none exists
    const superAdminExists = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' },
    });

    if (!superAdminExists) {
      console.log('Creating super admin user...');
      
      // Hash password
      const password = 'Admin@123'; // Default password
      const passwordHash = await bcrypt.hash(password, 12);

      // Create super admin
      const superAdmin = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash,
          firstName: 'Super',
          lastName: 'Admin',
          role: 'SUPER_ADMIN',
          isEmailVerified: true,
          isActive: true,
          mfaEnabled: false,
          phoneNumber: '+260987654321',
        },
      });

      console.log(`Super admin created with ID: ${superAdmin.id}`);
    } else {
      console.log('Super admin already exists, skipping creation.');
    }

    // Create a demo institution if none exists
    const demoInstitutionExists = await prisma.institution.findFirst({
      where: { name: 'Demo Institution' },
    });

    if (!demoInstitutionExists) {
      console.log('Creating demo institution...');
      
      // Create demo institution
      const demoInstitution = await prisma.institution.create({
        data: {
          name: 'Demo Institution',
          domain: 'demo.edusync.com',
          primaryColor: '#4F46E5',
          secondaryColor: '#10B981',
          isActive: true,
          subscriptionStatus: 'TRIAL',
          subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          address: '123 Main Street',
          city: 'Demo City',
          state: 'Demo State',
          country: 'Demo Country',
          postalCode: '12345',
          phoneNumber: '+260123456789',
          email: '<EMAIL>',
          website: 'https://demo.edusync.com',
          verificationStatus: true,
          studentCount: 500,
          teacherCount: 50,
          referralSource: 'Direct',
          specialRequirements: 'None'
        },
      });

      console.log(`Demo institution created with ID: ${demoInstitution.id}`);

      // Create an institution admin
      const institutionAdminExists = await prisma.user.findFirst({
        where: { email: '<EMAIL>' },
      });

      if (!institutionAdminExists) {
        console.log('Creating institution admin user...');
        
        // Hash password
        const password = 'Admin@123'; // Default password
        const passwordHash = await bcrypt.hash(password, 12);

        // Create institution admin
        const institutionAdmin = await prisma.user.create({
          data: {
            email: '<EMAIL>',
            passwordHash,
            firstName: 'Institution',
            lastName: 'Admin',
            role: 'INSTITUTION_ADMIN',
            isEmailVerified: true,
            isActive: true,
          },
        });

        // Associate with institution
        await prisma.institutionUser.create({
          data: {
            userId: institutionAdmin.id,
            institutionId: demoInstitution.id,
            role: 'INSTITUTION_ADMIN',
          },
        });

        console.log(`Institution admin created with ID: ${institutionAdmin.id}`);
      }

      // Create a demo school
      console.log('Creating demo school...');
      
      const demoSchool = await prisma.school.create({
        data: {
          name: 'Demo School',
          institutionId: demoInstitution.id,
          address: '123 Main St',
          city: 'Demo City',
          state: 'Demo State',
          country: 'Demo Country',
          postalCode: '12345',
          phoneNumber: '+1234567890',
          email: '<EMAIL>',
          website: 'https://school.demo.edusync.com',
          isActive: true,
        },
      });

      console.log(`Demo school created with ID: ${demoSchool.id}`);

      // Create a school admin
      const schoolAdminExists = await prisma.user.findFirst({
        where: { email: '<EMAIL>' },
      });

      if (!schoolAdminExists) {
        console.log('Creating school admin user...');
        
        // Hash password
        const password = 'Admin@123'; // Default password
        const passwordHash = await bcrypt.hash(password, 12);

        // Create school admin
        const schoolAdmin = await prisma.user.create({
          data: {
            email: '<EMAIL>',
            passwordHash,
            firstName: 'School',
            lastName: 'Admin',
            role: 'SCHOOL_ADMIN',
            isEmailVerified: true,
            isActive: true,
          },
        });

        // Associate with institution
        await prisma.institutionUser.create({
          data: {
            userId: schoolAdmin.id,
            institutionId: demoInstitution.id,
            role: 'SCHOOL_ADMIN',
          },
        });

        // Associate with school
        await prisma.schoolUser.create({
          data: {
            userId: schoolAdmin.id,
            schoolId: demoSchool.id,
            role: 'SCHOOL_ADMIN',
          },
        });

        console.log(`School admin created with ID: ${schoolAdmin.id}`);
      }
    } else {
      console.log('Demo institution already exists, skipping creation.');
    }

    console.log('Database initialization completed successfully!');
  } catch (error) {
    console.error('Error initializing database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the initialization
initializeDatabase();
