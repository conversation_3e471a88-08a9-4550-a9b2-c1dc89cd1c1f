const express = require('express');
const router = express.Router();
const { authenticate, restrictTo } = require('../../../middleware/authenticate');
const {
  getSystemSettings,
  updateSystemSettings,
  getAppConfig,
  updateAppConfig,
  getEmailSettings,
  updateEmailSettings,
  testEmailConfig,
  getBackupSettings,
  updateBackupSettings,
  triggerManualBackup,
  getSystemInfo
} = require('./settings.controller');

// Protect all routes and restrict to SUPER_ADMIN
router.use(authenticate);
router.use(restrictTo('SUPER_ADMIN'));

// General system settings
router.get('/', getSystemSettings);
router.put('/', updateSystemSettings);

// Application configuration
router.get('/app', getAppConfig);
router.put('/app', updateAppConfig);

// Email settings
router.get('/email', getEmailSettings);
router.put('/email', updateEmailSettings);
router.post('/email/test', testEmailConfig);

// Backup settings
router.get('/backup', getBackupSettings);
router.put('/backup', updateBackupSettings);
router.post('/backup/trigger', triggerManualBackup);

// System information
router.get('/system-info', getSystemInfo);

module.exports = router;
