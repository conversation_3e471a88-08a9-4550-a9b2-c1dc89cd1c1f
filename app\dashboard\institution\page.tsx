"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Building,
  MapPin,
  Phone,
  Mail,
  Globe,
  Users,
  BookOpen,
  Calendar,
  DollarSign,
  FileText,
  Settings,
  Loader2,
} from "lucide-react"
import { useTenant } from "@/contexts/tenant-context"
import { useAuth } from "@/contexts/auth-context"
import { useToast } from "@/hooks/use-toast"
import Link from "next/link"
import { useRouter } from "next/navigation"

interface Institution {
  id: string
  name: string
  domain?: string
  logo?: string
  primaryColor?: string
  secondaryColor?: string
  isActive: boolean
  subscriptionStatus: string
  subscriptionEndDate?: string
  address?: string
  city?: string
  state?: string
  country?: string
  postalCode?: string
  phoneNumber?: string
  email?: string
  website?: string
  verificationStatus: boolean
  studentCount?: number
  teacherCount?: number
  referralSource?: string
  specialRequirements?: string
  createdAt: string
  updatedAt: string
}

interface School {
  id: string
  name: string
  type: string
  address?: string
  city?: string
  state?: string
  country?: string
  postalCode?: string
  phoneNumber?: string
  email?: string
  website?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export default function InstitutionPage() {
  const [institution, setInstitution] = useState<Institution | null>(null)
  const [schools, setSchools] = useState<School[]>([])
  const [loading, setLoading] = useState(true)
  const { currentInstitution } = useTenant()
  const { user } = useAuth()
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      console.log("User not authenticated, redirecting to login")
      router.push('/auth/login')
      return
    }

    console.log("Institution page: currentInstitution changed:", currentInstitution)
    if (currentInstitution?.id) {
      fetchInstitutionData()
    } else {
      console.log("Institution page: No currentInstitution available")
      setLoading(false)
      // Don't use fallback - let the user see the "no institution" message
    }
  }, [currentInstitution, user, router])

  const fetchInstitutionData = async () => {
    if (!currentInstitution?.id) {
      console.log("No institution ID available")
      return
    }

    try {
      setLoading(true)
      console.log("Fetching institution data for ID:", currentInstitution.id)

      // Fetch institution details
      const institutionResponse = await fetch(`/api/institutions/${currentInstitution.id}`, {
        credentials: 'include',
      })

      console.log("Institution response status:", institutionResponse.status)

      if (institutionResponse.ok) {
        const institutionData = await institutionResponse.json()
        console.log("Institution data received:", institutionData)
        setInstitution(institutionData.data?.institution || institutionData.institution || institutionData)
      } else {
        console.error("Failed to fetch institution:", institutionResponse.status)
        toast({
          title: "Error",
          description: "Failed to load institution details. Please try again.",
          variant: "destructive",
        })
      }

      // Fetch schools under this institution
      const schoolsResponse = await fetch(`/api/schools?institutionId=${currentInstitution.id}`, {
        credentials: 'include',
      })

      console.log("Schools response status:", schoolsResponse.status)

      if (schoolsResponse.ok) {
        const schoolsData = await schoolsResponse.json()
        console.log("Schools data received:", schoolsData)
        setSchools(schoolsData.data?.schools || schoolsData.schools || [])
      } else {
        console.error("Failed to fetch schools:", schoolsResponse.status)
        toast({
          title: "Error",
          description: "Failed to load schools data. Please try again.",
          variant: "destructive",
        })
      }

    } catch (error) {
      console.error("Error fetching institution data:", error)
      toast({
        title: "Error",
        description: "Failed to load institution data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading institution data...</span>
        </div>
      </div>
    )
  }

  // Show message if user has no institution association
  if (!currentInstitution) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center max-w-md">
          <div className="mb-4">
            <svg className="h-16 w-16 mx-auto text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold mb-2">No Institution Access</h3>
          <p className="text-muted-foreground mb-4">
            You are not currently associated with any institution. Please contact your administrator to get access to an institution.
          </p>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>If you believe this is an error:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>Contact your institution administrator</li>
              <li>Verify your account has been properly set up</li>
              <li>Try logging out and logging back in</li>
            </ul>
          </div>
        </div>
      </div>
    )
  }

  if (!institution) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <p className="text-lg font-medium">Institution not found</p>
          <p className="text-muted-foreground">Unable to load institution data.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{institution.name}</h1>
          <p className="text-muted-foreground">View and manage your institution details, branches, and settings.</p>
        </div>
        <Link href="/dashboard/institution-profile">
          <Button>
            <Settings className="mr-2 h-4 w-4" />
            Edit Institution
          </Button>
        </Link>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="branches">Branches</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-2xl font-bold flex items-center">
                <Building className="mr-2 h-5 w-5 text-primary" />
                {institution.name}
              </CardTitle>
              <CardDescription>
                Established {new Date(institution.createdAt).getFullYear()} •
                {institution.subscriptionStatus === 'ACTIVE' ? 'Active' :
                 institution.subscriptionStatus === 'TRIAL' ? 'Trial' : 'Inactive'} Institution
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {(institution.address || institution.city || institution.country) && (
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <h3 className="font-medium">Address</h3>
                        <p className="text-sm text-muted-foreground">
                          {institution.address && (
                            <>
                              {institution.address}
                              <br />
                            </>
                          )}
                          {institution.city && institution.state && (
                            <>
                              {institution.city}, {institution.state} {institution.postalCode}
                              <br />
                            </>
                          )}
                          {institution.country}
                        </p>
                      </div>
                    </div>
                  )}

                  {institution.phoneNumber && (
                    <div className="flex items-start space-x-3">
                      <Phone className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <h3 className="font-medium">Contact</h3>
                        <p className="text-sm text-muted-foreground">{institution.phoneNumber}</p>
                      </div>
                    </div>
                  )}

                  {institution.email && (
                    <div className="flex items-start space-x-3">
                      <Mail className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <h3 className="font-medium">Email</h3>
                        <p className="text-sm text-muted-foreground">{institution.email}</p>
                      </div>
                    </div>
                  )}

                  {institution.website && (
                    <div className="flex items-start space-x-3">
                      <Globe className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <h3 className="font-medium">Website</h3>
                        <p className="text-sm text-muted-foreground">
                          <a href={institution.website} target="_blank" rel="noopener noreferrer" className="hover:underline">
                            {institution.website.replace(/^https?:\/\//, '')}
                          </a>
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <Users className="h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <h3 className="font-medium">Total Students</h3>
                      <p className="text-sm text-muted-foreground">
                        {institution.studentCount ? `${institution.studentCount.toLocaleString()} students` : 'Not specified'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Users className="h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <h3 className="font-medium">Total Teachers</h3>
                      <p className="text-sm text-muted-foreground">
                        {institution.teacherCount ? `${institution.teacherCount.toLocaleString()} teachers` : 'Not specified'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <BookOpen className="h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <h3 className="font-medium">Schools</h3>
                      <p className="text-sm text-muted-foreground">
                        {schools.length > 0 ? `${schools.length} school${schools.length > 1 ? 's' : ''}` : 'No schools yet'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <h3 className="font-medium">Status</h3>
                      <p className="text-sm text-muted-foreground">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          institution.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {institution.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {institution.specialRequirements && (
                <div className="pt-4 border-t">
                  <h3 className="font-medium mb-2">Special Requirements</h3>
                  <p className="text-sm text-muted-foreground">
                    {institution.specialRequirements}
                  </p>
                </div>
              )}

              {schools.length > 0 && (
                <div className="pt-4 border-t">
                  <h3 className="font-medium mb-3">Schools</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {schools.map((school) => (
                      <div key={school.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{school.name}</h4>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            school.type === 'PRIMARY' ? 'bg-blue-100 text-blue-800' :
                            school.type === 'SECONDARY' ? 'bg-green-100 text-green-800' :
                            school.type === 'TERTIARY' ? 'bg-purple-100 text-purple-800' :
                            school.type === 'KINDERGARTEN' ? 'bg-yellow-100 text-yellow-800' :
                            school.type === 'VOCATIONAL' ? 'bg-orange-100 text-orange-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {school.type.charAt(0) + school.type.slice(1).toLowerCase()}
                          </span>
                        </div>
                        {school.email && (
                          <p className="text-sm text-muted-foreground mb-1">
                            <Mail className="inline h-3 w-3 mr-1" />
                            {school.email}
                          </p>
                        )}
                        {school.phoneNumber && (
                          <p className="text-sm text-muted-foreground mb-1">
                            <Phone className="inline h-3 w-3 mr-1" />
                            {school.phoneNumber}
                          </p>
                        )}
                        {(school.city || school.address) && (
                          <p className="text-sm text-muted-foreground">
                            <MapPin className="inline h-3 w-3 mr-1" />
                            {school.address ? school.address : `${school.city}, ${school.country}`}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Link href="/dashboard/institution-profile" className="w-full">
                <Button variant="outline" className="w-full">
                  View Full Institution Profile
                </Button>
              </Link>
            </CardFooter>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Financial Overview</CardTitle>
                <CardDescription>Current fiscal year</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Total Revenue</p>
                    <p className="text-2xl font-bold">$4.2M</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-primary opacity-75" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Accreditation</CardTitle>
                <CardDescription>Status and renewals</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Status</p>
                    <p className="text-lg font-bold text-green-500">Active</p>
                    <p className="text-xs text-muted-foreground">Renewal: June 2025</p>
                  </div>
                  <FileText className="h-8 w-8 text-primary opacity-75" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Compliance</CardTitle>
                <CardDescription>Regulatory requirements</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Status</p>
                    <div className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                      <p className="font-medium">Compliant</p>
                    </div>
                    <p className="text-xs text-muted-foreground">Last audit: March 2023</p>
                  </div>
                  <FileText className="h-8 w-8 text-primary opacity-75" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="branches" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Institution Branches</CardTitle>
              <CardDescription>Manage all branches and campuses of your institution</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    name: "Main Campus",
                    address: "123 Education Street, Academic District, New York, NY 10001",
                    students: 850,
                    staff: 65,
                    established: 2005,
                  },
                  {
                    name: "North Campus",
                    address: "456 Learning Avenue, Knowledge Heights, New York, NY 10002",
                    students: 395,
                    staff: 22,
                    established: 2012,
                  },
                ].map((branch, index) => (
                  <Card key={index}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-xl">{branch.name}</CardTitle>
                      <CardDescription>{branch.address}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Students</p>
                          <p className="font-medium">{branch.students}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Staff</p>
                          <p className="font-medium">{branch.staff}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Established</p>
                          <p className="font-medium">{branch.established}</p>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">
                <Building className="mr-2 h-4 w-4" />
                Add New Branch
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Institution Documents</CardTitle>
              <CardDescription>Manage important documents and certificates</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    name: "Accreditation Certificate",
                    type: "PDF",
                    size: "2.4 MB",
                    uploaded: "Jan 15, 2023",
                    expires: "Jan 15, 2026",
                  },
                  {
                    name: "Tax Exemption Certificate",
                    type: "PDF",
                    size: "1.8 MB",
                    uploaded: "Mar 22, 2023",
                    expires: "Mar 22, 2025",
                  },
                  {
                    name: "Business License",
                    type: "PDF",
                    size: "1.2 MB",
                    uploaded: "Apr 10, 2023",
                    expires: "Apr 10, 2024",
                  },
                  {
                    name: "Insurance Policy",
                    type: "PDF",
                    size: "3.5 MB",
                    uploaded: "May 5, 2023",
                    expires: "May 5, 2024",
                  },
                ].map((doc, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center">
                      <FileText className="h-8 w-8 text-primary mr-4" />
                      <div>
                        <p className="font-medium">{doc.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {doc.type} • {doc.size} • Uploaded: {doc.uploaded}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-right mr-4">
                        <p className="text-sm font-medium">Expires</p>
                        <p className="text-sm text-muted-foreground">{doc.expires}</p>
                      </div>
                      <Button variant="outline" size="sm">
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">
                <FileText className="mr-2 h-4 w-4" />
                Upload New Document
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Institution Settings</CardTitle>
              <CardDescription>Manage your institution settings and preferences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-medium">General Settings</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Institution Name</label>
                    <input type="text" className="w-full p-2 border rounded-md" defaultValue="Horizon Academy" />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Institution Type</label>
                    <select className="w-full p-2 border rounded-md">
                      <option>Private</option>
                      <option>Public</option>
                      <option>Charter</option>
                      <option>International</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Academic Year Start</label>
                    <input type="date" className="w-full p-2 border rounded-md" defaultValue="2023-09-01" />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Academic Year End</label>
                    <input type="date" className="w-full p-2 border rounded-md" defaultValue="2024-06-30" />
                  </div>
                </div>
              </div>

              <div className="space-y-2 pt-4 border-t">
                <h3 className="font-medium">Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email Address</label>
                    <input
                      type="email"
                      className="w-full p-2 border rounded-md"
                      defaultValue="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Phone Number</label>
                    <input type="tel" className="w-full p-2 border rounded-md" defaultValue="+****************" />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Website</label>
                    <input
                      type="url"
                      className="w-full p-2 border rounded-md"
                      defaultValue="https://www.horizonacademy.edu"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Address</label>
                    <textarea
                      className="w-full p-2 border rounded-md"
                      rows={2}
                      defaultValue="123 Education Street, Academic District, New York, NY 10001, United States"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2 pt-4 border-t">
                <h3 className="font-medium">System Preferences</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Enable Online Admissions</p>
                      <p className="text-sm text-muted-foreground">Allow students to apply online</p>
                    </div>
                    <div className="h-6 w-11 bg-primary rounded-full relative">
                      <div className="h-5 w-5 bg-white rounded-full absolute top-0.5 right-0.5"></div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Enable Parent Portal</p>
                      <p className="text-sm text-muted-foreground">Allow parents to access student information</p>
                    </div>
                    <div className="h-6 w-11 bg-primary rounded-full relative">
                      <div className="h-5 w-5 bg-white rounded-full absolute top-0.5 right-0.5"></div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Enable SMS Notifications</p>
                      <p className="text-sm text-muted-foreground">Send SMS notifications for important updates</p>
                    </div>
                    <div className="h-6 w-11 bg-muted rounded-full relative">
                      <div className="h-5 w-5 bg-white rounded-full absolute top-0.5 left-0.5"></div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Cancel</Button>
              <Button>Save Changes</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
