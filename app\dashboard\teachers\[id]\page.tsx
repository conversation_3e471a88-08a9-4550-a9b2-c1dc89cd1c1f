'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  Edit,
  Mail,
  Phone,
  MapPin,
  Calendar,
  GraduationCap,
  Briefcase,
  Users,
  BookOpen,
  Clock,
  Loader2,
  User,
  Building,
  DollarSign
} from 'lucide-react'
import { toast } from 'sonner'
import { useAuth } from '@/contexts/auth-context'

// TypeScript interfaces
interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  phoneNumber?: string
  isActive: boolean
  role: string
  createdAt: string
  updatedAt: string
}

interface Assignment {
  id: string
  isClassTeacher: boolean
  startDate: string
  endDate?: string
  isActive: boolean
  subject?: {
    id: string
    name: string
    code: string
    description?: string
  }
  department?: {
    id: string
    name: string
    description?: string
  }
}

interface Teacher {
  id: string
  userId: string
  user: User
  employeeId?: string
  qualification?: string
  experience?: number
  specialization?: string
  salary?: number
  contractType?: string
  hireDate?: string
  assignments?: Assignment[]
  _count?: {
    assignments: number
    createdAssignments: number
  }
}

interface TeacherResponse {
  success: boolean
  data: Teacher
  message?: string
}

export default function TeacherProfilePage() {
  const params = useParams()
  const router = useRouter()
  const { user: currentUser, isLoading: authLoading, isAuthenticated } = useAuth()
  const teacherId = params.id as string

  const [teacher, setTeacher] = useState<Teacher | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editFormData, setEditFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    employeeId: '',
    qualification: '',
    experience: '',
    specialization: '',
    salary: '',
    contractType: '',
    hireDate: ''
  })

  // Fetch teacher data
  const fetchTeacher = async () => {
    try {
      setLoading(true)

      const response = await fetch(`/api/teachers/${teacherId}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Teacher not found')
          router.push('/dashboard/teachers')
          return
        }
        if (response.status === 401) {
          toast.error('Authentication required. Please log in again.')
          router.push('/auth/login')
          return
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: TeacherResponse = await response.json()

      if (data.success) {
        setTeacher(data.data)
        // Initialize edit form with current data
        setEditFormData({
          firstName: data.data.user.firstName,
          lastName: data.data.user.lastName,
          email: data.data.user.email,
          phoneNumber: data.data.user.phoneNumber || '',
          employeeId: data.data.employeeId || '',
          qualification: data.data.qualification || '',
          experience: data.data.experience?.toString() || '',
          specialization: data.data.specialization || '',
          salary: data.data.salary?.toString() || '',
          contractType: data.data.contractType || '',
          hireDate: data.data.hireDate ? data.data.hireDate.split('T')[0] : ''
        })
      } else {
        toast.error(data.message || 'Failed to fetch teacher data')
      }
    } catch (error) {
      console.error('Error fetching teacher:', error)
      toast.error('Failed to load teacher data')
    } finally {
      setLoading(false)
    }
  }

  // Check if current user can edit this teacher
  const canEdit = () => {
    if (!currentUser || !teacher) return false

    // INSTITUTION_ADMIN and SCHOOL_ADMIN can edit
    if (['INSTITUTION_ADMIN', 'SCHOOL_ADMIN'].includes(currentUser.role)) {
      return true
    }

    // Teacher can edit their own profile
    if (currentUser.role === 'TEACHER' && currentUser.id === teacher.user.id) {
      return true
    }

    return false
  }

  // Update teacher
  const handleUpdateTeacher = async () => {
    try {
      setUpdating(true)

      const updateData = {
        firstName: editFormData.firstName,
        lastName: editFormData.lastName,
        email: editFormData.email,
        phoneNumber: editFormData.phoneNumber || null,
        employeeId: editFormData.employeeId || null,
        qualification: editFormData.qualification || null,
        experience: editFormData.experience ? parseInt(editFormData.experience) : null,
        specialization: editFormData.specialization || null,
        salary: editFormData.salary ? parseFloat(editFormData.salary) : null,
        contractType: editFormData.contractType || null,
        hireDate: editFormData.hireDate || null
      }

      const response = await fetch(`/api/teachers/${teacherId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(updateData),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        toast.success('Teacher updated successfully')
        setIsEditDialogOpen(false)
        fetchTeacher() // Refresh data
      } else {
        toast.error(data.message || 'Failed to update teacher')
      }
    } catch (error) {
      console.error('Error updating teacher:', error)
      toast.error('Failed to update teacher')
    } finally {
      setUpdating(false)
    }
  }

  // Toggle teacher status
  const toggleStatus = async () => {
    if (!teacher) return

    try {
      setUpdating(true)

      const response = await fetch(`/api/teachers/${teacherId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          isActive: !teacher.user.isActive
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        toast.success(`Teacher ${teacher.user.isActive ? 'deactivated' : 'activated'} successfully`)
        fetchTeacher() // Refresh data
      } else {
        toast.error(data.message || 'Failed to update teacher status')
      }
    } catch (error) {
      console.error('Error updating teacher status:', error)
      toast.error('Failed to update teacher status')
    } finally {
      setUpdating(false)
    }
  }

  useEffect(() => {
    if (teacherId && currentUser && !authLoading) {
      fetchTeacher()
    } else if (teacherId && !currentUser && !authLoading) {
      toast.error('Authentication required. Please log in again.')
      router.push('/auth/login')
    }
  }, [teacherId, currentUser, authLoading])

  // Show loading while auth is loading or data is loading
  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">
          {authLoading ? 'Authenticating...' : 'Loading teacher data...'}
        </span>
      </div>
    )
  }

  // Check authentication after loading is complete
  if (!authLoading && !isAuthenticated) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <h2 className="text-2xl font-semibold">Authentication Required</h2>
        <p className="text-muted-foreground">Please log in to view this page.</p>
        <Button onClick={() => router.push('/auth/login')}>
          Go to Login
        </Button>
      </div>
    )
  }

  if (!teacher) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <h2 className="text-2xl font-semibold">Teacher Not Found</h2>
        <p className="text-muted-foreground">The teacher you're looking for doesn't exist.</p>
        <Button onClick={() => router.push('/dashboard/teachers')}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Teachers
        </Button>
      </div>
    )
  }

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString()
  }

  const formatCurrency = (amount?: number) => {
    if (!amount) return 'N/A'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/dashboard/teachers')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Teachers
          </Button>
          <h1 className="text-3xl font-bold">Teacher Profile</h1>
        </div>

        {canEdit() && (
          <div className="flex space-x-2">
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Profile
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Edit Teacher Profile</DialogTitle>
                </DialogHeader>
                <div className="grid grid-cols-2 gap-4 py-4">
                  <div>
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      value={editFormData.firstName}
                      onChange={(e) => setEditFormData({ ...editFormData, firstName: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      value={editFormData.lastName}
                      onChange={(e) => setEditFormData({ ...editFormData, lastName: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={editFormData.email}
                      onChange={(e) => setEditFormData({ ...editFormData, email: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="phoneNumber">Phone Number</Label>
                    <Input
                      id="phoneNumber"
                      value={editFormData.phoneNumber}
                      onChange={(e) => setEditFormData({ ...editFormData, phoneNumber: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="employeeId">Employee ID</Label>
                    <Input
                      id="employeeId"
                      value={editFormData.employeeId}
                      onChange={(e) => setEditFormData({ ...editFormData, employeeId: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="qualification">Qualification</Label>
                    <Input
                      id="qualification"
                      value={editFormData.qualification}
                      onChange={(e) => setEditFormData({ ...editFormData, qualification: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="experience">Experience (years)</Label>
                    <Input
                      id="experience"
                      type="number"
                      value={editFormData.experience}
                      onChange={(e) => setEditFormData({ ...editFormData, experience: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="specialization">Specialization</Label>
                    <Input
                      id="specialization"
                      value={editFormData.specialization}
                      onChange={(e) => setEditFormData({ ...editFormData, specialization: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="salary">Salary</Label>
                    <Input
                      id="salary"
                      type="number"
                      value={editFormData.salary}
                      onChange={(e) => setEditFormData({ ...editFormData, salary: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="contractType">Contract Type</Label>
                    <Select value={editFormData.contractType} onValueChange={(value) => setEditFormData({ ...editFormData, contractType: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select contract type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="FULL_TIME">Full Time</SelectItem>
                        <SelectItem value="PART_TIME">Part Time</SelectItem>
                        <SelectItem value="CONTRACT">Contract</SelectItem>
                        <SelectItem value="TEMPORARY">Temporary</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="hireDate">Hire Date</Label>
                    <Input
                      id="hireDate"
                      type="date"
                      value={editFormData.hireDate}
                      onChange={(e) => setEditFormData({ ...editFormData, hireDate: e.target.value })}
                    />
                  </div>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleUpdateTeacher} disabled={updating}>
                    {updating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      'Update Profile'
                    )}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            {['INSTITUTION_ADMIN', 'SCHOOL_ADMIN'].includes(currentUser?.role || '') && (
              <Button
                variant={teacher.user.isActive ? "destructive" : "default"}
                onClick={toggleStatus}
                disabled={updating}
              >
                {updating ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                {teacher.user.isActive ? 'Deactivate' : 'Activate'}
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Profile Header Card */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
            <Avatar className="h-24 w-24">
              <AvatarImage src="" alt={`${teacher.user.firstName} ${teacher.user.lastName}`} />
              <AvatarFallback className="text-lg">
                {getInitials(teacher.user.firstName, teacher.user.lastName)}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 space-y-2">
              <div className="flex items-center space-x-3">
                <h2 className="text-2xl font-bold">
                  {teacher.user.firstName} {teacher.user.lastName}
                </h2>
                <Badge variant={teacher.user.isActive ? "default" : "destructive"}>
                  {teacher.user.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>

              <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <Mail className="mr-1 h-4 w-4" />
                  <a href={`mailto:${teacher.user.email}`} className="hover:underline">
                    {teacher.user.email}
                  </a>
                </div>
                {teacher.user.phoneNumber && (
                  <div className="flex items-center">
                    <Phone className="mr-1 h-4 w-4" />
                    <a href={`tel:${teacher.user.phoneNumber}`} className="hover:underline">
                      {teacher.user.phoneNumber}
                    </a>
                  </div>
                )}
                {teacher.employeeId && (
                  <div className="flex items-center">
                    <Briefcase className="mr-1 h-4 w-4" />
                    ID: {teacher.employeeId}
                  </div>
                )}
              </div>

              <div className="flex flex-wrap gap-2">
                {teacher.specialization && (
                  <Badge variant="outline">{teacher.specialization}</Badge>
                )}
                {teacher.contractType && (
                  <Badge variant="outline">{teacher.contractType.replace('_', ' ')}</Badge>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="assignments">Teaching Assignments</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Full Name</p>
                    <p>{teacher.user.firstName} {teacher.user.lastName}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Email</p>
                    <p className="flex items-center">
                      <Mail className="mr-2 h-4 w-4" />
                      <a href={`mailto:${teacher.user.email}`} className="hover:underline">
                        {teacher.user.email}
                      </a>
                    </p>
                  </div>
                  {teacher.user.phoneNumber && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Phone</p>
                      <p className="flex items-center">
                        <Phone className="mr-2 h-4 w-4" />
                        <a href={`tel:${teacher.user.phoneNumber}`} className="hover:underline">
                          {teacher.user.phoneNumber}
                        </a>
                      </p>
                    </div>
                  )}
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Role</p>
                    <p className="flex items-center">
                      <User className="mr-2 h-4 w-4" />
                      {teacher.user.role}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                    <Badge variant={teacher.user.isActive ? "default" : "destructive"}>
                      {teacher.user.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Employment Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Briefcase className="mr-2 h-5 w-5" />
                  Employment Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  {teacher.employeeId && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Employee ID</p>
                      <p>{teacher.employeeId}</p>
                    </div>
                  )}
                  {teacher.qualification && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Qualification</p>
                      <p className="flex items-center">
                        <GraduationCap className="mr-2 h-4 w-4" />
                        {teacher.qualification}
                      </p>
                    </div>
                  )}
                  {teacher.specialization && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Specialization</p>
                      <p className="flex items-center">
                        <BookOpen className="mr-2 h-4 w-4" />
                        {teacher.specialization}
                      </p>
                    </div>
                  )}
                  {teacher.experience && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Experience</p>
                      <p className="flex items-center">
                        <Clock className="mr-2 h-4 w-4" />
                        {teacher.experience} years
                      </p>
                    </div>
                  )}
                  {teacher.contractType && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Contract Type</p>
                      <p>{teacher.contractType.replace('_', ' ')}</p>
                    </div>
                  )}
                  {teacher.hireDate && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Hire Date</p>
                      <p className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4" />
                        {formatDate(teacher.hireDate)}
                      </p>
                    </div>
                  )}
                  {teacher.salary && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Salary</p>
                      <p className="flex items-center">
                        <DollarSign className="mr-2 h-4 w-4" />
                        {formatCurrency(teacher.salary)}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="assignments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="mr-2 h-5 w-5" />
                Teaching Assignments
              </CardTitle>
              <CardDescription>
                Current classes and subjects assigned to this teacher
              </CardDescription>
            </CardHeader>
            <CardContent>
              {teacher.assignments && teacher.assignments.length > 0 ? (
                <div className="space-y-4">
                  {teacher.assignments.map((assignment) => (
                    <div key={assignment.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <h4 className="font-medium">
                          {assignment.subject?.name || 'General Assignment'}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {assignment.subject?.code && `Code: ${assignment.subject.code}`}
                          {assignment.department?.name && ` | Department: ${assignment.department.name}`}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {assignment.isClassTeacher && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 mr-2">
                              Class Teacher
                            </span>
                          )}
                          Start Date: {new Date(assignment.startDate).toLocaleDateString()}
                          {assignment.endDate && ` | End Date: ${new Date(assignment.endDate).toLocaleDateString()}`}
                        </p>
                      </div>
                      <Badge variant={assignment.isActive ? "default" : "secondary"}>
                        {assignment.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <BookOpen className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-sm font-medium">No assignments</h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    This teacher has no current teaching assignments.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Total Assignments</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {teacher._count?.assignments || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Current teaching assignments
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Created Assignments</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {teacher._count?.createdAssignments || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Assignments created by teacher
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Experience</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {teacher.experience || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Years of experience
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
