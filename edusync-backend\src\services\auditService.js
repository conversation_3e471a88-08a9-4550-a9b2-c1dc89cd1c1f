const { PrismaClient } = require('@prisma/client');
const { logger } = require('../utils/logger');

const prisma = new PrismaClient();

/**
 * Create an audit log entry
 * @param {Object} data - Audit log data
 */
const createAuditLog = async (data) => {
  try {
    await prisma.auditLog.create({
      data,
    });
  } catch (error) {
    logger.error('Failed to create audit log:', error);
    // Don't throw error as audit logging should not affect the main application flow
  }
};

/**
 * Log an audit event
 * @param {Object} data - Audit log data
 */
const logAudit = async (data) => {
  await createAuditLog(data);
};

module.exports = {
  createAuditLog,
  logAudit
};
